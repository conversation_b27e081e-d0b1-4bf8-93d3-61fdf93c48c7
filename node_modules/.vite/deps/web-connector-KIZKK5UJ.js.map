{"version": 3, "sources": ["../../thirdweb/src/utils/storage/inMemoryStorage.ts", "../../thirdweb/src/wallets/in-app/core/actions/get-enclave-user-status.ts", "../../thirdweb/src/wallets/in-app/native/helpers/constants.ts", "../../thirdweb/src/wallets/in-app/native/helpers/errors.ts", "../../thirdweb/src/wallets/in-app/core/authentication/authEndpoint.ts", "../../thirdweb/src/wallets/in-app/core/authentication/backend.ts", "../../thirdweb/src/wallets/in-app/core/authentication/guest.ts", "../../thirdweb/src/wallets/in-app/core/authentication/jwt.ts", "../../thirdweb/src/wallets/in-app/core/authentication/linkAccount.ts", "../../thirdweb/src/wallets/in-app/core/authentication/passkeys.ts", "../../thirdweb/src/wallets/in-app/core/authentication/siwe.ts", "../../thirdweb/src/wallets/in-app/core/actions/sign-authorization.enclave.ts", "../../thirdweb/src/wallets/in-app/core/actions/sign-message.enclave.ts", "../../thirdweb/src/wallets/in-app/core/actions/sign-transaction.enclave.ts", "../../thirdweb/src/wallets/in-app/core/actions/sign-typed-data.enclave.ts", "../../thirdweb/src/wallets/in-app/core/wallet/enclave-wallet.ts", "../../thirdweb/src/wallets/in-app/web/utils/iFrameCommunication/IframeCommunicator.ts", "../../thirdweb/src/wallets/in-app/web/utils/iFrameCommunication/InAppWalletIframeCommunicator.ts", "../../thirdweb/src/wallets/in-app/core/actions/generate-wallet.enclave.ts", "../../thirdweb/src/wallets/in-app/web/lib/auth/abstract-login.ts", "../../thirdweb/src/wallets/in-app/web/lib/auth/base-login.ts", "../../thirdweb/src/wallets/in-app/web/lib/auth/iframe-auth.ts", "../../thirdweb/src/wallets/in-app/web/lib/auth/otp.ts", "../../thirdweb/src/wallets/in-app/web/lib/iframe-wallet.ts", "../../thirdweb/src/wallets/in-app/web/lib/web-connector.ts"], "sourcesContent": ["import type { AsyncStorage } from \"./AsyncStorage.js\";\n\nconst store = new Map<string, string>();\n\nexport const inMemoryStorage: AsyncStorage = {\n  getItem: async (key: string) => {\n    return store.get(key) ?? null;\n  },\n  setItem: async (key: string, value: string) => {\n    store.set(key, value);\n  },\n  removeItem: async (key: string) => {\n    store.delete(key);\n  },\n};\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport type { UserStatus } from \"../wallet/enclave-wallet.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\n\n/**\n * Gets the user's status from the backend.\n *\n * @internal\n */\nexport async function getUserStatus({\n  authToken,\n  client,\n  ecosystem,\n}: {\n  authToken: string;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n}): Promise<UserStatus> {\n  const clientFetch = getClientFetch(client, ecosystem);\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/2024-05-05/accounts`,\n    {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n    },\n  );\n\n  if (!response.ok) {\n    const result = await response.text().catch(() => {\n      return \"Unknown error\";\n    });\n    throw new Error(`Failed to get user info: ${result}`);\n  }\n\n  return (await response.json()) as UserStatus;\n}\n", "import { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\n\nconst AUTH_SHARE_ID = 3;\nexport const AUTH_SHARE_INDEX = AUTH_SHARE_ID - 1;\n\nconst DEVICE_SHARE_ID = 1;\nexport const DEVICE_SHARE_INDEX = DEVICE_SHARE_ID - 1;\nexport const DEVICE_SHARE_MISSING_MESSAGE = \"Missing device share.\";\nexport const INVALID_DEVICE_SHARE_MESSAGE =\n  \"Invalid private key reconstructed from shares\";\n\nconst RECOVERY_SHARE_ID = 2;\nexport const RECOVERY_SHARE_INDEX = RECOVERY_SHARE_ID - 1;\n\nexport const AWS_REGION = \"us-west-2\";\n\nexport const THIRDWEB_SESSION_NONCE_HEADER = \"x-session-nonce\";\nconst COGNITO_USER_POOL_ID = \"us-west-2_UFwLcZIpq\";\nexport const COGNITO_IDENTITY_POOL_ID =\n  \"us-west-2:2ad7ab1e-f48b-48a6-adfa-ac1090689c26\";\nexport const GENERATE_RECOVERY_PASSWORD_LAMBDA_FUNCTION_V1 =\n  \"arn:aws:lambda:us-west-2:324457261097:function:recovery-share-password-GenerateRecoverySharePassw-bbE5ZbVAToil\";\nexport const GENERATE_RECOVERY_PASSWORD_LAMBDA_FUNCTION_V2 =\n  \"arn:aws:lambda:us-west-2:324457261097:function:lambda-thirdweb-auth-enc-key-prod-ThirdwebAuthEncKeyFunction\";\nexport const ENCLAVE_KMS_KEY_ARN =\n  \"arn:aws:kms:us-west-2:324457261097:key/ccfb9ecd-f45d-4f37-864a-25fe72dcb49e\";\n\n// TODO allow overriding domain\nconst DOMAIN_URL_2023 = getThirdwebBaseUrl(\"inAppWallet\");\nconst BASE_URL_2023 = `${DOMAIN_URL_2023}/`;\nconst ROUTE_2023_10_20_API_BASE_PATH = `${BASE_URL_2023}api/2023-10-20`;\nconst ROUTE_2024_05_05_API_BASE_PATH = `${BASE_URL_2023}api/2024-05-05`;\n\nexport const ROUTE_EMBEDDED_WALLET_DETAILS = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/embedded-wallet-user-details`;\nexport const ROUTE_COGNITO_IDENTITY_POOL_URL = `cognito-idp.${AWS_REGION}.amazonaws.com/${COGNITO_USER_POOL_ID}`;\n\nexport const ROUTE_STORE_USER_SHARES = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/embedded-wallet-shares`;\nexport const ROUTE_GET_USER_SHARES = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/embedded-wallet-shares`;\nexport const ROUTE_VERIFY_THIRDWEB_CLIENT_ID = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/verify-thirdweb-client-id`;\nexport const ROUTE_AUTH_JWT_CALLBACK = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/validate-custom-jwt`;\nexport const ROUTE_AUTH_ENDPOINT_CALLBACK = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/validate-custom-auth-endpoint`;\n\nexport const ROUTE_AUTH_COGNITO_ID_TOKEN_V1 = `${ROUTE_2023_10_20_API_BASE_PATH}/embedded-wallet/cognito-id-token`;\nexport const ROUTE_AUTH_COGNITO_ID_TOKEN_V2 = `${ROUTE_2024_05_05_API_BASE_PATH}/login/web-token-exchange`;\n", "import { stringify } from \"../../../../utils/json.js\";\n\nexport const ErrorMessages = {\n  invalidOtp:\n    \"Your OTP code is invalid or expired. Please request a new code or try again.\",\n  missingRecoveryCode: \"Missing encryption key for user\",\n};\n\nexport const createErrorMessage = (message: string, error: unknown): string => {\n  if (error instanceof Error) {\n    return `${message}: ${error.message}`;\n  }\n  return `${message}: ${stringify(error)}`;\n};\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport { ROUTE_AUTH_ENDPOINT_CALLBACK } from \"../../native/helpers/constants.js\";\nimport { createErrorMessage } from \"../../native/helpers/errors.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\nexport async function authEndpoint(args: {\n  payload: string;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  const clientFetch = getClientFetch(args.client, args.ecosystem);\n\n  const res = await clientFetch(ROUTE_AUTH_ENDPOINT_CALLBACK, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: stringify({\n      payload: args.payload,\n      developerClientId: args.client.clientId,\n    }),\n  });\n\n  if (!res.ok) {\n    const error = await res.json();\n    throw new Error(\n      `Custom auth endpoint authentication error: ${error.message}`,\n    );\n  }\n\n  try {\n    const { verifiedToken } = await res.json();\n\n    return { storedToken: verifiedToken };\n  } catch (e) {\n    throw new Error(\n      createErrorMessage(\n        \"Malformed response from post auth_endpoint authentication\",\n        e,\n      ),\n    );\n  }\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport { getLoginUrl } from \"./getLoginPath.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\n/**\n * Authenticates via the wallet secret\n * @internal\n */\nexport async function backendAuthenticate(args: {\n  client: ThirdwebClient;\n  walletSecret: string;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  const clientFetch = getClientFetch(args.client, args.ecosystem);\n  const path = getLoginUrl({\n    authOption: \"backend\",\n    client: args.client,\n    ecosystem: args.ecosystem,\n  });\n  const res = await clientFetch(`${path}`, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: stringify({\n      walletSecret: args.walletSecret,\n    }),\n  });\n\n  if (!res.ok) {\n    throw new Error(\"Failed to generate backend account\");\n  }\n\n  return (await res.json()) satisfies AuthStoredTokenWithCookieReturnType;\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport { randomBytesHex } from \"../../../../utils/random.js\";\nimport type { AsyncStorage } from \"../../../../utils/storage/AsyncStorage.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport { ClientScopedStorage } from \"./client-scoped-storage.js\";\nimport { getLoginCallbackUrl } from \"./getLoginPath.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\n/**\n * Does no real authentication, just issues a temporary token for the user.\n * @internal\n */\nexport async function guestAuthenticate(args: {\n  client: ThirdwebClient;\n  storage: AsyncStorage;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  const storage = new ClientScopedStorage({\n    storage: args.storage,\n    clientId: args.client.clientId,\n    ecosystem: args.ecosystem,\n  });\n\n  let sessionId = await storage.getGuestSessionId();\n  if (!sessionId) {\n    sessionId = randomBytesHex(32);\n    storage.saveGuestSessionId(sessionId);\n  }\n\n  const clientFetch = getClientFetch(args.client, args.ecosystem);\n  const path = getLoginCallbackUrl({\n    authOption: \"guest\",\n    client: args.client,\n    ecosystem: args.ecosystem,\n  });\n  const res = await clientFetch(`${path}`, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: stringify({\n      sessionId,\n    }),\n  });\n\n  if (!res.ok) throw new Error(\"Failed to generate guest account\");\n\n  return (await res.json()) satisfies AuthStoredTokenWithCookieReturnType;\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport { ROUTE_AUTH_JWT_CALLBACK } from \"../../native/helpers/constants.js\";\nimport { createErrorMessage } from \"../../native/helpers/errors.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\nexport async function customJwt(args: {\n  jwt: string;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  const clientFetch = getClientFetch(args.client, args.ecosystem);\n\n  const res = await clientFetch(ROUTE_AUTH_JWT_CALLBACK, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: stringify({\n      jwt: args.jwt,\n      developerClientId: args.client.clientId,\n    }),\n  });\n\n  if (!res.ok) {\n    const error = await res.json();\n    throw new Error(`JWT authentication error: ${error.message}`);\n  }\n\n  try {\n    const { verifiedToken } = await res.json();\n\n    return { storedToken: verifiedToken };\n  } catch (e) {\n    throw new Error(\n      createErrorMessage(\"Malformed response from post jwt authentication\", e),\n    );\n  }\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport type { ClientScopedStorage } from \"./client-scoped-storage.js\";\nimport type { Profile } from \"./types.js\";\n\n/**\n * Links a new account to the current one using an auth token.\n * For the public-facing API, use `wallet.linkProfile` instead.\n *\n * @internal\n */\nexport async function linkAccount({\n  client,\n  ecosystem,\n  tokenToLink,\n  storage,\n}: {\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n  tokenToLink: string;\n  storage: ClientScopedStorage;\n}): Promise<Profile[]> {\n  const clientFetch = getClientFetch(client, ecosystem);\n  const IN_APP_URL = getThirdwebBaseUrl(\"inAppWallet\");\n  const currentAccountToken = await storage.getAuthCookie();\n\n  if (!currentAccountToken) {\n    throw new Error(\"Failed to link account, no user logged in\");\n  }\n\n  const headers: Record<string, string> = {\n    Authorization: `Bearer iaw-auth-token:${currentAccountToken}`,\n    \"Content-Type\": \"application/json\",\n  };\n  const linkedDetailsResp = await clientFetch(\n    `${IN_APP_URL}/api/2024-05-05/account/connect`,\n    {\n      method: \"POST\",\n      headers,\n      body: stringify({\n        accountAuthTokenToConnect: tokenToLink,\n      }),\n    },\n  );\n\n  if (!linkedDetailsResp.ok) {\n    const body = await linkedDetailsResp.json();\n    throw new Error(body.message || \"Failed to link account.\");\n  }\n\n  const { linkedAccounts } = await linkedDetailsResp.json();\n\n  return (linkedAccounts ?? []) satisfies Profile[];\n}\n\n/**\n * Links a new account to the current one using an auth token.\n * For the public-facing API, use `wallet.linkProfile` instead.\n *\n * @internal\n */\nexport async function unlinkAccount({\n  client,\n  ecosystem,\n  profileToUnlink,\n  storage,\n}: {\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n  profileToUnlink: Profile;\n  storage: ClientScopedStorage;\n}): Promise<Profile[]> {\n  const clientFetch = getClientFetch(client, ecosystem);\n  const IN_APP_URL = getThirdwebBaseUrl(\"inAppWallet\");\n  const currentAccountToken = await storage.getAuthCookie();\n\n  if (!currentAccountToken) {\n    throw new Error(\"Failed to unlink account, no user logged in\");\n  }\n\n  const headers: Record<string, string> = {\n    Authorization: `Bearer iaw-auth-token:${currentAccountToken}`,\n    \"Content-Type\": \"application/json\",\n  };\n  const linkedDetailsResp = await clientFetch(\n    `${IN_APP_URL}/api/2024-05-05/account/disconnect`,\n    {\n      method: \"POST\",\n      headers,\n      body: stringify(profileToUnlink),\n    },\n  );\n\n  if (!linkedDetailsResp.ok) {\n    const body = await linkedDetailsResp.json();\n    throw new Error(body.message || \"Failed to unlink account.\");\n  }\n\n  const { linkedAccounts } = await linkedDetailsResp.json();\n\n  return (linkedAccounts ?? []) satisfies Profile[];\n}\n\n/**\n * Gets the linked accounts for the current user.\n * For the public-facing API, use `wallet.getProfiles` instead.\n *\n * @internal\n */\nexport async function getLinkedProfilesInternal({\n  client,\n  ecosystem,\n  storage,\n}: {\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n  storage: ClientScopedStorage;\n}): Promise<Profile[]> {\n  const clientFetch = getClientFetch(client, ecosystem);\n  const IN_APP_URL = getThirdwebBaseUrl(\"inAppWallet\");\n  const currentAccountToken = await storage.getAuthCookie();\n  if (!currentAccountToken) {\n    throw new Error(\"Failed to get linked accounts, no user logged in\");\n  }\n\n  const headers: Record<string, string> = {\n    Authorization: `Bearer iaw-auth-token:${currentAccountToken}`,\n    \"Content-Type\": \"application/json\",\n  };\n\n  const linkedAccountsResp = await clientFetch(\n    `${IN_APP_URL}/api/2024-05-05/accounts`,\n    {\n      method: \"GET\",\n      headers,\n    },\n  );\n\n  if (!linkedAccountsResp.ok) {\n    const body = await linkedAccountsResp.json();\n    throw new Error(body.message || \"Failed to get linked accounts.\");\n  }\n\n  const { linkedAccounts } = await linkedAccountsResp.json();\n\n  return (linkedAccounts ?? []) satisfies Profile[];\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport type { ClientScopedStorage } from \"./client-scoped-storage.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\nfunction getVerificationPath() {\n  return `${getThirdwebBaseUrl(\n    \"inAppWallet\",\n  )}/api/2024-05-05/login/passkey/callback`;\n}\nfunction getChallengePath(type: \"sign-in\" | \"sign-up\", username?: string) {\n  return `${getThirdwebBaseUrl(\n    \"inAppWallet\",\n  )}/api/2024-05-05/login/passkey?type=${type}${\n    username ? `&username=${username}` : \"\"\n  }`;\n}\n\nexport type RegisterResult = {\n  authenticatorData: string;\n  credentialId: string;\n  clientData: string;\n  credential: {\n    publicKey: string;\n    algorithm: string;\n  };\n  origin: string;\n};\n\nexport type AuthenticateResult = {\n  credentialId: string;\n  authenticatorData: string;\n  clientData: string;\n  signature: string;\n  origin: string;\n};\n\nexport type RpInfo = { name: string; id: string };\n\nexport interface PasskeyClient {\n  register: (args: {\n    name: string;\n    challenge: string;\n    rp: RpInfo;\n  }) => Promise<RegisterResult>;\n  authenticate: (args: {\n    credentialId: string | undefined;\n    challenge: string;\n    rp: RpInfo;\n  }) => Promise<AuthenticateResult>;\n  isAvailable: () => boolean;\n}\n\nexport async function registerPasskey(options: {\n  client: ThirdwebClient;\n  passkeyClient: PasskeyClient;\n  storage?: ClientScopedStorage;\n  ecosystem?: Ecosystem;\n  username?: string;\n  rp: RpInfo;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  if (!options.passkeyClient.isAvailable()) {\n    throw new Error(\"Passkeys are not available on this device\");\n  }\n  const fetchWithId = getClientFetch(options.client, options.ecosystem);\n  const generatedName = options.username ?? generateUsername(options.ecosystem);\n  // 1. request challenge from  server\n  const res = await fetchWithId(getChallengePath(\"sign-up\", generatedName));\n  const challengeData = await res.json();\n  if (!challengeData.challenge) {\n    throw new Error(\"No challenge received\");\n  }\n  const challenge = challengeData.challenge;\n\n  // 2. initiate registration\n  const registration = await options.passkeyClient.register({\n    name: generatedName,\n    challenge,\n    rp: options.rp,\n  });\n\n  const customHeaders: Record<string, string> = {};\n  if (options.ecosystem?.partnerId) {\n    customHeaders[\"x-ecosystem-partner-id\"] = options.ecosystem.partnerId;\n  }\n  if (options.ecosystem?.id) {\n    customHeaders[\"x-ecosystem-id\"] = options.ecosystem.id;\n  }\n\n  // 3. send the registration object to the server\n  const verifRes = await fetchWithId(getVerificationPath(), {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...customHeaders,\n    },\n    body: stringify({\n      type: \"sign-up\",\n      authenticatorData: registration.authenticatorData,\n      credentialId: registration.credentialId,\n      serverVerificationId: challengeData.serverVerificationId,\n      clientData: registration.clientData,\n      username: generatedName,\n      credential: {\n        publicKey: registration.credential.publicKey,\n        algorithm: registration.credential.algorithm,\n      },\n      origin: registration.origin,\n      rpId: options.rp.id,\n    }),\n  });\n  const verifData = await verifRes.json();\n\n  if (!verifData || !verifData.storedToken) {\n    throw new Error(\n      `Error verifying passkey: ${verifData.message ?? \"unknown error\"}`,\n    );\n  }\n  // 4. store the credentialId in local storage\n  await options.storage?.savePasskeyCredentialId(registration.credentialId);\n\n  // 5. returns back the IAW authentication token\n  return verifData;\n}\n\nexport async function loginWithPasskey(options: {\n  client: ThirdwebClient;\n  passkeyClient: PasskeyClient;\n  rp: RpInfo;\n  storage?: ClientScopedStorage;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  if (!options.passkeyClient.isAvailable()) {\n    throw new Error(\"Passkeys are not available on this device\");\n  }\n  const fetchWithId = getClientFetch(options.client, options.ecosystem);\n  // 1. request challenge from  server/iframe\n  const [challengeData, credentialId] = await Promise.all([\n    fetchWithId(getChallengePath(\"sign-in\")).then((r) => r.json()),\n    options.storage?.getPasskeyCredentialId(),\n  ]);\n  if (!challengeData.challenge) {\n    throw new Error(\"No challenge received\");\n  }\n  const challenge = challengeData.challenge;\n  // 2. initiate login\n  const authentication = await options.passkeyClient.authenticate({\n    credentialId: credentialId ?? undefined,\n    challenge,\n    rp: options.rp,\n  });\n\n  const customHeaders: Record<string, string> = {};\n  if (options.ecosystem?.partnerId) {\n    customHeaders[\"x-ecosystem-partner-id\"] = options.ecosystem.partnerId;\n  }\n  if (options.ecosystem?.id) {\n    customHeaders[\"x-ecosystem-id\"] = options.ecosystem.id;\n  }\n\n  const verifRes = await fetchWithId(getVerificationPath(), {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...customHeaders,\n    },\n    body: stringify({\n      type: \"sign-in\",\n      authenticatorData: authentication.authenticatorData,\n      credentialId: authentication.credentialId,\n      serverVerificationId: challengeData.serverVerificationId,\n      clientData: authentication.clientData,\n      signature: authentication.signature,\n      origin: authentication.origin,\n      rpId: options.rp.id,\n    }),\n  });\n\n  const verifData = await verifRes.json();\n\n  if (!verifData || !verifData.storedToken) {\n    throw new Error(\n      `Error verifying passkey: ${verifData.message ?? \"unknown error\"}`,\n    );\n  }\n\n  // 5. store the credentialId in local storage\n  await options.storage?.savePasskeyCredentialId(authentication.credentialId);\n\n  // 6. return the auth'd user type\n  return verifData;\n}\n\nfunction generateUsername(ecosystem?: Ecosystem) {\n  return `${ecosystem?.id ?? \"wallet\"}-${new Date().toISOString()}`;\n}\n", "import { signLoginPayload } from \"../../../../auth/core/sign-login-payload.js\";\nimport type { LoginPayload } from \"../../../../auth/core/types.js\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Wallet } from \"../../../interfaces/wallet.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\nimport { getLoginCallbackUrl, getLoginUrl } from \"./getLoginPath.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"./types.js\";\n\n/**\n * @internal\n */\nexport async function siweAuthenticate(args: {\n  wallet: Wallet;\n  chain: Chain;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n}): Promise<AuthStoredTokenWithCookieReturnType> {\n  const { wallet, chain, client, ecosystem } = args;\n  // only connect if the wallet doesn't already have an account\n  const account =\n    wallet.getAccount() || (await wallet.connect({ client, chain }));\n  const clientFetch = getClientFetch(client, ecosystem);\n\n  const payload = await (async () => {\n    const path = getLoginUrl({\n      authOption: \"wallet\",\n      client: args.client,\n      ecosystem: args.ecosystem,\n    });\n    const res = await clientFetch(\n      `${path}&address=${account.address}&chainId=${chain.id}`,\n    );\n\n    if (!res.ok) throw new Error(\"Failed to generate SIWE login payload\");\n\n    return (await res.json()) satisfies LoginPayload;\n  })();\n  const { signature } = await signLoginPayload({ payload, account });\n\n  const authResult = await (async () => {\n    const path = getLoginCallbackUrl({\n      authOption: \"wallet\",\n      client: args.client,\n      ecosystem: args.ecosystem,\n    });\n    const res = await clientFetch(\n      `${path}&signature=${signature}&payload=${encodeURIComponent(payload)}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: stringify({\n          signature,\n          payload,\n        }),\n      },\n    );\n\n    if (!res.ok) throw new Error(\"Failed to verify SIWE signature\");\n\n    return (await res.json()) satisfies AuthStoredTokenWithCookieReturnType;\n  })();\n  return authResult;\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { AuthorizationRequest } from \"../../../../transaction/actions/eip7702/authorization.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { ClientScopedStorage } from \"../authentication/client-scoped-storage.js\";\n\nexport async function signAuthorization({\n  client,\n  payload,\n  storage,\n}: {\n  client: ThirdwebClient;\n  payload: AuthorizationRequest;\n  storage: ClientScopedStorage;\n}) {\n  const authToken = await storage.getAuthCookie();\n  const ecosystem = storage.ecosystem;\n  const clientFetch = getClientFetch(client, ecosystem);\n\n  if (!authToken) {\n    throw new Error(\"No auth token found when signing message\");\n  }\n\n  const body = {\n    address: payload.address,\n    chainId: payload.chainId,\n    nonce: Number(payload.nonce),\n  };\n\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/v1/enclave-wallet/sign-authorization`,\n    {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n      body: stringify(body),\n    },\n  );\n\n  if (!response.ok) {\n    throw new Error(\n      `Failed to sign message - ${response.status} ${response.statusText}`,\n    );\n  }\n\n  const signedAuthorization = (await response.json()) as {\n    r: string;\n    s: string;\n    yParity: string;\n    nonce: string;\n    address: string;\n    chainId: string;\n  };\n  return signedAuthorization;\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { ClientScopedStorage } from \"../authentication/client-scoped-storage.js\";\n\nexport async function signMessage({\n  client,\n  payload: { message, isRaw, originalMessage, chainId },\n  storage,\n}: {\n  client: ThirdwebClient;\n  payload: {\n    message: string;\n    isRaw: boolean;\n    originalMessage?: string;\n    chainId?: number;\n  };\n  storage: ClientScopedStorage;\n}) {\n  const authToken = await storage.getAuthCookie();\n  const ecosystem = storage.ecosystem;\n  const clientFetch = getClientFetch(client, ecosystem);\n\n  if (!authToken) {\n    throw new Error(\"No auth token found when signing message\");\n  }\n\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/v1/enclave-wallet/sign-message`,\n    {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n      body: stringify({\n        messagePayload: {\n          message,\n          isRaw,\n          originalMessage,\n          chainId,\n        },\n      }),\n    },\n  );\n\n  if (!response.ok) {\n    throw new Error(\n      `Failed to sign message - ${response.status} ${response.statusText}`,\n    );\n  }\n\n  const signedMessage = (await response.json()) as {\n    r: string;\n    s: string;\n    v: number;\n    signature: string;\n    hash: string;\n  };\n  return signedMessage;\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { ClientScopedStorage } from \"../authentication/client-scoped-storage.js\";\n\nexport async function signTransaction({\n  client,\n  payload,\n  storage,\n}: {\n  client: ThirdwebClient;\n  payload: Record<string, unknown>;\n  storage: ClientScopedStorage;\n}) {\n  const authToken = await storage.getAuthCookie();\n  const ecosystem = storage.ecosystem;\n  const clientFetch = getClientFetch(client, ecosystem);\n\n  if (!authToken) {\n    throw new Error(\"No auth token found when signing transaction\");\n  }\n\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/v1/enclave-wallet/sign-transaction`,\n    {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n      body: stringify({\n        transactionPayload: payload,\n      }),\n    },\n  );\n\n  if (!response.ok) {\n    throw new Error(\n      `Failed to sign transaction - ${response.status} ${response.statusText}`,\n    );\n  }\n\n  const signedTransaction = (await response.json()) as {\n    r: string;\n    s: string;\n    v: number;\n    signature: string;\n    hash: string;\n  };\n  return signedTransaction.signature as Hex;\n}\n", "import type * as ox__TypedData from \"ox/TypedData\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { ClientScopedStorage } from \"../authentication/client-scoped-storage.js\";\n\nexport async function signTypedData<\n  const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n>({\n  client,\n  payload,\n  storage,\n}: {\n  client: ThirdwebClient;\n  payload: ox__TypedData.Definition<typedData, primaryType>;\n  storage: ClientScopedStorage;\n}) {\n  const authToken = await storage.getAuthCookie();\n  const ecosystem = storage.ecosystem;\n  const clientFetch = getClientFetch(client, ecosystem);\n\n  if (!authToken) {\n    throw new Error(\"No auth token found when signing typed data\");\n  }\n\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/v1/enclave-wallet/sign-typed-data`,\n    {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n      body: stringify({\n        ...payload,\n      }),\n    },\n  );\n\n  if (!response.ok) {\n    throw new Error(\n      `Failed to sign typed data - ${response.status} ${response.statusText}`,\n    );\n  }\n\n  const signedTypedData = (await response.json()) as {\n    r: string;\n    s: string;\n    v: number;\n    signature: string;\n    hash: string;\n  };\n  return signedTypedData;\n}\n", "import { bytesToHex } from \"viem\";\nimport { trackTransaction } from \"../../../../analytics/track/transaction.js\";\nimport { getCached<PERSON><PERSON><PERSON> } from \"../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { eth_sendRawTransaction } from \"../../../../rpc/actions/eth_sendRawTransaction.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\nimport { getAddress } from \"../../../../utils/address.js\";\nimport { type Hex, isHex, toHex } from \"../../../../utils/encoding/hex.js\";\nimport { parseTypedData } from \"../../../../utils/signatures/helpers/parse-typed-data.js\";\nimport type { Prettify } from \"../../../../utils/type-utils.js\";\nimport type {\n  Account,\n  SendTransactionOption,\n} from \"../../../interfaces/wallet.js\";\nimport { getUserStatus } from \"../actions/get-enclave-user-status.js\";\nimport { signAuthorization as signEnclaveAuthorization } from \"../actions/sign-authorization.enclave.js\";\nimport { signMessage as signEnclaveMessage } from \"../actions/sign-message.enclave.js\";\nimport { signTransaction as signEnclaveTransaction } from \"../actions/sign-transaction.enclave.js\";\nimport { signTypedData as signEnclaveTypedData } from \"../actions/sign-typed-data.enclave.js\";\nimport type { ClientScopedStorage } from \"../authentication/client-scoped-storage.js\";\nimport type {\n  AuthDetails,\n  AuthResultAndRecoveryCode,\n  GetUser,\n} from \"../authentication/types.js\";\nimport type { Ecosystem } from \"./types.js\";\nimport type { IWebWallet } from \"./web-wallet.js\";\n\nexport type UserStatus = {\n  linkedAccounts: {\n    type: string;\n    details:\n      | { email: string; [key: string]: string }\n      | { phone: string; [key: string]: string }\n      | { address: string; [key: string]: string }\n      | { id: string; [key: string]: string };\n  }[];\n  wallets: UserWallet[];\n  id: string;\n};\n\nexport type UserWallet = {\n  address: string;\n  createdAt: string;\n  type: \"sharded\" | \"enclave\";\n};\n\nexport class EnclaveWallet implements IWebWallet {\n  private client: ThirdwebClient;\n  private ecosystem?: Ecosystem;\n  private address: string;\n  private localStorage: ClientScopedStorage;\n\n  constructor({\n    client,\n    ecosystem,\n    address,\n    storage,\n  }: Prettify<{\n    client: ThirdwebClient;\n    ecosystem?: Ecosystem;\n    address: string;\n    storage: ClientScopedStorage;\n  }>) {\n    this.client = client;\n    this.ecosystem = ecosystem;\n    this.address = address;\n    this.localStorage = storage;\n  }\n\n  /**\n   * Store the auth token for use\n   * @returns `{walletAddress: string }` The user's wallet details\n   * @internal\n   */\n  async postWalletSetUp(authResult: AuthResultAndRecoveryCode): Promise<void> {\n    await this.localStorage.saveAuthCookie(authResult.storedToken.cookieString);\n  }\n\n  /**\n   * Gets the current user's details\n   * @internal\n   */\n  async getUserWalletStatus(): Promise<GetUser> {\n    const token = await this.localStorage.getAuthCookie();\n    if (!token) {\n      return { status: \"Logged Out\" };\n    }\n\n    const userStatus = await getUserStatus({\n      authToken: token,\n      client: this.client,\n      ecosystem: this.ecosystem,\n    });\n\n    if (!userStatus) {\n      return { status: \"Logged Out\" };\n    }\n    const wallet = userStatus.wallets[0];\n\n    const authDetails: AuthDetails = {\n      email: userStatus.linkedAccounts.find(\n        (account) => account.details.email !== undefined,\n      )?.details.email,\n      phoneNumber: userStatus.linkedAccounts.find(\n        (account) => account.details.phone !== undefined,\n      )?.details.phone,\n      userWalletId: userStatus.id || \"\",\n      recoveryShareManagement: \"ENCLAVE\",\n    };\n\n    if (!wallet) {\n      return {\n        status: \"Logged In, Wallet Uninitialized\",\n        authDetails,\n      };\n    }\n\n    return {\n      status: \"Logged In, Wallet Initialized\",\n      walletAddress: wallet.address,\n      authDetails,\n      account: await this.getAccount(),\n    };\n  }\n\n  /**\n   * Returns an account to perform wallet operations\n   * @internal\n   */\n  async getAccount(): Promise<Account> {\n    const client = this.client;\n    const storage = this.localStorage;\n    const address = this.address;\n    const ecosystem = this.ecosystem;\n\n    const _signTransaction = async (tx: SendTransactionOption) => {\n      const rpcRequest = getRpcClient({\n        client,\n        chain: getCachedChain(tx.chainId),\n      });\n      const transaction: Record<string, unknown> = {\n        to: tx.to ? getAddress(tx.to) : undefined,\n        data: tx.data,\n        value: hexlify(tx.value),\n        gas: hexlify(tx.gas),\n        nonce:\n          hexlify(tx.nonce) ||\n          toHex(\n            await import(\n              \"../../../../rpc/actions/eth_getTransactionCount.js\"\n            ).then(({ eth_getTransactionCount }) =>\n              eth_getTransactionCount(rpcRequest, {\n                address: getAddress(this.address),\n                blockTag: \"pending\",\n              }),\n            ),\n          ),\n        chainId: toHex(tx.chainId),\n      };\n\n      if (tx.authorizationList && tx.authorizationList.length > 0) {\n        transaction.type = 4;\n        transaction.authorizationList = tx.authorizationList;\n        transaction.maxFeePerGas = hexlify(tx.maxFeePerGas);\n        transaction.maxPriorityFeePerGas = hexlify(tx.maxPriorityFeePerGas);\n      } else if (hexlify(tx.maxFeePerGas)) {\n        transaction.maxFeePerGas = hexlify(tx.maxFeePerGas);\n        transaction.maxPriorityFeePerGas = hexlify(tx.maxPriorityFeePerGas);\n        transaction.type = 2;\n      } else {\n        transaction.gasPrice = hexlify(tx.gasPrice);\n        transaction.type = 0;\n      }\n\n      return signEnclaveTransaction({\n        client,\n        storage,\n        payload: transaction,\n      });\n    };\n    return {\n      address: getAddress(address),\n      async signTransaction(tx) {\n        if (!tx.chainId) {\n          throw new Error(\"chainId required in tx to sign\");\n        }\n\n        return _signTransaction({\n          chainId: tx.chainId,\n          ...tx,\n        });\n      },\n      async sendTransaction(tx) {\n        const rpcRequest = getRpcClient({\n          client,\n          chain: getCachedChain(tx.chainId),\n        });\n        const signedTx = await _signTransaction(tx);\n\n        const transactionHash = await eth_sendRawTransaction(\n          rpcRequest,\n          signedTx,\n        );\n\n        trackTransaction({\n          client,\n          ecosystem,\n          chainId: tx.chainId,\n          walletAddress: address,\n          walletType: \"inApp\",\n          transactionHash,\n          contractAddress: tx.to ?? undefined,\n          gasPrice: tx.gasPrice,\n        });\n\n        return { transactionHash };\n      },\n      async signMessage({ message, originalMessage, chainId }) {\n        const messagePayload = (() => {\n          if (typeof message === \"string\") {\n            return { message, isRaw: false, originalMessage, chainId };\n          }\n          return {\n            message:\n              typeof message.raw === \"string\"\n                ? message.raw\n                : bytesToHex(message.raw),\n            isRaw: true,\n            originalMessage,\n            chainId,\n          };\n        })();\n\n        const { signature } = await signEnclaveMessage({\n          client,\n          payload: messagePayload,\n          storage,\n        });\n        return signature as Hex;\n      },\n      async signTypedData(_typedData) {\n        const parsedTypedData = parseTypedData(_typedData);\n        const { signature } = await signEnclaveTypedData({\n          client,\n          payload: parsedTypedData,\n          storage,\n        });\n\n        return signature as Hex;\n      },\n      async signAuthorization(payload) {\n        const authorization = await signEnclaveAuthorization({\n          client,\n          payload,\n          storage,\n        });\n        return {\n          address: getAddress(authorization.address),\n          chainId: Number.parseInt(authorization.chainId),\n          nonce: BigInt(authorization.nonce),\n          r: BigInt(authorization.r),\n          s: BigInt(authorization.s),\n          yParity: Number.parseInt(authorization.yParity),\n        };\n      },\n    };\n  }\n}\n\nfunction hexlify(value: string | number | bigint | undefined) {\n  return value === undefined || isHex(value) ? value : toHex(value);\n}\n", "import { sleep } from \"../../../../../utils/sleep.js\";\nimport type { ClientScopedStorage } from \"../../../../../wallets/in-app/core/authentication/client-scoped-storage.js\";\nimport type { Ecosystem } from \"../../../../../wallets/in-app/core/wallet/types.js\";\n\ntype IFrameCommunicatorProps = {\n  link: string;\n  baseUrl: string;\n  iframeId: string;\n  container?: HTMLElement;\n  onIframeInitialize?: () => void;\n  localStorage: ClientScopedStorage;\n  clientId: string;\n  ecosystem?: Ecosystem;\n};\n\nconst iframeBaseStyle = {\n  height: \"100%\",\n  width: \"100%\",\n  border: \"none\",\n  backgroundColor: \"transparent\",\n  colorScheme: \"light\",\n  position: \"fixed\",\n  top: \"0px\",\n  right: \"0px\",\n  zIndex: \"2147483646\",\n  display: \"none\",\n  pointerEvents: \"all\",\n};\n\n// Global var to help track iframe state\nconst isIframeLoaded = new Map<string, boolean>();\n\n/**\n * @internal\n */\n// biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\nexport class IframeCommunicator<T extends { [key: string]: any }> {\n  private iframe?: HTMLIFrameElement;\n  private POLLING_INTERVAL_SECONDS = 1.4;\n  private iframeBaseUrl;\n  protected localStorage: ClientScopedStorage;\n  protected clientId: string;\n  protected ecosystem?: Ecosystem;\n\n  /**\n   * @internal\n   */\n  constructor({\n    link,\n    baseUrl,\n    iframeId,\n    container,\n    onIframeInitialize,\n    localStorage,\n    clientId,\n    ecosystem,\n  }: IFrameCommunicatorProps) {\n    this.localStorage = localStorage;\n    this.clientId = clientId;\n    this.ecosystem = ecosystem;\n    this.iframeBaseUrl = baseUrl;\n\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    container = container ?? document.body;\n    // Creating the IFrame element for communication\n    let iframe = document.getElementById(iframeId) as HTMLIFrameElement | null;\n    const hrefLink = new URL(link);\n\n    // TODO (ew) - bring back version tracking\n    // const sdkVersion = process.env.THIRDWEB_EWS_SDK_VERSION;\n    // if (!sdkVersion) {\n    //   throw new Error(\"Missing THIRDWEB_EWS_SDK_VERSION env var\");\n    // }\n    // hrefLink.searchParams.set(\"sdkVersion\", sdkVersion);\n    if (!iframe || iframe.src !== hrefLink.href) {\n      // ! Do not update the hrefLink here or it'll cause multiple re-renders\n\n      iframe = document.createElement(\"iframe\");\n      const mergedIframeStyles = {\n        ...iframeBaseStyle,\n      };\n      Object.assign(iframe.style, mergedIframeStyles);\n      iframe.setAttribute(\"id\", iframeId);\n      iframe.setAttribute(\"fetchpriority\", \"high\");\n      container.appendChild(iframe);\n\n      iframe.src = hrefLink.href;\n\n      // iframe.setAttribute(\"data-version\", sdkVersion);\n      // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n      const onIframeLoaded = (event: MessageEvent<any>) => {\n        if (event.data.eventType === \"ewsIframeLoaded\") {\n          window.removeEventListener(\"message\", onIframeLoaded);\n          if (!iframe) {\n            console.warn(\"thirdweb iFrame not found\");\n            return;\n          }\n          this.onIframeLoadHandler(iframe, onIframeInitialize)();\n        }\n      };\n      window.addEventListener(\"message\", onIframeLoaded);\n    }\n    this.iframe = iframe;\n  }\n\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n  protected async onIframeLoadedInitVariables(): Promise<Record<string, any>> {\n    return {\n      authCookie: await this.localStorage.getAuthCookie(),\n      deviceShareStored: await this.localStorage.getDeviceShare(),\n      walletUserId: await this.localStorage.getWalletUserId(),\n      clientId: this.clientId,\n      partnerId: this.ecosystem?.partnerId,\n      ecosystemId: this.ecosystem?.id,\n    };\n  }\n\n  /**\n   * @internal\n   */\n  onIframeLoadHandler(\n    iframe: HTMLIFrameElement,\n    onIframeInitialize?: () => void,\n  ) {\n    return async () => {\n      const channel = new MessageChannel();\n\n      const promise = new Promise((res, rej) => {\n        // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n        channel.port1.onmessage = (event: any) => {\n          const { data } = event;\n          channel.port1.close();\n          if (!data.success) {\n            rej(new Error(data.error));\n          }\n          isIframeLoaded.set(iframe.src, true);\n          if (onIframeInitialize) {\n            onIframeInitialize();\n          }\n          res(true);\n        };\n      });\n\n      iframe?.contentWindow?.postMessage(\n        {\n          eventType: \"initIframe\",\n          data: await this.onIframeLoadedInitVariables(),\n        },\n        this.iframeBaseUrl,\n        [channel.port2],\n      );\n\n      await promise;\n    };\n  }\n\n  /**\n   * @internal\n   */\n  async call<ReturnData>({\n    procedureName,\n    params,\n    showIframe = false,\n  }: {\n    procedureName: keyof T;\n    params: T[keyof T];\n    showIframe?: boolean;\n  }) {\n    if (!this.iframe) {\n      throw new Error(\n        \"Iframe not found. You are likely calling this from the backend where the DOM is not available.\",\n      );\n    }\n    while (!isIframeLoaded.get(this.iframe.src)) {\n      await sleep(this.POLLING_INTERVAL_SECONDS * 1000);\n    }\n    if (showIframe) {\n      this.iframe.style.display = \"block\";\n      // magic number to let the display render before performing the animation of the modal in\n      await sleep(0.005 * 1000);\n    }\n\n    const channel = new MessageChannel();\n    const promise = new Promise<ReturnData>((res, rej) => {\n      // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n      channel.port1.onmessage = async (event: any) => {\n        const { data } = event;\n        channel.port1.close();\n        if (showIframe) {\n          // magic number to let modal fade out before hiding it\n          await sleep(0.1 * 1000);\n          if (this.iframe) {\n            this.iframe.style.display = \"none\";\n          }\n        }\n        if (!data.success) {\n          rej(new Error(data.error));\n        } else {\n          res(data.data);\n        }\n      };\n    });\n\n    this.iframe.contentWindow?.postMessage(\n      {\n        eventType: procedureName,\n        // Pass the initialization data on every request in case the iframe storage was reset (can happen in some environments such as iOS PWAs)\n        data: {\n          ...params,\n          ...(await this.onIframeLoadedInitVariables()),\n        },\n      },\n      this.iframeBaseUrl,\n      [channel.port2],\n    );\n    return promise;\n  }\n\n  /**\n   * This has to be called by any iframe that will be removed from the DOM.\n   * Use to make sure that we reset the global loaded state of the particular iframe.src\n   * @internal\n   */\n  destroy() {\n    if (this.iframe) {\n      isIframeLoaded.delete(this.iframe.src);\n    }\n  }\n}\n", "import { webLocalStorage } from \"../../../../../utils/storage/webStorage.js\";\nimport { ClientScopedStorage } from \"../../../core/authentication/client-scoped-storage.js\";\nimport { IN_APP_WALLET_PATH } from \"../../../core/constants/settings.js\";\nimport type { Ecosystem } from \"../../../core/wallet/types.js\";\nimport { IframeCommunicator } from \"./IframeCommunicator.js\";\n\n/**\n * @internal\n */\nexport class InAppWalletIframeCommunicator<\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  T extends { [key: string]: any },\n> extends IframeCommunicator<T> {\n  /**\n   * @internal\n   */\n  constructor({\n    clientId,\n    baseUrl,\n    ecosystem,\n  }: {\n    clientId: string;\n    baseUrl: string;\n    ecosystem?: Ecosystem;\n  }) {\n    super({\n      iframeId: IN_APP_WALLET_IFRAME_ID + (ecosystem?.id || \"\"),\n      link: createInAppWalletIframeLink({\n        clientId,\n        path: IN_APP_WALLET_PATH,\n        ecosystem,\n        baseUrl,\n      }).href,\n      baseUrl,\n      container: typeof document === \"undefined\" ? undefined : document.body,\n      localStorage: new ClientScopedStorage({\n        storage: webLocalStorage,\n        clientId,\n        ecosystem,\n      }),\n      clientId,\n      ecosystem,\n    });\n    this.clientId = clientId;\n    this.ecosystem = ecosystem;\n  }\n}\n\n// This is the URL and ID tag of the iFrame that we communicate with\n/**\n * @internal\n */\nfunction createInAppWalletIframeLink({\n  clientId,\n  baseUrl,\n  path,\n  ecosystem,\n  queryParams,\n}: {\n  clientId: string;\n  baseUrl: string;\n  path: string;\n  ecosystem?: Ecosystem;\n  queryParams?: { [key: string]: string | number };\n}) {\n  const inAppWalletUrl = new URL(`${path}`, baseUrl);\n  if (queryParams) {\n    for (const queryKey of Object.keys(queryParams)) {\n      inAppWalletUrl.searchParams.set(\n        queryKey,\n        queryParams[queryKey]?.toString() || \"\",\n      );\n    }\n  }\n  inAppWalletUrl.searchParams.set(\"clientId\", clientId);\n  if (ecosystem?.partnerId !== undefined) {\n    inAppWalletUrl.searchParams.set(\"partnerId\", ecosystem.partnerId);\n  }\n  if (ecosystem?.id !== undefined) {\n    inAppWalletUrl.searchParams.set(\"ecosystemId\", ecosystem.id);\n  }\n  return inAppWalletUrl;\n}\nconst IN_APP_WALLET_IFRAME_ID = \"thirdweb-in-app-wallet-iframe\";\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport { getClientFetch } from \"../../../../utils/fetch.js\";\nimport type { UserWallet } from \"../wallet/enclave-wallet.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\n\n/**\n * Generate a new enclave wallet using an auth token\n * @internal\n */\nexport async function generateWallet({\n  client,\n  ecosystem,\n  authToken,\n}: {\n  client: ThirdwebClient;\n  authToken: string;\n  ecosystem?: Ecosystem;\n}) {\n  const clientFetch = getClientFetch(client, ecosystem);\n  const response = await clientFetch(\n    `${getThirdwebBaseUrl(\"inAppWallet\")}/api/v1/enclave-wallet/generate`,\n    {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"x-thirdweb-client-id\": client.clientId,\n        Authorization: `Bearer embedded-wallet-token:${authToken}`,\n      },\n    },\n  );\n\n  if (!response.ok) {\n    throw new Error(\n      `Failed to generate wallet - ${response.status} ${response.statusText}`,\n    );\n  }\n\n  const { wallet } = (await response.json()) as {\n    wallet: UserWallet;\n  };\n\n  return wallet;\n}\n", "import type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport type {\n  AuthAndWalletRpcReturnType,\n  AuthLoginReturnType,\n  AuthProvider,\n  SendEmailOtpReturnType,\n} from \"../../../core/authentication/types.js\";\nimport type { Ecosystem } from \"../../../core/wallet/types.js\";\nimport type { ClientIdWithQuerierType } from \"../../types.js\";\nimport type { InAppWalletIframeCommunicator } from \"../../utils/iFrameCommunication/InAppWalletIframeCommunicator.js\";\n\nexport type LoginQuerierTypes = {\n  loginWithCustomAuthEndpoint: { payload: string; encryptionKey: string };\n  loginWithCustomJwt: { jwt: string; encryptionKey?: string };\n  loginWithThirdwebModal: undefined | { email: string };\n  sendThirdwebSmsLoginOtp: { phoneNumber: string };\n  sendThirdwebEmailLoginOtp: { email: string };\n  verifyThirdwebEmailLoginOtp: {\n    email: string;\n    otp: string;\n    recoveryCode?: string;\n  };\n  verifyThirdwebSmsLoginOtp: {\n    phoneNumber: string;\n    otp: string;\n    recoveryCode?: string;\n  };\n  injectDeveloperClientId: undefined;\n  getHeadlessOauthLoginLink: { authProvider: AuthProvider };\n};\n\n/**\n * @internal\n */\nexport abstract class AbstractLogin<\n  MODAL = void,\n  EMAIL_MODAL extends { email: string } = { email: string },\n  EMAIL_VERIFICATION extends { email: string; otp: string } = {\n    email: string;\n    otp: string;\n    recoveryCode?: string;\n  },\n> {\n  protected LoginQuerier: InAppWalletIframeCommunicator<LoginQuerierTypes>;\n  protected preLogin;\n  protected postLogin: (\n    authResults: AuthAndWalletRpcReturnType,\n  ) => Promise<AuthLoginReturnType>;\n  protected client: ThirdwebClient;\n  protected baseUrl: string;\n  protected ecosystem?: Ecosystem;\n\n  /**\n   * Used to manage the user's auth states. This should not be instantiated directly.\n   * @internal\n   */\n  constructor({\n    baseUrl,\n    querier,\n    preLogin,\n    postLogin,\n    client,\n    ecosystem,\n  }: ClientIdWithQuerierType & {\n    baseUrl: string;\n    preLogin: () => Promise<void>;\n    postLogin: (\n      authDetails: AuthAndWalletRpcReturnType,\n    ) => Promise<AuthLoginReturnType>;\n    ecosystem?: Ecosystem;\n  }) {\n    this.baseUrl = baseUrl;\n    this.LoginQuerier = querier;\n    this.preLogin = preLogin;\n    this.postLogin = postLogin;\n    this.client = client;\n    this.ecosystem = ecosystem;\n  }\n\n  abstract loginWithCustomJwt(args: {\n    jwt: string;\n    encryptionKey: string;\n  }): Promise<AuthLoginReturnType>;\n  abstract loginWithCustomAuthEndpoint(args: {\n    payload: string;\n    encryptionKey: string;\n  }): Promise<AuthLoginReturnType>;\n  abstract loginWithModal(args?: MODAL): Promise<AuthLoginReturnType>;\n  abstract loginWithIframe(args: EMAIL_MODAL): Promise<AuthLoginReturnType>;\n\n  /**\n   * @internal\n   */\n  async sendEmailLoginOtp({\n    email,\n  }: LoginQuerierTypes[\"sendThirdwebEmailLoginOtp\"]): Promise<SendEmailOtpReturnType> {\n    const result = await this.LoginQuerier.call<SendEmailOtpReturnType>({\n      procedureName: \"sendThirdwebEmailLoginOtp\",\n      params: { email },\n    });\n    return result;\n  }\n\n  /**\n   *\n   * @internal\n   */\n  async sendSmsLoginOtp({\n    phoneNumber,\n  }: LoginQuerierTypes[\"sendThirdwebSmsLoginOtp\"]): Promise<SendEmailOtpReturnType> {\n    const result = await this.LoginQuerier.call<SendEmailOtpReturnType>({\n      procedureName: \"sendThirdwebSmsLoginOtp\",\n      params: { phoneNumber },\n    });\n    return result;\n  }\n\n  abstract loginWithEmailOtp(\n    args: EMAIL_VERIFICATION,\n  ): Promise<AuthLoginReturnType>;\n\n  abstract loginWithSmsOtp(args: {\n    phoneNumber: string;\n    otp: string;\n    recoveryCode?: string;\n  }): Promise<AuthLoginReturnType>;\n}\n", "import type {\n  AuthAndWalletRpcReturnType,\n  AuthLoginReturnType,\n} from \"../../../core/authentication/types.js\";\nimport { AbstractLogin, type LoginQuerierTypes } from \"./abstract-login.js\";\n\n/**\n *\n */\nexport class BaseLogin extends AbstractLogin<\n  void,\n  { email: string },\n  { email: string; otp: string; recoveryCode?: string }\n> {\n  async authenticateWithModal(): Promise<AuthAndWalletRpcReturnType> {\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"loginWithThirdwebModal\",\n      params: undefined,\n      showIframe: true,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithModal(): Promise<AuthLoginReturnType> {\n    await this.preLogin();\n    const result = await this.authenticateWithModal();\n    return this.postLogin(result);\n  }\n\n  async authenticateWithIframe({\n    email,\n  }: {\n    email: string;\n  }): Promise<AuthAndWalletRpcReturnType> {\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"loginWithThirdwebModal\",\n      params: { email },\n      showIframe: true,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithIframe({\n    email,\n  }: {\n    email: string;\n  }): Promise<AuthLoginReturnType> {\n    await this.preLogin();\n    const result = await this.authenticateWithIframe({ email });\n    return this.postLogin(result);\n  }\n\n  async authenticateWithCustomJwt({\n    encryptionKey,\n    jwt,\n  }: LoginQuerierTypes[\"loginWithCustomJwt\"]): Promise<AuthAndWalletRpcReturnType> {\n    if (!encryptionKey || encryptionKey.length === 0) {\n      throw new Error(\"Encryption key is required for custom jwt auth\");\n    }\n\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"loginWithCustomJwt\",\n      params: { encryptionKey, jwt },\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithCustomJwt({\n    encryptionKey,\n    jwt,\n  }: LoginQuerierTypes[\"loginWithCustomJwt\"]): Promise<AuthLoginReturnType> {\n    if (!encryptionKey || encryptionKey.length === 0) {\n      throw new Error(\"Encryption key is required for custom jwt auth\");\n    }\n\n    await this.preLogin();\n    const result = await this.authenticateWithCustomJwt({ encryptionKey, jwt });\n    return this.postLogin(result);\n  }\n\n  async authenticateWithCustomAuthEndpoint({\n    encryptionKey,\n    payload,\n  }: LoginQuerierTypes[\"loginWithCustomAuthEndpoint\"]): Promise<AuthAndWalletRpcReturnType> {\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"loginWithCustomAuthEndpoint\",\n      params: { encryptionKey, payload },\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithCustomAuthEndpoint({\n    encryptionKey,\n    payload,\n  }: LoginQuerierTypes[\"loginWithCustomAuthEndpoint\"]): Promise<AuthLoginReturnType> {\n    if (!encryptionKey || encryptionKey.length === 0) {\n      throw new Error(\"Encryption key is required for custom auth\");\n    }\n\n    await this.preLogin();\n    const result = await this.authenticateWithCustomAuthEndpoint({\n      encryptionKey,\n      payload,\n    });\n    return this.postLogin(result);\n  }\n\n  async authenticateWithEmailOtp({\n    email,\n    otp,\n    recoveryCode,\n  }: LoginQuerierTypes[\"verifyThirdwebEmailLoginOtp\"]): Promise<AuthAndWalletRpcReturnType> {\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"verifyThirdwebEmailLoginOtp\",\n      params: { email, otp, recoveryCode },\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithEmailOtp({\n    email,\n    otp,\n    recoveryCode,\n  }: LoginQuerierTypes[\"verifyThirdwebEmailLoginOtp\"]): Promise<AuthLoginReturnType> {\n    const result = await this.authenticateWithEmailOtp({\n      email,\n      otp,\n      recoveryCode,\n    });\n    return this.postLogin(result);\n  }\n\n  async authenticateWithSmsOtp({\n    phoneNumber,\n    otp,\n    recoveryCode,\n  }: LoginQuerierTypes[\"verifyThirdwebSmsLoginOtp\"]): Promise<AuthAndWalletRpcReturnType> {\n    return this.LoginQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"verifyThirdwebSmsLoginOtp\",\n      params: { phoneNumber, otp, recoveryCode },\n    });\n  }\n\n  /**\n   * @internal\n   */\n  override async loginWithSmsOtp({\n    phoneNumber,\n    otp,\n    recoveryCode,\n  }: LoginQuerierTypes[\"verifyThirdwebSmsLoginOtp\"]): Promise<AuthLoginReturnType> {\n    const result = await this.authenticateWithSmsOtp({\n      phoneNumber,\n      otp,\n      recoveryCode,\n    });\n    return this.postLogin(result);\n  }\n}\n", "import type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport { generateWallet } from \"../../../core/actions/generate-wallet.enclave.js\";\nimport { getUserStatus } from \"../../../core/actions/get-enclave-user-status.js\";\nimport type { ClientScopedStorage } from \"../../../core/authentication/client-scoped-storage.js\";\nimport type {\n  AuthAndWalletRpcReturnType,\n  AuthLoginReturnType,\n  AuthStoredTokenWithCookieReturnType,\n  LogoutReturnType,\n  SendEmailOtpReturnType,\n} from \"../../../core/authentication/types.js\";\nimport type { Ecosystem } from \"../../../core/wallet/types.js\";\nimport type { ClientIdWithQuerierType } from \"../../types.js\";\nimport type { InAppWalletIframeCommunicator } from \"../../utils/iFrameCommunication/InAppWalletIframeCommunicator.js\";\nimport { BaseLogin } from \"./base-login.js\";\n\nexport type AuthQuerierTypes = {\n  logout: undefined;\n  initIframe: {\n    partnerId?: string;\n    ecosystemId?: string;\n    clientId: string;\n    authCookie: string;\n    walletUserId: string;\n    deviceShareStored: string | null;\n  };\n  loginWithStoredTokenDetails: {\n    storedToken: AuthStoredTokenWithCookieReturnType[\"storedToken\"];\n    recoveryCode?: string;\n  };\n  migrateFromShardToEnclave: {\n    storedToken: AuthStoredTokenWithCookieReturnType[\"storedToken\"];\n  };\n};\n\n/**\n *\n */\nexport class Auth {\n  protected client: ThirdwebClient;\n  protected ecosystem?: Ecosystem;\n  protected AuthQuerier: InAppWalletIframeCommunicator<AuthQuerierTypes>;\n  protected localStorage: ClientScopedStorage;\n  protected onAuthSuccess: (\n    authResults: AuthAndWalletRpcReturnType,\n  ) => Promise<AuthLoginReturnType>;\n  private BaseLogin: BaseLogin;\n\n  /**\n   * Used to manage the user's auth states. This should not be instantiated directly.\n   * @internal\n   */\n  constructor({\n    client,\n    querier,\n    onAuthSuccess,\n    ecosystem,\n    baseUrl,\n    localStorage,\n  }: ClientIdWithQuerierType & {\n    baseUrl: string;\n    ecosystem?: Ecosystem;\n    onAuthSuccess: (\n      authDetails: AuthAndWalletRpcReturnType,\n    ) => Promise<AuthLoginReturnType>;\n    localStorage: ClientScopedStorage;\n  }) {\n    this.client = client;\n    this.ecosystem = ecosystem;\n\n    this.AuthQuerier = querier;\n    this.localStorage = localStorage;\n    this.onAuthSuccess = onAuthSuccess;\n    this.BaseLogin = new BaseLogin({\n      postLogin: async (result) => {\n        return this.postLogin(result);\n      },\n      preLogin: async () => {\n        await this.preLogin();\n      },\n      ecosystem,\n      querier: querier,\n      client,\n      baseUrl,\n    });\n  }\n\n  private async preLogin() {\n    await this.logout();\n  }\n\n  private async postLogin({\n    storedToken,\n    walletDetails,\n  }: AuthAndWalletRpcReturnType): Promise<AuthLoginReturnType> {\n    if (storedToken.shouldStoreCookieString) {\n      await this.localStorage.saveAuthCookie(storedToken.cookieString);\n    }\n    const initializedUser = await this.onAuthSuccess({\n      storedToken,\n      walletDetails,\n    });\n    return initializedUser;\n  }\n\n  async loginWithAuthToken(\n    authToken: AuthStoredTokenWithCookieReturnType,\n    recoveryCode?: string,\n  ): Promise<AuthLoginReturnType> {\n    // We don't call logout for backend auth because that is handled on the backend where the iframe isn't available to call. Moreover, logout clears the local storage which isn't applicable for backend auth.\n    if (authToken.storedToken.authProvider !== \"Backend\") {\n      await this.preLogin();\n    }\n\n    const user = await getUserStatus({\n      authToken: authToken.storedToken.cookieString,\n      client: this.client,\n      ecosystem: this.ecosystem,\n    });\n    if (!user) {\n      throw new Error(\"Cannot login, no user found for auth token\");\n    }\n\n    // If they're already an enclave wallet, proceed to login\n    if (user.wallets.length > 0 && user.wallets[0]?.type === \"enclave\") {\n      return this.postLogin({\n        storedToken: authToken.storedToken,\n        walletDetails: {\n          walletAddress: user.wallets[0].address,\n        },\n      });\n    }\n\n    if (user.wallets.length === 0) {\n      // If this is a new ecosystem wallet without an enclave yet, we'll generate an enclave\n      const result = await generateWallet({\n        authToken: authToken.storedToken.cookieString,\n        client: this.client,\n        ecosystem: this.ecosystem,\n      });\n      return this.postLogin({\n        storedToken: authToken.storedToken,\n        walletDetails: {\n          walletAddress: result.address,\n        },\n      });\n    }\n\n    // If this is an existing sharded wallet or in-app wallet, we'll login with the sharded wallet\n    const result = await this.AuthQuerier.call<AuthAndWalletRpcReturnType>({\n      procedureName: \"loginWithStoredTokenDetails\",\n      params: {\n        storedToken: authToken.storedToken,\n        recoveryCode,\n      },\n    });\n    return this.postLogin(result);\n  }\n\n  /**\n   * Used to log the user into their thirdweb wallet on your platform via a myriad of auth providers\n   * @example\n   * ```typescript\n   * const thirdwebInAppWallet = new InAppWalletSdk({clientId: \"YOUR_CLIENT_ID\", chain: \"Polygon\"})\n   * try {\n   *   const user = await thirdwebInAppWallet.auth.loginWithModal();\n   *   // user is now logged in\n   * } catch (e) {\n   *   // User closed modal or something else went wrong during the authentication process\n   *   console.error(e)\n   * }\n   * ```\n   * @returns `{{user: InitializedUser}}` An InitializedUser object.\n   */\n  async loginWithModal(): Promise<AuthLoginReturnType> {\n    return this.BaseLogin.loginWithModal();\n  }\n  async authenticateWithModal(): Promise<AuthAndWalletRpcReturnType> {\n    return this.BaseLogin.authenticateWithModal();\n  }\n\n  /**\n   * Used to log the user into their thirdweb wallet using email OTP\n   * @example\n   * ```typescript\n   *  // Basic Flow\n   *  const thirdwebInAppWallet = new InAppWalletSdk({clientId: \"\", chain: \"Polygon\"});\n   *  try {\n   *    // prompts user to enter the code they received\n   *    const user = await thirdwebInAppWallet.auth.loginWithThirdwebEmailOtp({ email : \"<EMAIL>\" });\n   *    // user is now logged in\n   *  } catch (e) {\n   *    // User closed the OTP modal or something else went wrong during the authentication process\n   *    console.error(e)\n   *  }\n   * ```\n   * @param args - args.email: We will send the email an OTP that needs to be entered in order for them to be logged in.\n   * @returns `{{user: InitializedUser}}` An InitializedUser object. See {@link InAppWalletSdk.getUser} for more\n   */\n  async loginWithIframe(\n    args: Parameters<BaseLogin[\"loginWithIframe\"]>[0],\n  ): Promise<AuthLoginReturnType> {\n    return this.BaseLogin.loginWithIframe(args);\n  }\n  async authenticateWithIframe(\n    args: Parameters<BaseLogin[\"authenticateWithIframe\"]>[0],\n  ): Promise<AuthAndWalletRpcReturnType> {\n    return this.BaseLogin.authenticateWithIframe(args);\n  }\n\n  /**\n   * @internal\n   */\n  async loginWithCustomJwt(\n    args: Parameters<BaseLogin[\"loginWithCustomJwt\"]>[0],\n  ): Promise<AuthLoginReturnType> {\n    return this.BaseLogin.loginWithCustomJwt(args);\n  }\n  async authenticateWithCustomJwt(\n    args: Parameters<BaseLogin[\"authenticateWithCustomJwt\"]>[0],\n  ): Promise<AuthAndWalletRpcReturnType> {\n    return this.BaseLogin.authenticateWithCustomJwt(args);\n  }\n\n  /**\n   * @internal\n   */\n  async loginWithCustomAuthEndpoint(\n    args: Parameters<BaseLogin[\"loginWithCustomAuthEndpoint\"]>[0],\n  ): Promise<AuthLoginReturnType> {\n    return this.BaseLogin.loginWithCustomAuthEndpoint(args);\n  }\n  async authenticateWithCustomAuthEndpoint(\n    args: Parameters<BaseLogin[\"authenticateWithCustomAuthEndpoint\"]>[0],\n  ): Promise<AuthAndWalletRpcReturnType> {\n    return this.BaseLogin.authenticateWithCustomAuthEndpoint(args);\n  }\n\n  /**\n   * A headless way to send the users at the passed email an OTP code.\n   * You need to then call {@link Auth.loginWithEmailOtp} in order to complete the login process\n   * @example\n   * @param param0.email\n   * ```typescript\n   *  const thirdwebInAppWallet = new InAppWalletSdk({clientId: \"\", chain: \"Polygon\"});\n   *  // sends user an OTP code\n   * try {\n   *    await thirdwebInAppWallet.auth.sendEmailLoginOtp({ email : \"<EMAIL>\" });\n   * } catch(e) {\n   *    // Error Sending user's email an OTP code\n   *    console.error(e);\n   * }\n   *\n   * // Then when your user is ready to verify their OTP\n   * try {\n   *    const user = await thirdwebInAppWallet.auth.verifyEmailLoginOtp({ email: \"<EMAIL>\", otp: \"6-DIGIT_CODE_HERE\" });\n   * } catch(e) {\n   *    // Error verifying the OTP code\n   *    console.error(e)\n   * }\n   * ```\n   * @param param0 - param0.email We will send the email an OTP that needs to be entered in order for them to be logged in.\n   * @returns `{{ isNewUser: boolean }}` IsNewUser indicates if the user is a new user to your platform\n   * @internal\n   */\n  async sendEmailLoginOtp({\n    email,\n  }: Parameters<\n    BaseLogin[\"sendEmailLoginOtp\"]\n  >[0]): Promise<SendEmailOtpReturnType> {\n    return this.BaseLogin.sendEmailLoginOtp({\n      email,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  async sendSmsLoginOtp({\n    phoneNumber,\n  }: Parameters<\n    BaseLogin[\"sendSmsLoginOtp\"]\n  >[0]): Promise<SendEmailOtpReturnType> {\n    return this.BaseLogin.sendSmsLoginOtp({\n      phoneNumber,\n    });\n  }\n\n  /**\n   * Used to verify the otp that the user receives from thirdweb\n   *\n   * See {@link Auth.sendEmailLoginOtp} for how the headless call flow looks like. Simply swap out the calls to `loginWithThirdwebEmailOtp` with `verifyThirdwebEmailLoginOtp`\n   * @param args - props.email We will send the email an OTP that needs to be entered in order for them to be logged in.\n   * props.otp The code that the user received in their email\n   * @returns `{{user: InitializedUser}}` An InitializedUser object containing the user's status, wallet, authDetails, and more\n   * @internal\n   */\n  async loginWithEmailOtp(args: Parameters<BaseLogin[\"loginWithEmailOtp\"]>[0]) {\n    await this.preLogin();\n    return this.BaseLogin.loginWithEmailOtp(args);\n  }\n  async authenticateWithEmailOtp(\n    args: Parameters<BaseLogin[\"authenticateWithEmailOtp\"]>[0],\n  ) {\n    return this.BaseLogin.authenticateWithEmailOtp(args);\n  }\n\n  /**\n   * @internal\n   */\n  async loginWithSmsOtp(args: Parameters<BaseLogin[\"loginWithSmsOtp\"]>[0]) {\n    await this.preLogin();\n    return this.BaseLogin.loginWithSmsOtp(args);\n  }\n  async authenticateWithSmsOtp(\n    args: Parameters<BaseLogin[\"authenticateWithSmsOtp\"]>[0],\n  ) {\n    return this.BaseLogin.authenticateWithSmsOtp(args);\n  }\n\n  /**\n   * Logs any existing user out of their wallet.\n   * @returns `{{success: boolean}}` true if a user is successfully logged out. false if there's no user currently logged in.\n   * @internal\n   */\n  async logout(): Promise<LogoutReturnType> {\n    const isRemoveAuthCookie = await this.localStorage.removeAuthCookie();\n    const isRemoveUserId = await this.localStorage.removeWalletUserId();\n\n    return {\n      success: isRemoveAuthCookie || isRemoveUserId,\n    };\n  }\n}\n", "import type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport { stringify } from \"../../../../../utils/json.js\";\nimport {\n  getLoginCallbackUrl,\n  getLoginUrl,\n} from \"../../../core/authentication/getLoginPath.js\";\nimport type {\n  AuthStoredTokenWithCookieReturnType,\n  MultiStepAuthArgsType,\n  PreAuthArgsType,\n} from \"../../../core/authentication/types.js\";\nimport type { Ecosystem } from \"../../../core/wallet/types.js\";\n\n/**\n * @internal\n */\nexport const sendOtp = async (args: PreAuthArgsType): Promise<void> => {\n  const { client, ecosystem } = args;\n  const url = getLoginUrl({ client, ecosystem, authOption: args.strategy });\n\n  const headers: Record<string, string> = {\n    \"Content-Type\": \"application/json\",\n    \"x-client-id\": client.clientId,\n  };\n\n  if (ecosystem?.id) {\n    headers[\"x-ecosystem-id\"] = ecosystem.id;\n  }\n\n  if (ecosystem?.partnerId) {\n    headers[\"x-ecosystem-partner-id\"] = ecosystem.partnerId;\n  }\n\n  const body = (() => {\n    switch (args.strategy) {\n      case \"email\":\n        return {\n          email: args.email,\n        };\n      case \"phone\":\n        return {\n          phone: args.phoneNumber,\n        };\n    }\n  })();\n\n  const response = await fetch(url, {\n    method: \"POST\",\n    headers,\n    body: stringify(body),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to send verification code\");\n  }\n\n  return await response.json();\n};\n\n/**\n * @internal\n */\nexport const verifyOtp = async (\n  args: MultiStepAuthArgsType & {\n    client: ThirdwebClient;\n    ecosystem?: Ecosystem;\n  },\n): Promise<AuthStoredTokenWithCookieReturnType> => {\n  const { client, ecosystem } = args;\n  const url = getLoginCallbackUrl({\n    authOption: args.strategy,\n    client: args.client,\n    ecosystem: args.ecosystem,\n  });\n\n  const headers: Record<string, string> = {\n    \"Content-Type\": \"application/json\",\n    \"x-client-id\": client.clientId,\n  };\n\n  if (ecosystem?.id) {\n    headers[\"x-ecosystem-id\"] = ecosystem.id;\n  }\n\n  if (ecosystem?.partnerId) {\n    headers[\"x-ecosystem-partner-id\"] = ecosystem.partnerId;\n  }\n\n  const body = (() => {\n    switch (args.strategy) {\n      case \"email\":\n        return {\n          email: args.email,\n          code: args.verificationCode,\n        };\n      case \"phone\":\n        return {\n          phone: args.phoneNumber,\n          code: args.verificationCode,\n        };\n    }\n  })();\n\n  const response = await fetch(url, {\n    method: \"POST\",\n    headers,\n    body: stringify(body),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to verify verification code\");\n  }\n\n  return await response.json();\n};\n", "import type * as ethers5 from \"ethers5\";\nimport type { TypedDataDefinition } from \"viem\";\nimport { trackTransaction } from \"../../../../analytics/track/transaction.js\";\nimport { getCached<PERSON><PERSON>n } from \"../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { eth_sendRawTransaction } from \"../../../../rpc/actions/eth_sendRawTransaction.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\nimport { getAddress } from \"../../../../utils/address.js\";\nimport { getThirdwebDomains } from \"../../../../utils/domains.js\";\nimport { type Hex, hexToString } from \"../../../../utils/encoding/hex.js\";\nimport { parseTypedData } from \"../../../../utils/signatures/helpers/parse-typed-data.js\";\nimport type { Prettify } from \"../../../../utils/type-utils.js\";\nimport type {\n  Account,\n  SendTransactionOption,\n} from \"../../../interfaces/wallet.js\";\nimport type { ClientScopedStorage } from \"../../core/authentication/client-scoped-storage.js\";\nimport type {\n  AuthResultAndRecoveryCode,\n  GetUser,\n  GetUserWalletStatusRpcReturnType,\n} from \"../../core/authentication/types.js\";\nimport type { Ecosystem } from \"../../core/wallet/types.js\";\nimport type { IWebWallet } from \"../../core/wallet/web-wallet.js\";\nimport type {\n  ClientIdWithQuerierType,\n  GetAddressReturnType,\n  SignMessageReturnType,\n  SignTransactionReturnType,\n  SignedTypedDataReturnType,\n} from \"../types.js\";\nimport type { InAppWalletIframeCommunicator } from \"../utils/iFrameCommunication/InAppWalletIframeCommunicator.js\";\n\ntype WalletManagementTypes = {\n  createWallet: undefined;\n  setUpNewDevice: undefined;\n  getUserStatus: undefined;\n};\ntype WalletManagementUiTypes = {\n  createWalletUi: undefined;\n  setUpNewDeviceUi: undefined;\n};\n\ntype SignerProcedureTypes = {\n  getAddress: undefined;\n  signMessage: {\n    message: string | Hex;\n    chainId: number;\n    rpcEndpoint?: string;\n    partnerId?: string;\n  };\n  signTransaction: {\n    transaction: ethers5.ethers.providers.TransactionRequest;\n    chainId: number;\n    rpcEndpoint?: string;\n    partnerId?: string;\n  };\n  signTypedDataV4: {\n    domain: TypedDataDefinition[\"domain\"];\n    types: TypedDataDefinition[\"types\"];\n    message: TypedDataDefinition[\"message\"];\n    chainId: number;\n    rpcEndpoint?: string;\n    partnerId?: string;\n  };\n  //connect: { provider: Provider };\n};\n\n/**\n *\n */\nexport class IFrameWallet implements IWebWallet {\n  public client: ThirdwebClient;\n  public ecosystem?: Ecosystem;\n  protected walletManagerQuerier: InAppWalletIframeCommunicator<\n    WalletManagementTypes & WalletManagementUiTypes\n  >;\n  protected localStorage: ClientScopedStorage;\n\n  /**\n   * Not meant to be initialized directly. Call {@link initializeUser} to get an instance\n   * @internal\n   */\n  constructor({\n    client,\n    ecosystem,\n    querier,\n    localStorage,\n  }: Prettify<\n    ClientIdWithQuerierType & {\n      ecosystem?: Ecosystem;\n      localStorage: ClientScopedStorage;\n    }\n  >) {\n    this.client = client;\n    this.ecosystem = ecosystem;\n    this.walletManagerQuerier = querier;\n    this.localStorage = localStorage;\n  }\n\n  /**\n   * Used to set-up the user device in the case that they are using incognito\n   * @returns `{walletAddress : string }` The user's wallet details\n   * @internal\n   */\n  async postWalletSetUp(authResult: AuthResultAndRecoveryCode): Promise<void> {\n    if (authResult.deviceShareStored) {\n      await this.localStorage.saveDeviceShare(\n        authResult.deviceShareStored,\n        authResult.storedToken.authDetails.userWalletId,\n      );\n    }\n  }\n\n  /**\n   * Gets the various status states of the user\n   * @example\n   * ```typescript\n   *  const userStatus = await Paper.getUserWalletStatus();\n   *  switch (userStatus.status) {\n   *  case UserWalletStatus.LOGGED_OUT: {\n   *    // User is logged out, call one of the auth methods on Paper.auth to authenticate the user\n   *    break;\n   *  }\n   *  case UserWalletStatus.LOGGED_IN_WALLET_UNINITIALIZED: {\n   *    // User is logged in, but does not have a wallet associated with it\n   *    // you also have access to the user's details\n   *    userStatus.user.authDetails;\n   *    break;\n   *  }\n   *  case UserWalletStatus.LOGGED_IN_NEW_DEVICE: {\n   *    // User is logged in and created a wallet already, but is missing the device shard\n   *    // You have access to:\n   *    userStatus.user.authDetails;\n   *    userStatus.user.walletAddress;\n   *    break;\n   *  }\n   *  case UserWalletStatus.LOGGED_IN_WALLET_INITIALIZED: {\n   *    // user is logged in and wallet is all set up.\n   *    // You have access to:\n   *    userStatus.user.authDetails;\n   *    userStatus.user.walletAddress;\n   *    userStatus.user.wallet;\n   *    break;\n   *  }\n   *}\n   *```\n   * @returns `{GetUserWalletStatusFnReturnType}` an object to containing various information on the user statuses\n   * @internal\n   */\n  async getUserWalletStatus(): Promise<GetUser> {\n    const userStatus =\n      await this.walletManagerQuerier.call<GetUserWalletStatusRpcReturnType>({\n        procedureName: \"getUserStatus\",\n        params: undefined,\n      });\n    if (userStatus.status === \"Logged In, Wallet Initialized\") {\n      return {\n        status: \"Logged In, Wallet Initialized\",\n        ...userStatus.user,\n        account: await this.getAccount(),\n      };\n    }\n    if (userStatus.status === \"Logged In, New Device\") {\n      return {\n        status: \"Logged In, New Device\",\n        ...userStatus.user,\n      };\n    }\n    if (userStatus.status === \"Logged In, Wallet Uninitialized\") {\n      return {\n        status: \"Logged In, Wallet Uninitialized\",\n        ...userStatus.user,\n      };\n    }\n    // Logged out\n    return { status: userStatus.status };\n  }\n\n  /**\n   * Returns an account that communicates with the iFrame for signing operations\n   * @internal\n   */\n  async getAccount(): Promise<Account> {\n    const querier = this\n      .walletManagerQuerier as unknown as InAppWalletIframeCommunicator<SignerProcedureTypes>;\n    const client = this.client;\n    const partnerId = this.ecosystem?.partnerId;\n\n    const { address } = await querier.call<GetAddressReturnType>({\n      procedureName: \"getAddress\",\n      params: undefined,\n    });\n    const _signTransaction = async (tx: SendTransactionOption) => {\n      // biome-ignore lint/suspicious/noExplicitAny: ethers tx transformation\n      const transaction: Record<string, any> = {\n        to: tx.to ?? undefined,\n        data: tx.data,\n        value: tx.value,\n        gasLimit: tx.gas,\n        nonce: tx.nonce,\n        chainId: tx.chainId,\n      };\n\n      if (tx.maxFeePerGas) {\n        // ethers (in the iframe) rejects any type 0 transaction with unknown keys\n        // TODO remove this once iframe is upgraded to v5\n        transaction.accessList = tx.accessList;\n        transaction.maxFeePerGas = tx.maxFeePerGas;\n        transaction.maxPriorityFeePerGas = tx.maxPriorityFeePerGas;\n        transaction.type = 2;\n      } else {\n        transaction.gasPrice = tx.gasPrice;\n        transaction.type = 0;\n      }\n      const RPC_URL = getThirdwebDomains().rpc;\n      const { signedTransaction } =\n        await querier.call<SignTransactionReturnType>({\n          procedureName: \"signTransaction\",\n          params: {\n            transaction,\n            chainId: tx.chainId,\n            partnerId,\n            rpcEndpoint: `https://${tx.chainId}.${RPC_URL}`, // TODO (ew) shouldnt be needed\n          },\n        });\n      return signedTransaction as Hex;\n    };\n    return {\n      address: getAddress(address),\n      async signTransaction(tx) {\n        if (!tx.chainId) {\n          throw new Error(\"chainId required in tx to sign\");\n        }\n        return _signTransaction({\n          ...tx,\n          chainId: tx.chainId,\n        });\n      },\n      async sendTransaction(tx) {\n        const rpcRequest = getRpcClient({\n          client,\n          chain: getCachedChain(tx.chainId),\n        });\n        const signedTx = await _signTransaction(tx);\n\n        const transactionHash = await eth_sendRawTransaction(\n          rpcRequest,\n          signedTx,\n        );\n\n        trackTransaction({\n          client,\n          chainId: tx.chainId,\n          walletAddress: address,\n          walletType: \"inApp\",\n          transactionHash,\n          contractAddress: tx.to ?? undefined,\n          gasPrice: tx.gasPrice,\n        });\n\n        return { transactionHash };\n      },\n      async signMessage({ message }) {\n        // in-app wallets use ethers to sign messages, which always expects a string (or bytes maybe but string is safest)\n        const messageDecoded = (() => {\n          if (typeof message === \"string\") {\n            return message;\n          }\n          if (message.raw instanceof Uint8Array) {\n            return message.raw;\n          }\n          return hexToString(message.raw);\n        })();\n\n        const { signedMessage } = await querier.call<SignMessageReturnType>({\n          procedureName: \"signMessage\",\n          params: {\n            // biome-ignore lint/suspicious/noExplicitAny: ethers tx transformation\n            message: messageDecoded as any, // needs bytes or string\n            partnerId,\n            chainId: 1, // TODO check if we need this\n          },\n        });\n        return signedMessage as Hex;\n      },\n      async signTypedData(_typedData) {\n        const parsedTypedData = parseTypedData(_typedData);\n        // deleting EIP712 Domain as it results in ambiguous primary type on some cases\n        // this happens when going from viem to ethers via the iframe\n        if (parsedTypedData.types?.EIP712Domain) {\n          parsedTypedData.types.EIP712Domain = undefined;\n        }\n        const domain = parsedTypedData.domain as TypedDataDefinition[\"domain\"];\n        const chainId = domain?.chainId;\n        const verifyingContract = domain?.verifyingContract\n          ? { verifyingContract: domain?.verifyingContract }\n          : {};\n        const domainData = {\n          ...verifyingContract,\n          name: domain?.name,\n          version: domain?.version,\n        };\n        // chain id can't be included if it wasn't explicitly specified\n        if (chainId) {\n          (domainData as Record<string, unknown>).chainId = chainId;\n        }\n\n        const RPC_URL = getThirdwebDomains().rpc;\n        const { signedTypedData } =\n          await querier.call<SignedTypedDataReturnType>({\n            procedureName: \"signTypedDataV4\",\n            params: {\n              domain: domainData,\n              types:\n                parsedTypedData.types as SignerProcedureTypes[\"signTypedDataV4\"][\"types\"],\n              message:\n                parsedTypedData.message as SignerProcedureTypes[\"signTypedDataV4\"][\"message\"],\n              chainId: Number.parseInt(BigInt(chainId || 1).toString()),\n              partnerId,\n              rpcEndpoint: `https://${chainId}.${RPC_URL}`, // TODO (ew) shouldnt be needed\n            },\n          });\n        return signedTypedData as Hex;\n      },\n    };\n  }\n}\n", "import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getThirdwebBaseUrl } from \"../../../../utils/domains.js\";\nimport type { AsyncStorage } from \"../../../../utils/storage/AsyncStorage.js\";\nimport { inMemoryStorage } from \"../../../../utils/storage/inMemoryStorage.js\";\nimport { webLocalStorage } from \"../../../../utils/storage/webStorage.js\";\nimport type { SocialAuthOption } from \"../../../../wallets/types.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport { getUserStatus } from \"../../core/actions/get-enclave-user-status.js\";\nimport { authEndpoint } from \"../../core/authentication/authEndpoint.js\";\nimport { backendAuthenticate } from \"../../core/authentication/backend.js\";\nimport { ClientScopedStorage } from \"../../core/authentication/client-scoped-storage.js\";\nimport { guestAuthenticate } from \"../../core/authentication/guest.js\";\nimport { customJwt } from \"../../core/authentication/jwt.js\";\nimport {\n  getLinkedProfilesInternal,\n  linkAccount,\n  unlinkAccount,\n} from \"../../core/authentication/linkAccount.js\";\nimport {\n  loginWithPasskey,\n  registerPasskey,\n} from \"../../core/authentication/passkeys.js\";\nimport { siweAuthenticate } from \"../../core/authentication/siwe.js\";\nimport type {\n  AuthArgsType,\n  AuthLoginReturnType,\n  AuthStoredTokenWithCookieReturnType,\n  GetUser,\n  LogoutReturnType,\n  MultiStepAuthArgsType,\n  MultiStepAuthProviderType,\n  Profile,\n  SingleStepAuthArgsType,\n} from \"../../core/authentication/types.js\";\nimport type { InAppConnector } from \"../../core/interfaces/connector.js\";\nimport { EnclaveWallet } from \"../../core/wallet/enclave-wallet.js\";\nimport type { Ecosystem } from \"../../core/wallet/types.js\";\nimport type { IWebWallet } from \"../../core/wallet/web-wallet.js\";\nimport type { InAppWalletConstructorType } from \"../types.js\";\nimport { InAppWalletIframeCommunicator } from \"../utils/iFrameCommunication/InAppWalletIframeCommunicator.js\";\nimport { Auth, type AuthQuerierTypes } from \"./auth/iframe-auth.js\";\nimport { loginWithOauth, loginWithOauthRedirect } from \"./auth/oauth.js\";\nimport { sendOtp, verifyOtp } from \"./auth/otp.js\";\nimport { IFrameWallet } from \"./iframe-wallet.js\";\n\n/**\n * @internal\n */\nexport class InAppWebConnector implements InAppConnector {\n  private client: ThirdwebClient;\n  private ecosystem?: Ecosystem;\n  private querier: InAppWalletIframeCommunicator<AuthQuerierTypes>;\n  private storage: ClientScopedStorage;\n\n  private wallet?: IWebWallet;\n  /**\n   * Used to manage the Auth state of the user.\n   */\n  auth: Auth;\n  private passkeyDomain?: string;\n\n  private isClientIdLegacyPaper(clientId: string): boolean {\n    if (clientId.indexOf(\"-\") > 0 && clientId.length === 36) {\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @example\n   * `const thirdwebInAppWallet = new InAppWalletSdk({ clientId: \"\", chain: \"Goerli\" });`\n   * @internal\n   */\n  constructor({\n    client,\n    onAuthSuccess,\n    ecosystem,\n    passkeyDomain,\n    storage,\n  }: InAppWalletConstructorType) {\n    if (this.isClientIdLegacyPaper(client.clientId)) {\n      throw new Error(\n        \"You are using a legacy clientId. Please use the clientId found on the thirdweb dashboard settings page\",\n      );\n    }\n    const baseUrl = getThirdwebBaseUrl(\"inAppWallet\");\n    this.client = client;\n    this.ecosystem = ecosystem;\n    this.passkeyDomain = passkeyDomain;\n    this.storage = new ClientScopedStorage({\n      storage: storage ?? getDefaultStorage(),\n      clientId: client.clientId,\n      ecosystem: ecosystem,\n    });\n    this.querier = new InAppWalletIframeCommunicator({\n      clientId: client.clientId,\n      ecosystem,\n      baseUrl,\n    });\n\n    this.auth = new Auth({\n      client,\n      querier: this.querier,\n      baseUrl,\n      localStorage: this.storage,\n      ecosystem,\n      onAuthSuccess: async (authResult) => {\n        onAuthSuccess?.(authResult);\n\n        if (authResult.storedToken.authDetails.walletType === \"sharded\") {\n          // If this is an existing sharded ecosystem wallet, we'll need to migrate\n          const result = await this.querier.call<boolean>({\n            procedureName: \"migrateFromShardToEnclave\",\n            params: {\n              storedToken: authResult.storedToken,\n            },\n          });\n          if (!result) {\n            console.warn(\n              \"Failed to migrate from sharded to enclave wallet, continuing with sharded wallet\",\n            );\n          }\n        }\n\n        this.wallet = await this.initializeWallet(\n          authResult.storedToken.cookieString,\n        );\n\n        if (!this.wallet) {\n          throw new Error(\"Failed to initialize wallet\");\n        }\n\n        const deviceShareStored =\n          \"deviceShareStored\" in authResult.walletDetails\n            ? authResult.walletDetails.deviceShareStored\n            : undefined;\n\n        await this.wallet.postWalletSetUp({\n          storedToken: authResult.storedToken,\n          deviceShareStored,\n        });\n\n        if (this.wallet instanceof IFrameWallet) {\n          await this.querier.call({\n            procedureName: \"initIframe\",\n            params: {\n              partnerId: ecosystem?.partnerId,\n              ecosystemId: ecosystem?.id,\n              clientId: this.client.clientId,\n              // For enclave wallets we won't have a device share\n              deviceShareStored:\n                \"deviceShareStored\" in authResult.walletDetails\n                  ? authResult.walletDetails.deviceShareStored\n                  : null,\n              walletUserId: authResult.storedToken.authDetails.userWalletId,\n              authCookie: authResult.storedToken.cookieString,\n            },\n          });\n        }\n\n        return {\n          user: {\n            status: \"Logged In, Wallet Initialized\",\n            authDetails: authResult.storedToken.authDetails,\n            account: await this.wallet.getAccount(),\n            walletAddress: authResult.walletDetails.walletAddress,\n          },\n        };\n      },\n    });\n  }\n\n  async initializeWallet(authToken?: string): Promise<IWebWallet> {\n    const storedAuthToken = await this.storage.getAuthCookie();\n    if (!authToken && storedAuthToken === null) {\n      throw new Error(\n        \"No auth token provided and no stored auth token found to initialize the wallet\",\n      );\n    }\n\n    const user = await getUserStatus({\n      authToken: authToken || (storedAuthToken as string),\n      client: this.client,\n      ecosystem: this.ecosystem,\n    });\n\n    if (!user) {\n      throw new Error(\"Cannot initialize wallet, no user logged in\");\n    }\n    if (user.wallets.length === 0) {\n      throw new Error(\n        \"Cannot initialize wallet, this user does not have a wallet generated yet\",\n      );\n    }\n\n    if (user.wallets[0]?.type === \"enclave\") {\n      return new EnclaveWallet({\n        client: this.client,\n        ecosystem: this.ecosystem,\n        address: user.wallets[0].address,\n        storage: this.storage,\n      });\n    }\n\n    return new IFrameWallet({\n      client: this.client,\n      ecosystem: this.ecosystem,\n      querier: this.querier,\n      localStorage: this.storage,\n    });\n  }\n\n  /**\n   * Gets the user if they're logged in\n   * @example\n   * ```js\n   *  const user = await thirdwebInAppWallet.getUser();\n   *  switch (user.status) {\n   *     case UserWalletStatus.LOGGED_OUT: {\n   *       // User is logged out, call one of the auth methods on thirdwebInAppWallet.auth to authenticate the user\n   *       break;\n   *     }\n   *     case UserWalletStatus.LOGGED_IN_WALLET_INITIALIZED: {\n   *       // user is logged in and wallet is all set up.\n   *       // You have access to:\n   *       user.status;\n   *       user.authDetails;\n   *       user.walletAddress;\n   *       user.wallet;\n   *       break;\n   *     }\n   * }\n   * ```\n   * @returns GetUser - an object to containing various information on the user statuses\n   */\n  async getUser(): Promise<GetUser> {\n    // If we don't have a wallet yet we'll create one\n    if (!this.wallet) {\n      const localAuthToken = await this.storage.getAuthCookie();\n      if (!localAuthToken) {\n        return { status: \"Logged Out\" };\n      }\n      this.wallet = await this.initializeWallet(localAuthToken);\n    }\n    if (!this.wallet) {\n      throw new Error(\"Wallet not initialized\");\n    }\n    return await this.wallet.getUserWalletStatus();\n  }\n\n  getAccount(): Promise<Account> {\n    if (!this.wallet) {\n      throw new Error(\"Wallet not initialized\");\n    }\n    return this.wallet.getAccount();\n  }\n\n  async preAuthenticate(args: MultiStepAuthProviderType): Promise<void> {\n    return sendOtp({\n      ...args,\n      client: this.client,\n      ecosystem: this.ecosystem,\n    });\n  }\n\n  async authenticateWithRedirect(\n    strategy: SocialAuthOption,\n    mode?: \"redirect\" | \"popup\" | \"window\",\n    redirectUrl?: string,\n  ): Promise<void> {\n    return loginWithOauthRedirect({\n      authOption: strategy,\n      client: this.client,\n      ecosystem: this.ecosystem,\n      redirectUrl,\n      mode,\n    });\n  }\n\n  async loginWithAuthToken(\n    authResult: AuthStoredTokenWithCookieReturnType,\n    recoveryCode?: string,\n  ) {\n    return this.auth.loginWithAuthToken(authResult, recoveryCode);\n  }\n\n  /**\n   * Authenticates the user and returns the auth token, but does not instantiate their wallet\n   */\n  async authenticate(\n    args: MultiStepAuthArgsType | SingleStepAuthArgsType,\n  ): Promise<AuthStoredTokenWithCookieReturnType> {\n    const strategy = args.strategy;\n    switch (strategy) {\n      case \"email\":\n        return verifyOtp({\n          ...args,\n          client: this.client,\n          ecosystem: this.ecosystem,\n        });\n      case \"phone\":\n        return verifyOtp({\n          ...args,\n          client: this.client,\n          ecosystem: this.ecosystem,\n        });\n      case \"auth_endpoint\": {\n        return authEndpoint({\n          payload: args.payload,\n          client: this.client,\n          ecosystem: this.ecosystem,\n        });\n      }\n      case \"jwt\":\n        return customJwt({\n          jwt: args.jwt,\n          client: this.client,\n          ecosystem: this.ecosystem,\n        });\n      case \"passkey\": {\n        return this.passkeyAuth(args);\n      }\n      case \"iframe_email_verification\": {\n        return this.auth.authenticateWithIframe({\n          email: args.email,\n        });\n      }\n      case \"iframe\": {\n        return this.auth.authenticateWithModal();\n      }\n      case \"apple\":\n      case \"facebook\":\n      case \"google\":\n      case \"telegram\":\n      case \"github\":\n      case \"twitch\":\n      case \"farcaster\":\n      case \"line\":\n      case \"x\":\n      case \"steam\":\n      case \"coinbase\":\n      case \"discord\": {\n        return loginWithOauth({\n          authOption: strategy,\n          client: this.client,\n          ecosystem: this.ecosystem,\n          closeOpenedWindow: args.closeOpenedWindow,\n          openedWindow: args.openedWindow,\n        });\n      }\n      case \"guest\": {\n        return guestAuthenticate({\n          client: this.client,\n          ecosystem: this.ecosystem,\n          storage: webLocalStorage,\n        });\n      }\n      case \"backend\": {\n        return backendAuthenticate({\n          client: this.client,\n          walletSecret: args.walletSecret,\n          ecosystem: this.ecosystem,\n        });\n      }\n      case \"wallet\": {\n        return siweAuthenticate({\n          ecosystem: this.ecosystem,\n          client: this.client,\n          wallet: args.wallet,\n          chain: args.chain,\n        });\n      }\n    }\n  }\n\n  /**\n   * Authenticates the user then instantiates their wallet using the resulting auth token\n   */\n  async connect(\n    args: MultiStepAuthArgsType | SingleStepAuthArgsType,\n  ): Promise<AuthLoginReturnType> {\n    const strategy = args.strategy;\n    switch (strategy) {\n      case \"auth_endpoint\":\n      case \"jwt\": {\n        const authToken = await this.authenticate(args);\n        return await this.loginWithAuthToken(authToken, args.encryptionKey);\n      }\n      case \"iframe_email_verification\": {\n        return this.auth.loginWithIframe({\n          email: args.email,\n        });\n      }\n      case \"iframe\": {\n        return this.auth.loginWithModal();\n      }\n      case \"passkey\": {\n        const authToken = await this.passkeyAuth(args);\n        return this.loginWithAuthToken(authToken);\n      }\n      case \"backend\":\n      case \"phone\":\n      case \"email\":\n      case \"wallet\":\n      case \"apple\":\n      case \"facebook\":\n      case \"google\":\n      case \"farcaster\":\n      case \"telegram\":\n      case \"github\":\n      case \"line\":\n      case \"x\":\n      case \"guest\":\n      case \"coinbase\":\n      case \"twitch\":\n      case \"steam\":\n      case \"discord\": {\n        const authToken = await this.authenticate(args);\n        return await this.auth.loginWithAuthToken(authToken);\n      }\n\n      default:\n        assertUnreachable(strategy);\n    }\n  }\n\n  async logout(): Promise<LogoutReturnType> {\n    return await this.auth.logout();\n  }\n\n  private async passkeyAuth(\n    args: Extract<SingleStepAuthArgsType, { strategy: \"passkey\" }>,\n  ) {\n    const { PasskeyWebClient } = await import(\"./auth/passkeys.js\");\n    const { passkeyName, storeLastUsedPasskey = true } = args;\n    const passkeyClient = new PasskeyWebClient();\n    const storage = this.storage;\n    if (args.type === \"sign-up\") {\n      return registerPasskey({\n        client: this.client,\n        ecosystem: this.ecosystem,\n        username: passkeyName,\n        passkeyClient,\n        storage: storeLastUsedPasskey ? storage : undefined,\n        rp: {\n          id: this.passkeyDomain ?? window.location.hostname,\n          name: this.passkeyDomain ?? window.document.title,\n        },\n      });\n    }\n    return loginWithPasskey({\n      client: this.client,\n      ecosystem: this.ecosystem,\n      passkeyClient,\n      storage: storeLastUsedPasskey ? storage : undefined,\n      rp: {\n        id: this.passkeyDomain ?? window.location.hostname,\n        name: this.passkeyDomain ?? window.document.title,\n      },\n    });\n  }\n\n  async linkProfile(args: AuthArgsType) {\n    const { storedToken } = await this.authenticate(args);\n    return await linkAccount({\n      client: args.client,\n      tokenToLink: storedToken.cookieString,\n      storage: this.storage,\n      ecosystem: args.ecosystem || this.ecosystem,\n    });\n  }\n\n  async unlinkProfile(profile: Profile) {\n    return await unlinkAccount({\n      client: this.client,\n      storage: this.storage,\n      ecosystem: this.ecosystem,\n      profileToUnlink: profile,\n    });\n  }\n\n  async getProfiles() {\n    return getLinkedProfilesInternal({\n      client: this.client,\n      ecosystem: this.ecosystem,\n      storage: this.storage,\n    });\n  }\n}\n\nfunction assertUnreachable(x: never, message?: string): never {\n  throw new Error(message ?? `Invalid param: ${x}`);\n}\n\nfunction getDefaultStorage(): AsyncStorage {\n  if (typeof window !== \"undefined\" && window.localStorage) {\n    return webLocalStorage;\n  }\n  // default to in-memory storage if we're not in the browser\n  return inMemoryStorage;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,QAAQ,oBAAI,IAAG;AAEd,IAAM,kBAAgC;EAC3C,SAAS,OAAO,QAAe;AAC7B,WAAO,MAAM,IAAI,GAAG,KAAK;EAC3B;EACA,SAAS,OAAO,KAAa,UAAiB;AAC5C,UAAM,IAAI,KAAK,KAAK;EACtB;EACA,YAAY,OAAO,QAAe;AAChC,UAAM,OAAO,GAAG;EAClB;;;;ACFF,eAAsB,cAAc,EAClC,WACA,QACA,UAAS,GAKV;AACC,QAAM,cAAc,eAAe,QAAQ,SAAS;AACpD,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,4BACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;GAE3D;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,SAAS,MAAM,SAAS,KAAI,EAAG,MAAM,MAAK;AAC9C,aAAO;IACT,CAAC;AACD,UAAM,IAAI,MAAM,4BAA4B,MAAM,EAAE;EACtD;AAEA,SAAQ,MAAM,SAAS,KAAI;AAC7B;;;ACvCA,IAAM,gBAAgB;AACf,IAAM,mBAAmB,gBAAgB;AAEhD,IAAM,kBAAkB;AACjB,IAAM,qBAAqB,kBAAkB;AAKpD,IAAM,oBAAoB;AACnB,IAAM,uBAAuB,oBAAoB;AAEjD,IAAM,aAAa;AAG1B,IAAM,uBAAuB;AAW7B,IAAM,kBAAkB,mBAAmB,aAAa;AACxD,IAAM,gBAAgB,GAAG,eAAe;AACxC,IAAM,iCAAiC,GAAG,aAAa;AACvD,IAAM,iCAAiC,GAAG,aAAa;AAEhD,IAAM,gCAAgC,GAAG,8BAA8B;AACvE,IAAM,kCAAkC,eAAe,UAAU,kBAAkB,oBAAoB;AAEvG,IAAM,0BAA0B,GAAG,8BAA8B;AACjE,IAAM,wBAAwB,GAAG,8BAA8B;AAC/D,IAAM,kCAAkC,GAAG,8BAA8B;AACzE,IAAM,0BAA0B,GAAG,8BAA8B;AACjE,IAAM,+BAA+B,GAAG,8BAA8B;AAEtE,IAAM,iCAAiC,GAAG,8BAA8B;AACxE,IAAM,iCAAiC,GAAG,8BAA8B;;;ACnCxE,IAAM,qBAAqB,CAAC,SAAiB,UAA0B;AAC5E,MAAI,iBAAiB,OAAO;AAC1B,WAAO,GAAG,OAAO,KAAK,MAAM,OAAO;EACrC;AACA,SAAO,GAAG,OAAO,KAAK,UAAU,KAAK,CAAC;AACxC;;;ACLA,eAAsB,aAAa,MAIlC;AACC,QAAM,cAAc,eAAe,KAAK,QAAQ,KAAK,SAAS;AAE9D,QAAM,MAAM,MAAM,YAAY,8BAA8B;IAC1D,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,UAAU;MACd,SAAS,KAAK;MACd,mBAAmB,KAAK,OAAO;KAChC;GACF;AAED,MAAI,CAAC,IAAI,IAAI;AACX,UAAM,QAAQ,MAAM,IAAI,KAAI;AAC5B,UAAM,IAAI,MACR,8CAA8C,MAAM,OAAO,EAAE;EAEjE;AAEA,MAAI;AACF,UAAM,EAAE,cAAa,IAAK,MAAM,IAAI,KAAI;AAExC,WAAO,EAAE,aAAa,cAAa;EACrC,SAAS,GAAG;AACV,UAAM,IAAI,MACR,mBACE,6DACA,CAAC,CACF;EAEL;AACF;;;AClCA,eAAsB,oBAAoB,MAIzC;AACC,QAAM,cAAc,eAAe,KAAK,QAAQ,KAAK,SAAS;AAC9D,QAAM,OAAO,YAAY;IACvB,YAAY;IACZ,QAAQ,KAAK;IACb,WAAW,KAAK;GACjB;AACD,QAAM,MAAM,MAAM,YAAY,GAAG,IAAI,IAAI;IACvC,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,UAAU;MACd,cAAc,KAAK;KACpB;GACF;AAED,MAAI,CAAC,IAAI,IAAI;AACX,UAAM,IAAI,MAAM,oCAAoC;EACtD;AAEA,SAAQ,MAAM,IAAI,KAAI;AACxB;;;ACvBA,eAAsB,kBAAkB,MAIvC;AACC,QAAM,UAAU,IAAI,oBAAoB;IACtC,SAAS,KAAK;IACd,UAAU,KAAK,OAAO;IACtB,WAAW,KAAK;GACjB;AAED,MAAI,YAAY,MAAM,QAAQ,kBAAiB;AAC/C,MAAI,CAAC,WAAW;AACd,gBAAY,eAAe,EAAE;AAC7B,YAAQ,mBAAmB,SAAS;EACtC;AAEA,QAAM,cAAc,eAAe,KAAK,QAAQ,KAAK,SAAS;AAC9D,QAAM,OAAO,oBAAoB;IAC/B,YAAY;IACZ,QAAQ,KAAK;IACb,WAAW,KAAK;GACjB;AACD,QAAM,MAAM,MAAM,YAAY,GAAG,IAAI,IAAI;IACvC,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,UAAU;MACd;KACD;GACF;AAED,MAAI,CAAC,IAAI;AAAI,UAAM,IAAI,MAAM,kCAAkC;AAE/D,SAAQ,MAAM,IAAI,KAAI;AACxB;;;AC1CA,eAAsB,UAAU,MAI/B;AACC,QAAM,cAAc,eAAe,KAAK,QAAQ,KAAK,SAAS;AAE9D,QAAM,MAAM,MAAM,YAAY,yBAAyB;IACrD,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,UAAU;MACd,KAAK,KAAK;MACV,mBAAmB,KAAK,OAAO;KAChC;GACF;AAED,MAAI,CAAC,IAAI,IAAI;AACX,UAAM,QAAQ,MAAM,IAAI,KAAI;AAC5B,UAAM,IAAI,MAAM,6BAA6B,MAAM,OAAO,EAAE;EAC9D;AAEA,MAAI;AACF,UAAM,EAAE,cAAa,IAAK,MAAM,IAAI,KAAI;AAExC,WAAO,EAAE,aAAa,cAAa;EACrC,SAAS,GAAG;AACV,UAAM,IAAI,MACR,mBAAmB,mDAAmD,CAAC,CAAC;EAE5E;AACF;;;AC1BA,eAAsB,YAAY,EAChC,QACA,WACA,aACA,QAAO,GAMR;AACC,QAAM,cAAc,eAAe,QAAQ,SAAS;AACpD,QAAM,aAAa,mBAAmB,aAAa;AACnD,QAAM,sBAAsB,MAAM,QAAQ,cAAa;AAEvD,MAAI,CAAC,qBAAqB;AACxB,UAAM,IAAI,MAAM,2CAA2C;EAC7D;AAEA,QAAM,UAAkC;IACtC,eAAe,yBAAyB,mBAAmB;IAC3D,gBAAgB;;AAElB,QAAM,oBAAoB,MAAM,YAC9B,GAAG,UAAU,mCACb;IACE,QAAQ;IACR;IACA,MAAM,UAAU;MACd,2BAA2B;KAC5B;GACF;AAGH,MAAI,CAAC,kBAAkB,IAAI;AACzB,UAAM,OAAO,MAAM,kBAAkB,KAAI;AACzC,UAAM,IAAI,MAAM,KAAK,WAAW,yBAAyB;EAC3D;AAEA,QAAM,EAAE,eAAc,IAAK,MAAM,kBAAkB,KAAI;AAEvD,SAAQ,kBAAkB,CAAA;AAC5B;AAQA,eAAsB,cAAc,EAClC,QACA,WACA,iBACA,QAAO,GAMR;AACC,QAAM,cAAc,eAAe,QAAQ,SAAS;AACpD,QAAM,aAAa,mBAAmB,aAAa;AACnD,QAAM,sBAAsB,MAAM,QAAQ,cAAa;AAEvD,MAAI,CAAC,qBAAqB;AACxB,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AAEA,QAAM,UAAkC;IACtC,eAAe,yBAAyB,mBAAmB;IAC3D,gBAAgB;;AAElB,QAAM,oBAAoB,MAAM,YAC9B,GAAG,UAAU,sCACb;IACE,QAAQ;IACR;IACA,MAAM,UAAU,eAAe;GAChC;AAGH,MAAI,CAAC,kBAAkB,IAAI;AACzB,UAAM,OAAO,MAAM,kBAAkB,KAAI;AACzC,UAAM,IAAI,MAAM,KAAK,WAAW,2BAA2B;EAC7D;AAEA,QAAM,EAAE,eAAc,IAAK,MAAM,kBAAkB,KAAI;AAEvD,SAAQ,kBAAkB,CAAA;AAC5B;AAQA,eAAsB,0BAA0B,EAC9C,QACA,WACA,QAAO,GAKR;AACC,QAAM,cAAc,eAAe,QAAQ,SAAS;AACpD,QAAM,aAAa,mBAAmB,aAAa;AACnD,QAAM,sBAAsB,MAAM,QAAQ,cAAa;AACvD,MAAI,CAAC,qBAAqB;AACxB,UAAM,IAAI,MAAM,kDAAkD;EACpE;AAEA,QAAM,UAAkC;IACtC,eAAe,yBAAyB,mBAAmB;IAC3D,gBAAgB;;AAGlB,QAAM,qBAAqB,MAAM,YAC/B,GAAG,UAAU,4BACb;IACE,QAAQ;IACR;GACD;AAGH,MAAI,CAAC,mBAAmB,IAAI;AAC1B,UAAM,OAAO,MAAM,mBAAmB,KAAI;AAC1C,UAAM,IAAI,MAAM,KAAK,WAAW,gCAAgC;EAClE;AAEA,QAAM,EAAE,eAAc,IAAK,MAAM,mBAAmB,KAAI;AAExD,SAAQ,kBAAkB,CAAA;AAC5B;;;AC7IA,SAAS,sBAAmB;AAC1B,SAAO,GAAG,mBACR,aAAa,CACd;AACH;AACA,SAAS,iBAAiB,MAA6B,UAAiB;AACtE,SAAO,GAAG,mBACR,aAAa,CACd,sCAAsC,IAAI,GACzC,WAAW,aAAa,QAAQ,KAAK,EACvC;AACF;AAqCA,eAAsB,gBAAgB,SAOrC;AA9DD;AA+DE,MAAI,CAAC,QAAQ,cAAc,YAAW,GAAI;AACxC,UAAM,IAAI,MAAM,2CAA2C;EAC7D;AACA,QAAM,cAAc,eAAe,QAAQ,QAAQ,QAAQ,SAAS;AACpE,QAAM,gBAAgB,QAAQ,YAAY,iBAAiB,QAAQ,SAAS;AAE5E,QAAM,MAAM,MAAM,YAAY,iBAAiB,WAAW,aAAa,CAAC;AACxE,QAAM,gBAAgB,MAAM,IAAI,KAAI;AACpC,MAAI,CAAC,cAAc,WAAW;AAC5B,UAAM,IAAI,MAAM,uBAAuB;EACzC;AACA,QAAM,YAAY,cAAc;AAGhC,QAAM,eAAe,MAAM,QAAQ,cAAc,SAAS;IACxD,MAAM;IACN;IACA,IAAI,QAAQ;GACb;AAED,QAAM,gBAAwC,CAAA;AAC9C,OAAI,aAAQ,cAAR,mBAAmB,WAAW;AAChC,kBAAc,wBAAwB,IAAI,QAAQ,UAAU;EAC9D;AACA,OAAI,aAAQ,cAAR,mBAAmB,IAAI;AACzB,kBAAc,gBAAgB,IAAI,QAAQ,UAAU;EACtD;AAGA,QAAM,WAAW,MAAM,YAAY,oBAAmB,GAAI;IACxD,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,GAAG;;IAEL,MAAM,UAAU;MACd,MAAM;MACN,mBAAmB,aAAa;MAChC,cAAc,aAAa;MAC3B,sBAAsB,cAAc;MACpC,YAAY,aAAa;MACzB,UAAU;MACV,YAAY;QACV,WAAW,aAAa,WAAW;QACnC,WAAW,aAAa,WAAW;;MAErC,QAAQ,aAAa;MACrB,MAAM,QAAQ,GAAG;KAClB;GACF;AACD,QAAM,YAAY,MAAM,SAAS,KAAI;AAErC,MAAI,CAAC,aAAa,CAAC,UAAU,aAAa;AACxC,UAAM,IAAI,MACR,4BAA4B,UAAU,WAAW,eAAe,EAAE;EAEtE;AAEA,UAAM,aAAQ,YAAR,mBAAiB,wBAAwB,aAAa;AAG5D,SAAO;AACT;AAEA,eAAsB,iBAAiB,SAMtC;AArID;AAsIE,MAAI,CAAC,QAAQ,cAAc,YAAW,GAAI;AACxC,UAAM,IAAI,MAAM,2CAA2C;EAC7D;AACA,QAAM,cAAc,eAAe,QAAQ,QAAQ,QAAQ,SAAS;AAEpE,QAAM,CAAC,eAAe,YAAY,IAAI,MAAM,QAAQ,IAAI;IACtD,YAAY,iBAAiB,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAI,CAAE;KAC7D,aAAQ,YAAR,mBAAiB;GAClB;AACD,MAAI,CAAC,cAAc,WAAW;AAC5B,UAAM,IAAI,MAAM,uBAAuB;EACzC;AACA,QAAM,YAAY,cAAc;AAEhC,QAAM,iBAAiB,MAAM,QAAQ,cAAc,aAAa;IAC9D,cAAc,gBAAgB;IAC9B;IACA,IAAI,QAAQ;GACb;AAED,QAAM,gBAAwC,CAAA;AAC9C,OAAI,aAAQ,cAAR,mBAAmB,WAAW;AAChC,kBAAc,wBAAwB,IAAI,QAAQ,UAAU;EAC9D;AACA,OAAI,aAAQ,cAAR,mBAAmB,IAAI;AACzB,kBAAc,gBAAgB,IAAI,QAAQ,UAAU;EACtD;AAEA,QAAM,WAAW,MAAM,YAAY,oBAAmB,GAAI;IACxD,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,GAAG;;IAEL,MAAM,UAAU;MACd,MAAM;MACN,mBAAmB,eAAe;MAClC,cAAc,eAAe;MAC7B,sBAAsB,cAAc;MACpC,YAAY,eAAe;MAC3B,WAAW,eAAe;MAC1B,QAAQ,eAAe;MACvB,MAAM,QAAQ,GAAG;KAClB;GACF;AAED,QAAM,YAAY,MAAM,SAAS,KAAI;AAErC,MAAI,CAAC,aAAa,CAAC,UAAU,aAAa;AACxC,UAAM,IAAI,MACR,4BAA4B,UAAU,WAAW,eAAe,EAAE;EAEtE;AAGA,UAAM,aAAQ,YAAR,mBAAiB,wBAAwB,eAAe;AAG9D,SAAO;AACT;AAEA,SAAS,iBAAiB,WAAqB;AAC7C,SAAO,IAAG,uCAAW,OAAM,QAAQ,KAAI,oBAAI,KAAI,GAAG,YAAW,CAAE;AACjE;;;ACxLA,eAAsB,iBAAiB,MAKtC;AACC,QAAM,EAAE,QAAQ,OAAO,QAAQ,UAAS,IAAK;AAE7C,QAAM,UACJ,OAAO,WAAU,KAAO,MAAM,OAAO,QAAQ,EAAE,QAAQ,MAAK,CAAE;AAChE,QAAM,cAAc,eAAe,QAAQ,SAAS;AAEpD,QAAM,UAAU,OAAO,YAAW;AAChC,UAAM,OAAO,YAAY;MACvB,YAAY;MACZ,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;AACD,UAAM,MAAM,MAAM,YAChB,GAAG,IAAI,YAAY,QAAQ,OAAO,YAAY,MAAM,EAAE,EAAE;AAG1D,QAAI,CAAC,IAAI;AAAI,YAAM,IAAI,MAAM,uCAAuC;AAEpE,WAAQ,MAAM,IAAI,KAAI;EACxB,GAAE;AACF,QAAM,EAAE,UAAS,IAAK,MAAM,iBAAiB,EAAE,SAAS,QAAO,CAAE;AAEjE,QAAM,aAAa,OAAO,YAAW;AACnC,UAAM,OAAO,oBAAoB;MAC/B,YAAY;MACZ,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;AACD,UAAM,MAAM,MAAM,YAChB,GAAG,IAAI,cAAc,SAAS,YAAY,mBAAmB,OAAO,CAAC,IACrE;MACE,QAAQ;MACR,SAAS;QACP,gBAAgB;;MAElB,MAAM,UAAU;QACd;QACA;OACD;KACF;AAGH,QAAI,CAAC,IAAI;AAAI,YAAM,IAAI,MAAM,iCAAiC;AAE9D,WAAQ,MAAM,IAAI,KAAI;EACxB,GAAE;AACF,SAAO;AACT;;;AC5DA,eAAsB,kBAAkB,EACtC,QACA,SACA,QAAO,GAKR;AACC,QAAM,YAAY,MAAM,QAAQ,cAAa;AAC7C,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc,eAAe,QAAQ,SAAS;AAEpD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,0CAA0C;EAC5D;AAEA,QAAM,OAAO;IACX,SAAS,QAAQ;IACjB,SAAS,QAAQ;IACjB,OAAO,OAAO,QAAQ,KAAK;;AAG7B,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,6CACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;IAE1D,MAAM,UAAU,IAAI;GACrB;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,4BAA4B,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;EAExE;AAEA,QAAM,sBAAuB,MAAM,SAAS,KAAI;AAQhD,SAAO;AACT;;;ACpDA,eAAsB,YAAY,EAChC,QACA,SAAS,EAAE,SAAS,OAAO,iBAAiB,QAAO,GACnD,QAAO,GAUR;AACC,QAAM,YAAY,MAAM,QAAQ,cAAa;AAC7C,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc,eAAe,QAAQ,SAAS;AAEpD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,0CAA0C;EAC5D;AAEA,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,uCACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;IAE1D,MAAM,UAAU;MACd,gBAAgB;QACd;QACA;QACA;QACA;;KAEH;GACF;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,4BAA4B,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;EAExE;AAEA,QAAM,gBAAiB,MAAM,SAAS,KAAI;AAO1C,SAAO;AACT;;;ACvDA,eAAsB,gBAAgB,EACpC,QACA,SACA,QAAO,GAKR;AACC,QAAM,YAAY,MAAM,QAAQ,cAAa;AAC7C,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc,eAAe,QAAQ,SAAS;AAEpD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,8CAA8C;EAChE;AAEA,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,2CACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;IAE1D,MAAM,UAAU;MACd,oBAAoB;KACrB;GACF;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,gCAAgC,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;EAE5E;AAEA,QAAM,oBAAqB,MAAM,SAAS,KAAI;AAO9C,SAAO,kBAAkB;AAC3B;;;AC9CA,eAAsB,cAGpB,EACA,QACA,SACA,QAAO,GAKR;AACC,QAAM,YAAY,MAAM,QAAQ,cAAa;AAC7C,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc,eAAe,QAAQ,SAAS;AAEpD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AAEA,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,0CACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;IAE1D,MAAM,UAAU;MACd,GAAG;KACJ;GACF;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,+BAA+B,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;EAE3E;AAEA,QAAM,kBAAmB,MAAM,SAAS,KAAI;AAO5C,SAAO;AACT;;;ACTM,IAAO,gBAAP,MAAoB;EAMxB,YAAY,EACV,QACA,WACA,SACA,QAAO,GAMP;AAfM,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAaN,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,eAAe;EACtB;;;;;;EAOA,MAAM,gBAAgB,YAAqC;AACzD,UAAM,KAAK,aAAa,eAAe,WAAW,YAAY,YAAY;EAC5E;;;;;EAMA,MAAM,sBAAmB;AAnF3B;AAoFI,UAAM,QAAQ,MAAM,KAAK,aAAa,cAAa;AACnD,QAAI,CAAC,OAAO;AACV,aAAO,EAAE,QAAQ,aAAY;IAC/B;AAEA,UAAM,aAAa,MAAM,cAAc;MACrC,WAAW;MACX,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;AAED,QAAI,CAAC,YAAY;AACf,aAAO,EAAE,QAAQ,aAAY;IAC/B;AACA,UAAM,SAAS,WAAW,QAAQ,CAAC;AAEnC,UAAM,cAA2B;MAC/B,QAAO,gBAAW,eAAe,KAC/B,CAAC,YAAY,QAAQ,QAAQ,UAAU,MAAS,MAD3C,mBAEJ,QAAQ;MACX,cAAa,gBAAW,eAAe,KACrC,CAAC,YAAY,QAAQ,QAAQ,UAAU,MAAS,MADrC,mBAEV,QAAQ;MACX,cAAc,WAAW,MAAM;MAC/B,yBAAyB;;AAG3B,QAAI,CAAC,QAAQ;AACX,aAAO;QACL,QAAQ;QACR;;IAEJ;AAEA,WAAO;MACL,QAAQ;MACR,eAAe,OAAO;MACtB;MACA,SAAS,MAAM,KAAK,WAAU;;EAElC;;;;;EAMA,MAAM,aAAU;AACd,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,YAAY,KAAK;AAEvB,UAAM,mBAAmB,OAAO,OAA6B;AAC3D,YAAM,aAAa,aAAa;QAC9B;QACA,OAAO,eAAe,GAAG,OAAO;OACjC;AACD,YAAM,cAAuC;QAC3C,IAAI,GAAG,KAAK,WAAW,GAAG,EAAE,IAAI;QAChC,MAAM,GAAG;QACT,OAAO,QAAQ,GAAG,KAAK;QACvB,KAAK,QAAQ,GAAG,GAAG;QACnB,OACE,QAAQ,GAAG,KAAK,KAChB,MACE,MAAM,OACJ,uCAAoD,EACpD,KAAK,CAAC,EAAE,wBAAuB,MAC/B,wBAAwB,YAAY;UAClC,SAAS,WAAW,KAAK,OAAO;UAChC,UAAU;SACX,CAAC,CACH;QAEL,SAAS,MAAM,GAAG,OAAO;;AAG3B,UAAI,GAAG,qBAAqB,GAAG,kBAAkB,SAAS,GAAG;AAC3D,oBAAY,OAAO;AACnB,oBAAY,oBAAoB,GAAG;AACnC,oBAAY,eAAe,QAAQ,GAAG,YAAY;AAClD,oBAAY,uBAAuB,QAAQ,GAAG,oBAAoB;MACpE,WAAW,QAAQ,GAAG,YAAY,GAAG;AACnC,oBAAY,eAAe,QAAQ,GAAG,YAAY;AAClD,oBAAY,uBAAuB,QAAQ,GAAG,oBAAoB;AAClE,oBAAY,OAAO;MACrB,OAAO;AACL,oBAAY,WAAW,QAAQ,GAAG,QAAQ;AAC1C,oBAAY,OAAO;MACrB;AAEA,aAAO,gBAAuB;QAC5B;QACA;QACA,SAAS;OACV;IACH;AACA,WAAO;MACL,SAAS,WAAW,OAAO;MAC3B,MAAM,gBAAgB,IAAE;AACtB,YAAI,CAAC,GAAG,SAAS;AACf,gBAAM,IAAI,MAAM,gCAAgC;QAClD;AAEA,eAAO,iBAAiB;UACtB,SAAS,GAAG;UACZ,GAAG;SACJ;MACH;MACA,MAAM,gBAAgB,IAAE;AACtB,cAAM,aAAa,aAAa;UAC9B;UACA,OAAO,eAAe,GAAG,OAAO;SACjC;AACD,cAAM,WAAW,MAAM,iBAAiB,EAAE;AAE1C,cAAM,kBAAkB,MAAM,uBAC5B,YACA,QAAQ;AAGV,yBAAiB;UACf;UACA;UACA,SAAS,GAAG;UACZ,eAAe;UACf,YAAY;UACZ;UACA,iBAAiB,GAAG,MAAM;UAC1B,UAAU,GAAG;SACd;AAED,eAAO,EAAE,gBAAe;MAC1B;MACA,MAAM,YAAY,EAAE,SAAS,iBAAiB,QAAO,GAAE;AACrD,cAAM,kBAAkB,MAAK;AAC3B,cAAI,OAAO,YAAY,UAAU;AAC/B,mBAAO,EAAE,SAAS,OAAO,OAAO,iBAAiB,QAAO;UAC1D;AACA,iBAAO;YACL,SACE,OAAO,QAAQ,QAAQ,WACnB,QAAQ,MACR,WAAW,QAAQ,GAAG;YAC5B,OAAO;YACP;YACA;;QAEJ,GAAE;AAEF,cAAM,EAAE,UAAS,IAAK,MAAM,YAAmB;UAC7C;UACA,SAAS;UACT;SACD;AACD,eAAO;MACT;MACA,MAAM,cAAc,YAAU;AAC5B,cAAM,kBAAkB,eAAe,UAAU;AACjD,cAAM,EAAE,UAAS,IAAK,MAAM,cAAqB;UAC/C;UACA,SAAS;UACT;SACD;AAED,eAAO;MACT;MACA,MAAM,kBAAkB,SAAO;AAC7B,cAAM,gBAAgB,MAAM,kBAAyB;UACnD;UACA;UACA;SACD;AACD,eAAO;UACL,SAAS,WAAW,cAAc,OAAO;UACzC,SAAS,OAAO,SAAS,cAAc,OAAO;UAC9C,OAAO,OAAO,cAAc,KAAK;UACjC,GAAG,OAAO,cAAc,CAAC;UACzB,GAAG,OAAO,cAAc,CAAC;UACzB,SAAS,OAAO,SAAS,cAAc,OAAO;;MAElD;;EAEJ;;AAGF,SAAS,QAAQ,OAA2C;AAC1D,SAAO,UAAU,UAAa,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK;AAClE;;;ACjQA,IAAM,kBAAkB;EACtB,QAAQ;EACR,OAAO;EACP,QAAQ;EACR,iBAAiB;EACjB,aAAa;EACb,UAAU;EACV,KAAK;EACL,OAAO;EACP,QAAQ;EACR,SAAS;EACT,eAAe;;AAIjB,IAAM,iBAAiB,oBAAI,IAAG;AAMxB,IAAO,qBAAP,MAAyB;;;;EAW7B,YAAY,EACV,MACA,SACA,UACA,WACA,oBACA,cACA,UACA,UAAS,GACe;AAnBlB,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,4BAAA;;;;aAA2B;;AAC3B,WAAA,eAAA,MAAA,iBAAA;;;;;;AACE,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AAeR,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAErB,QAAI,OAAO,aAAa,aAAa;AACnC;IACF;AACA,gBAAY,aAAa,SAAS;AAElC,QAAI,SAAS,SAAS,eAAe,QAAQ;AAC7C,UAAM,WAAW,IAAI,IAAI,IAAI;AAQ7B,QAAI,CAAC,UAAU,OAAO,QAAQ,SAAS,MAAM;AAG3C,eAAS,SAAS,cAAc,QAAQ;AACxC,YAAM,qBAAqB;QACzB,GAAG;;AAEL,aAAO,OAAO,OAAO,OAAO,kBAAkB;AAC9C,aAAO,aAAa,MAAM,QAAQ;AAClC,aAAO,aAAa,iBAAiB,MAAM;AAC3C,gBAAU,YAAY,MAAM;AAE5B,aAAO,MAAM,SAAS;AAItB,YAAM,iBAAiB,CAAC,UAA4B;AAClD,YAAI,MAAM,KAAK,cAAc,mBAAmB;AAC9C,iBAAO,oBAAoB,WAAW,cAAc;AACpD,cAAI,CAAC,QAAQ;AACX,oBAAQ,KAAK,2BAA2B;AACxC;UACF;AACA,eAAK,oBAAoB,QAAQ,kBAAkB,EAAC;QACtD;MACF;AACA,aAAO,iBAAiB,WAAW,cAAc;IACnD;AACA,SAAK,SAAS;EAChB;;EAGU,MAAM,8BAA2B;AA5G7C;AA6GI,WAAO;MACL,YAAY,MAAM,KAAK,aAAa,cAAa;MACjD,mBAAmB,MAAM,KAAK,aAAa,eAAc;MACzD,cAAc,MAAM,KAAK,aAAa,gBAAe;MACrD,UAAU,KAAK;MACf,YAAW,UAAK,cAAL,mBAAgB;MAC3B,cAAa,UAAK,cAAL,mBAAgB;;EAEjC;;;;EAKA,oBACE,QACA,oBAA+B;AAE/B,WAAO,YAAW;AA9HtB;AA+HM,YAAM,UAAU,IAAI,eAAc;AAElC,YAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAO;AAEvC,gBAAQ,MAAM,YAAY,CAAC,UAAc;AACvC,gBAAM,EAAE,KAAI,IAAK;AACjB,kBAAQ,MAAM,MAAK;AACnB,cAAI,CAAC,KAAK,SAAS;AACjB,gBAAI,IAAI,MAAM,KAAK,KAAK,CAAC;UAC3B;AACA,yBAAe,IAAI,OAAO,KAAK,IAAI;AACnC,cAAI,oBAAoB;AACtB,+BAAkB;UACpB;AACA,cAAI,IAAI;QACV;MACF,CAAC;AAED,6CAAQ,kBAAR,mBAAuB,YACrB;QACE,WAAW;QACX,MAAM,MAAM,KAAK,4BAA2B;SAE9C,KAAK,eACL,CAAC,QAAQ,KAAK;AAGhB,YAAM;IACR;EACF;;;;EAKA,MAAM,KAAiB,EACrB,eACA,QACA,aAAa,MAAK,GAKnB;AAzKH;AA0KI,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MACR,gGAAgG;IAEpG;AACA,WAAO,CAAC,eAAe,IAAI,KAAK,OAAO,GAAG,GAAG;AAC3C,YAAM,MAAM,KAAK,2BAA2B,GAAI;IAClD;AACA,QAAI,YAAY;AACd,WAAK,OAAO,MAAM,UAAU;AAE5B,YAAM,MAAM,OAAQ,GAAI;IAC1B;AAEA,UAAM,UAAU,IAAI,eAAc;AAClC,UAAM,UAAU,IAAI,QAAoB,CAAC,KAAK,QAAO;AAEnD,cAAQ,MAAM,YAAY,OAAO,UAAc;AAC7C,cAAM,EAAE,KAAI,IAAK;AACjB,gBAAQ,MAAM,MAAK;AACnB,YAAI,YAAY;AAEd,gBAAM,MAAM,MAAM,GAAI;AACtB,cAAI,KAAK,QAAQ;AACf,iBAAK,OAAO,MAAM,UAAU;UAC9B;QACF;AACA,YAAI,CAAC,KAAK,SAAS;AACjB,cAAI,IAAI,MAAM,KAAK,KAAK,CAAC;QAC3B,OAAO;AACL,cAAI,KAAK,IAAI;QACf;MACF;IACF,CAAC;AAED,eAAK,OAAO,kBAAZ,mBAA2B,YACzB;MACE,WAAW;;MAEX,MAAM;QACJ,GAAG;QACH,GAAI,MAAM,KAAK,4BAA2B;;OAG9C,KAAK,eACL,CAAC,QAAQ,KAAK;AAEhB,WAAO;EACT;;;;;;EAOA,UAAO;AACL,QAAI,KAAK,QAAQ;AACf,qBAAe,OAAO,KAAK,OAAO,GAAG;IACvC;EACF;;;;AC5NI,IAAO,gCAAP,cAGI,mBAAqB;;;;EAI7B,YAAY,EACV,UACA,SACA,UAAS,GAKV;AACC,UAAM;MACJ,UAAU,4BAA2B,uCAAW,OAAM;MACtD,MAAM,4BAA4B;QAChC;QACA,MAAM;QACN;QACA;OACD,EAAE;MACH;MACA,WAAW,OAAO,aAAa,cAAc,SAAY,SAAS;MAClE,cAAc,IAAI,oBAAoB;QACpC,SAAS;QACT;QACA;OACD;MACD;MACA;KACD;AACD,SAAK,WAAW;AAChB,SAAK,YAAY;EACnB;;AAOF,SAAS,4BAA4B,EACnC,UACA,SACA,MACA,WACA,YAAW,GAOZ;AAhED;AAiEE,QAAM,iBAAiB,IAAI,IAAI,GAAG,IAAI,IAAI,OAAO;AACjD,MAAI,aAAa;AACf,eAAW,YAAY,OAAO,KAAK,WAAW,GAAG;AAC/C,qBAAe,aAAa,IAC1B,YACA,iBAAY,QAAQ,MAApB,mBAAuB,eAAc,EAAE;IAE3C;EACF;AACA,iBAAe,aAAa,IAAI,YAAY,QAAQ;AACpD,OAAI,uCAAW,eAAc,QAAW;AACtC,mBAAe,aAAa,IAAI,aAAa,UAAU,SAAS;EAClE;AACA,OAAI,uCAAW,QAAO,QAAW;AAC/B,mBAAe,aAAa,IAAI,eAAe,UAAU,EAAE;EAC7D;AACA,SAAO;AACT;AACA,IAAM,0BAA0B;;;ACzEhC,eAAsB,eAAe,EACnC,QACA,WACA,UAAS,GAKV;AACC,QAAM,cAAc,eAAe,QAAQ,SAAS;AACpD,QAAM,WAAW,MAAM,YACrB,GAAG,mBAAmB,aAAa,CAAC,mCACpC;IACE,QAAQ;IACR,SAAS;MACP,gBAAgB;MAChB,wBAAwB,OAAO;MAC/B,eAAe,gCAAgC,SAAS;;GAE3D;AAGH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,+BAA+B,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;EAE3E;AAEA,QAAM,EAAE,OAAM,IAAM,MAAM,SAAS,KAAI;AAIvC,SAAO;AACT;;;ACTM,IAAgB,gBAAhB,MAA6B;;;;;EAsBjC,YAAY,EACV,SACA,SACA,UACA,WACA,QACA,UAAS,GAQV;AA3BS,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AAGA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AAqBR,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,YAAY;EACnB;;;;EAgBA,MAAM,kBAAkB,EACtB,MAAK,GAC0C;AAC/C,UAAM,SAAS,MAAM,KAAK,aAAa,KAA6B;MAClE,eAAe;MACf,QAAQ,EAAE,MAAK;KAChB;AACD,WAAO;EACT;;;;;EAMA,MAAM,gBAAgB,EACpB,YAAW,GACkC;AAC7C,UAAM,SAAS,MAAM,KAAK,aAAa,KAA6B;MAClE,eAAe;MACf,QAAQ,EAAE,YAAW;KACtB;AACD,WAAO;EACT;;;;AC1GI,IAAO,YAAP,cAAyB,cAI9B;EACC,MAAM,wBAAqB;AACzB,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ;MACR,YAAY;KACb;EACH;;;;EAKS,MAAM,iBAAc;AAC3B,UAAM,KAAK,SAAQ;AACnB,UAAM,SAAS,MAAM,KAAK,sBAAqB;AAC/C,WAAO,KAAK,UAAU,MAAM;EAC9B;EAEA,MAAM,uBAAuB,EAC3B,MAAK,GAGN;AACC,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ,EAAE,MAAK;MACf,YAAY;KACb;EACH;;;;EAKS,MAAM,gBAAgB,EAC7B,MAAK,GAGN;AACC,UAAM,KAAK,SAAQ;AACnB,UAAM,SAAS,MAAM,KAAK,uBAAuB,EAAE,MAAK,CAAE;AAC1D,WAAO,KAAK,UAAU,MAAM;EAC9B;EAEA,MAAM,0BAA0B,EAC9B,eACA,IAAG,GACqC;AACxC,QAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAChD,YAAM,IAAI,MAAM,gDAAgD;IAClE;AAEA,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ,EAAE,eAAe,IAAG;KAC7B;EACH;;;;EAKS,MAAM,mBAAmB,EAChC,eACA,IAAG,GACqC;AACxC,QAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAChD,YAAM,IAAI,MAAM,gDAAgD;IAClE;AAEA,UAAM,KAAK,SAAQ;AACnB,UAAM,SAAS,MAAM,KAAK,0BAA0B,EAAE,eAAe,IAAG,CAAE;AAC1E,WAAO,KAAK,UAAU,MAAM;EAC9B;EAEA,MAAM,mCAAmC,EACvC,eACA,QAAO,GAC0C;AACjD,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ,EAAE,eAAe,QAAO;KACjC;EACH;;;;EAKS,MAAM,4BAA4B,EACzC,eACA,QAAO,GAC0C;AACjD,QAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAChD,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AAEA,UAAM,KAAK,SAAQ;AACnB,UAAM,SAAS,MAAM,KAAK,mCAAmC;MAC3D;MACA;KACD;AACD,WAAO,KAAK,UAAU,MAAM;EAC9B;EAEA,MAAM,yBAAyB,EAC7B,OACA,KACA,aAAY,GACqC;AACjD,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ,EAAE,OAAO,KAAK,aAAY;KACnC;EACH;;;;EAKS,MAAM,kBAAkB,EAC/B,OACA,KACA,aAAY,GACqC;AACjD,UAAM,SAAS,MAAM,KAAK,yBAAyB;MACjD;MACA;MACA;KACD;AACD,WAAO,KAAK,UAAU,MAAM;EAC9B;EAEA,MAAM,uBAAuB,EAC3B,aACA,KACA,aAAY,GACmC;AAC/C,WAAO,KAAK,aAAa,KAAiC;MACxD,eAAe;MACf,QAAQ,EAAE,aAAa,KAAK,aAAY;KACzC;EACH;;;;EAKS,MAAM,gBAAgB,EAC7B,aACA,KACA,aAAY,GACmC;AAC/C,UAAM,SAAS,MAAM,KAAK,uBAAuB;MAC/C;MACA;MACA;KACD;AACD,WAAO,KAAK,UAAU,MAAM;EAC9B;;;;ACjII,IAAO,OAAP,MAAW;;;;;EAcf,YAAY,EACV,QACA,SACA,eACA,WACA,SACA,aAAY,GAQb;AA3BS,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AACA,WAAA,eAAA,MAAA,eAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,iBAAA;;;;;;AAGF,WAAA,eAAA,MAAA,aAAA;;;;;;AAqBN,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,YAAY,IAAI,UAAU;MAC7B,WAAW,OAAO,WAAU;AAC1B,eAAO,KAAK,UAAU,MAAM;MAC9B;MACA,UAAU,YAAW;AACnB,cAAM,KAAK,SAAQ;MACrB;MACA;MACA;MACA;MACA;KACD;EACH;EAEQ,MAAM,WAAQ;AACpB,UAAM,KAAK,OAAM;EACnB;EAEQ,MAAM,UAAU,EACtB,aACA,cAAa,GACc;AAC3B,QAAI,YAAY,yBAAyB;AACvC,YAAM,KAAK,aAAa,eAAe,YAAY,YAAY;IACjE;AACA,UAAM,kBAAkB,MAAM,KAAK,cAAc;MAC/C;MACA;KACD;AACD,WAAO;EACT;EAEA,MAAM,mBACJ,WACA,cAAqB;AA1GzB;AA6GI,QAAI,UAAU,YAAY,iBAAiB,WAAW;AACpD,YAAM,KAAK,SAAQ;IACrB;AAEA,UAAM,OAAO,MAAM,cAAc;MAC/B,WAAW,UAAU,YAAY;MACjC,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;AACD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AAGA,QAAI,KAAK,QAAQ,SAAS,OAAK,UAAK,QAAQ,CAAC,MAAd,mBAAiB,UAAS,WAAW;AAClE,aAAO,KAAK,UAAU;QACpB,aAAa,UAAU;QACvB,eAAe;UACb,eAAe,KAAK,QAAQ,CAAC,EAAE;;OAElC;IACH;AAEA,QAAI,KAAK,QAAQ,WAAW,GAAG;AAE7B,YAAMA,UAAS,MAAM,eAAe;QAClC,WAAW,UAAU,YAAY;QACjC,QAAQ,KAAK;QACb,WAAW,KAAK;OACjB;AACD,aAAO,KAAK,UAAU;QACpB,aAAa,UAAU;QACvB,eAAe;UACb,eAAeA,QAAO;;OAEzB;IACH;AAGA,UAAM,SAAS,MAAM,KAAK,YAAY,KAAiC;MACrE,eAAe;MACf,QAAQ;QACN,aAAa,UAAU;QACvB;;KAEH;AACD,WAAO,KAAK,UAAU,MAAM;EAC9B;;;;;;;;;;;;;;;;EAiBA,MAAM,iBAAc;AAClB,WAAO,KAAK,UAAU,eAAc;EACtC;EACA,MAAM,wBAAqB;AACzB,WAAO,KAAK,UAAU,sBAAqB;EAC7C;;;;;;;;;;;;;;;;;;;EAoBA,MAAM,gBACJ,MAAiD;AAEjD,WAAO,KAAK,UAAU,gBAAgB,IAAI;EAC5C;EACA,MAAM,uBACJ,MAAwD;AAExD,WAAO,KAAK,UAAU,uBAAuB,IAAI;EACnD;;;;EAKA,MAAM,mBACJ,MAAoD;AAEpD,WAAO,KAAK,UAAU,mBAAmB,IAAI;EAC/C;EACA,MAAM,0BACJ,MAA2D;AAE3D,WAAO,KAAK,UAAU,0BAA0B,IAAI;EACtD;;;;EAKA,MAAM,4BACJ,MAA6D;AAE7D,WAAO,KAAK,UAAU,4BAA4B,IAAI;EACxD;EACA,MAAM,mCACJ,MAAoE;AAEpE,WAAO,KAAK,UAAU,mCAAmC,IAAI;EAC/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA,MAAM,kBAAkB,EACtB,MAAK,GAGH;AACF,WAAO,KAAK,UAAU,kBAAkB;MACtC;KACD;EACH;;;;EAKA,MAAM,gBAAgB,EACpB,YAAW,GAGT;AACF,WAAO,KAAK,UAAU,gBAAgB;MACpC;KACD;EACH;;;;;;;;;;EAWA,MAAM,kBAAkB,MAAmD;AACzE,UAAM,KAAK,SAAQ;AACnB,WAAO,KAAK,UAAU,kBAAkB,IAAI;EAC9C;EACA,MAAM,yBACJ,MAA0D;AAE1D,WAAO,KAAK,UAAU,yBAAyB,IAAI;EACrD;;;;EAKA,MAAM,gBAAgB,MAAiD;AACrE,UAAM,KAAK,SAAQ;AACnB,WAAO,KAAK,UAAU,gBAAgB,IAAI;EAC5C;EACA,MAAM,uBACJ,MAAwD;AAExD,WAAO,KAAK,UAAU,uBAAuB,IAAI;EACnD;;;;;;EAOA,MAAM,SAAM;AACV,UAAM,qBAAqB,MAAM,KAAK,aAAa,iBAAgB;AACnE,UAAM,iBAAiB,MAAM,KAAK,aAAa,mBAAkB;AAEjE,WAAO;MACL,SAAS,sBAAsB;;EAEnC;;;;AC5TK,IAAM,UAAU,OAAO,SAAwC;AACpE,QAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,QAAM,MAAM,YAAY,EAAE,QAAQ,WAAW,YAAY,KAAK,SAAQ,CAAE;AAExE,QAAM,UAAkC;IACtC,gBAAgB;IAChB,eAAe,OAAO;;AAGxB,MAAI,uCAAW,IAAI;AACjB,YAAQ,gBAAgB,IAAI,UAAU;EACxC;AAEA,MAAI,uCAAW,WAAW;AACxB,YAAQ,wBAAwB,IAAI,UAAU;EAChD;AAEA,QAAM,QAAQ,MAAK;AACjB,YAAQ,KAAK,UAAU;MACrB,KAAK;AACH,eAAO;UACL,OAAO,KAAK;;MAEhB,KAAK;AACH,eAAO;UACL,OAAO,KAAK;;IAElB;EACF,GAAE;AAEF,QAAM,WAAW,MAAM,MAAM,KAAK;IAChC,QAAQ;IACR;IACA,MAAM,UAAU,IAAI;GACrB;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,kCAAkC;EACpD;AAEA,SAAO,MAAM,SAAS,KAAI;AAC5B;AAKO,IAAM,YAAY,OACvB,SAIgD;AAChD,QAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,QAAM,MAAM,oBAAoB;IAC9B,YAAY,KAAK;IACjB,QAAQ,KAAK;IACb,WAAW,KAAK;GACjB;AAED,QAAM,UAAkC;IACtC,gBAAgB;IAChB,eAAe,OAAO;;AAGxB,MAAI,uCAAW,IAAI;AACjB,YAAQ,gBAAgB,IAAI,UAAU;EACxC;AAEA,MAAI,uCAAW,WAAW;AACxB,YAAQ,wBAAwB,IAAI,UAAU;EAChD;AAEA,QAAM,QAAQ,MAAK;AACjB,YAAQ,KAAK,UAAU;MACrB,KAAK;AACH,eAAO;UACL,OAAO,KAAK;UACZ,MAAM,KAAK;;MAEf,KAAK;AACH,eAAO;UACL,OAAO,KAAK;UACZ,MAAM,KAAK;;IAEjB;EACF,GAAE;AAEF,QAAM,WAAW,MAAM,MAAM,KAAK;IAChC,QAAQ;IACR;IACA,MAAM,UAAU,IAAI;GACrB;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,oCAAoC;EACtD;AAEA,SAAO,MAAM,SAAS,KAAI;AAC5B;;;AC3CM,IAAO,eAAP,MAAmB;;;;;EAYvB,YAAY,EACV,QACA,WACA,SACA,aAAY,GAMb;AArBM,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AACG,WAAA,eAAA,MAAA,wBAAA;;;;;;AAGA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAiBR,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAC5B,SAAK,eAAe;EACtB;;;;;;EAOA,MAAM,gBAAgB,YAAqC;AACzD,QAAI,WAAW,mBAAmB;AAChC,YAAM,KAAK,aAAa,gBACtB,WAAW,mBACX,WAAW,YAAY,YAAY,YAAY;IAEnD;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCA,MAAM,sBAAmB;AACvB,UAAM,aACJ,MAAM,KAAK,qBAAqB,KAAuC;MACrE,eAAe;MACf,QAAQ;KACT;AACH,QAAI,WAAW,WAAW,iCAAiC;AACzD,aAAO;QACL,QAAQ;QACR,GAAG,WAAW;QACd,SAAS,MAAM,KAAK,WAAU;;IAElC;AACA,QAAI,WAAW,WAAW,yBAAyB;AACjD,aAAO;QACL,QAAQ;QACR,GAAG,WAAW;;IAElB;AACA,QAAI,WAAW,WAAW,mCAAmC;AAC3D,aAAO;QACL,QAAQ;QACR,GAAG,WAAW;;IAElB;AAEA,WAAO,EAAE,QAAQ,WAAW,OAAM;EACpC;;;;;EAMA,MAAM,aAAU;AArLlB;AAsLI,UAAM,UAAU,KACb;AACH,UAAM,SAAS,KAAK;AACpB,UAAM,aAAY,UAAK,cAAL,mBAAgB;AAElC,UAAM,EAAE,QAAO,IAAK,MAAM,QAAQ,KAA2B;MAC3D,eAAe;MACf,QAAQ;KACT;AACD,UAAM,mBAAmB,OAAO,OAA6B;AAE3D,YAAM,cAAmC;QACvC,IAAI,GAAG,MAAM;QACb,MAAM,GAAG;QACT,OAAO,GAAG;QACV,UAAU,GAAG;QACb,OAAO,GAAG;QACV,SAAS,GAAG;;AAGd,UAAI,GAAG,cAAc;AAGnB,oBAAY,aAAa,GAAG;AAC5B,oBAAY,eAAe,GAAG;AAC9B,oBAAY,uBAAuB,GAAG;AACtC,oBAAY,OAAO;MACrB,OAAO;AACL,oBAAY,WAAW,GAAG;AAC1B,oBAAY,OAAO;MACrB;AACA,YAAM,UAAU,mBAAkB,EAAG;AACrC,YAAM,EAAE,kBAAiB,IACvB,MAAM,QAAQ,KAAgC;QAC5C,eAAe;QACf,QAAQ;UACN;UACA,SAAS,GAAG;UACZ;UACA,aAAa,WAAW,GAAG,OAAO,IAAI,OAAO;;;OAEhD;AACH,aAAO;IACT;AACA,WAAO;MACL,SAAS,WAAW,OAAO;MAC3B,MAAM,gBAAgB,IAAE;AACtB,YAAI,CAAC,GAAG,SAAS;AACf,gBAAM,IAAI,MAAM,gCAAgC;QAClD;AACA,eAAO,iBAAiB;UACtB,GAAG;UACH,SAAS,GAAG;SACb;MACH;MACA,MAAM,gBAAgB,IAAE;AACtB,cAAM,aAAa,aAAa;UAC9B;UACA,OAAO,eAAe,GAAG,OAAO;SACjC;AACD,cAAM,WAAW,MAAM,iBAAiB,EAAE;AAE1C,cAAM,kBAAkB,MAAM,uBAC5B,YACA,QAAQ;AAGV,yBAAiB;UACf;UACA,SAAS,GAAG;UACZ,eAAe;UACf,YAAY;UACZ;UACA,iBAAiB,GAAG,MAAM;UAC1B,UAAU,GAAG;SACd;AAED,eAAO,EAAE,gBAAe;MAC1B;MACA,MAAM,YAAY,EAAE,QAAO,GAAE;AAE3B,cAAM,kBAAkB,MAAK;AAC3B,cAAI,OAAO,YAAY,UAAU;AAC/B,mBAAO;UACT;AACA,cAAI,QAAQ,eAAe,YAAY;AACrC,mBAAO,QAAQ;UACjB;AACA,iBAAO,YAAY,QAAQ,GAAG;QAChC,GAAE;AAEF,cAAM,EAAE,cAAa,IAAK,MAAM,QAAQ,KAA4B;UAClE,eAAe;UACf,QAAQ;;YAEN,SAAS;;YACT;YACA,SAAS;;;SAEZ;AACD,eAAO;MACT;MACA,MAAM,cAAc,YAAU;AA5RpC,YAAAC;AA6RQ,cAAM,kBAAkB,eAAe,UAAU;AAGjD,aAAIA,MAAA,gBAAgB,UAAhB,gBAAAA,IAAuB,cAAc;AACvC,0BAAgB,MAAM,eAAe;QACvC;AACA,cAAM,SAAS,gBAAgB;AAC/B,cAAM,UAAU,iCAAQ;AACxB,cAAM,qBAAoB,iCAAQ,qBAC9B,EAAE,mBAAmB,iCAAQ,kBAAiB,IAC9C,CAAA;AACJ,cAAM,aAAa;UACjB,GAAG;UACH,MAAM,iCAAQ;UACd,SAAS,iCAAQ;;AAGnB,YAAI,SAAS;AACV,qBAAuC,UAAU;QACpD;AAEA,cAAM,UAAU,mBAAkB,EAAG;AACrC,cAAM,EAAE,gBAAe,IACrB,MAAM,QAAQ,KAAgC;UAC5C,eAAe;UACf,QAAQ;YACN,QAAQ;YACR,OACE,gBAAgB;YAClB,SACE,gBAAgB;YAClB,SAAS,OAAO,SAAS,OAAO,WAAW,CAAC,EAAE,SAAQ,CAAE;YACxD;YACA,aAAa,WAAW,OAAO,IAAI,OAAO;;;SAE7C;AACH,eAAO;MACT;;EAEJ;;;;ACtRI,IAAO,oBAAP,MAAwB;EAapB,sBAAsB,UAAgB;AAC5C,QAAI,SAAS,QAAQ,GAAG,IAAI,KAAK,SAAS,WAAW,IAAI;AACvD,aAAO;IACT;AACA,WAAO;EACT;;;;;;EAOA,YAAY,EACV,QACA,eACA,WACA,eACA,QAAO,GACoB;AA9BrB,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AAEA,WAAA,eAAA,MAAA,UAAA;;;;;;AAIR,WAAA,eAAA,MAAA,QAAA;;;;;;AACQ,WAAA,eAAA,MAAA,iBAAA;;;;;;AAqBN,QAAI,KAAK,sBAAsB,OAAO,QAAQ,GAAG;AAC/C,YAAM,IAAI,MACR,wGAAwG;IAE5G;AACA,UAAM,UAAU,mBAAmB,aAAa;AAChD,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,UAAU,IAAI,oBAAoB;MACrC,SAAS,WAAW,kBAAiB;MACrC,UAAU,OAAO;MACjB;KACD;AACD,SAAK,UAAU,IAAI,8BAA8B;MAC/C,UAAU,OAAO;MACjB;MACA;KACD;AAED,SAAK,OAAO,IAAI,KAAK;MACnB;MACA,SAAS,KAAK;MACd;MACA,cAAc,KAAK;MACnB;MACA,eAAe,OAAO,eAAc;AAClC,uDAAgB;AAEhB,YAAI,WAAW,YAAY,YAAY,eAAe,WAAW;AAE/D,gBAAM,SAAS,MAAM,KAAK,QAAQ,KAAc;YAC9C,eAAe;YACf,QAAQ;cACN,aAAa,WAAW;;WAE3B;AACD,cAAI,CAAC,QAAQ;AACX,oBAAQ,KACN,kFAAkF;UAEtF;QACF;AAEA,aAAK,SAAS,MAAM,KAAK,iBACvB,WAAW,YAAY,YAAY;AAGrC,YAAI,CAAC,KAAK,QAAQ;AAChB,gBAAM,IAAI,MAAM,6BAA6B;QAC/C;AAEA,cAAM,oBACJ,uBAAuB,WAAW,gBAC9B,WAAW,cAAc,oBACzB;AAEN,cAAM,KAAK,OAAO,gBAAgB;UAChC,aAAa,WAAW;UACxB;SACD;AAED,YAAI,KAAK,kBAAkB,cAAc;AACvC,gBAAM,KAAK,QAAQ,KAAK;YACtB,eAAe;YACf,QAAQ;cACN,WAAW,uCAAW;cACtB,aAAa,uCAAW;cACxB,UAAU,KAAK,OAAO;;cAEtB,mBACE,uBAAuB,WAAW,gBAC9B,WAAW,cAAc,oBACzB;cACN,cAAc,WAAW,YAAY,YAAY;cACjD,YAAY,WAAW,YAAY;;WAEtC;QACH;AAEA,eAAO;UACL,MAAM;YACJ,QAAQ;YACR,aAAa,WAAW,YAAY;YACpC,SAAS,MAAM,KAAK,OAAO,WAAU;YACrC,eAAe,WAAW,cAAc;;;MAG9C;KACD;EACH;EAEA,MAAM,iBAAiB,WAAkB;AA3K3C;AA4KI,UAAM,kBAAkB,MAAM,KAAK,QAAQ,cAAa;AACxD,QAAI,CAAC,aAAa,oBAAoB,MAAM;AAC1C,YAAM,IAAI,MACR,gFAAgF;IAEpF;AAEA,UAAM,OAAO,MAAM,cAAc;MAC/B,WAAW,aAAc;MACzB,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;AAED,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AACA,QAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,YAAM,IAAI,MACR,0EAA0E;IAE9E;AAEA,UAAI,UAAK,QAAQ,CAAC,MAAd,mBAAiB,UAAS,WAAW;AACvC,aAAO,IAAI,cAAc;QACvB,QAAQ,KAAK;QACb,WAAW,KAAK;QAChB,SAAS,KAAK,QAAQ,CAAC,EAAE;QACzB,SAAS,KAAK;OACf;IACH;AAEA,WAAO,IAAI,aAAa;MACtB,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB,SAAS,KAAK;MACd,cAAc,KAAK;KACpB;EACH;;;;;;;;;;;;;;;;;;;;;;;;EAyBA,MAAM,UAAO;AAEX,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,iBAAiB,MAAM,KAAK,QAAQ,cAAa;AACvD,UAAI,CAAC,gBAAgB;AACnB,eAAO,EAAE,QAAQ,aAAY;MAC/B;AACA,WAAK,SAAS,MAAM,KAAK,iBAAiB,cAAc;IAC1D;AACA,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,wBAAwB;IAC1C;AACA,WAAO,MAAM,KAAK,OAAO,oBAAmB;EAC9C;EAEA,aAAU;AACR,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,wBAAwB;IAC1C;AACA,WAAO,KAAK,OAAO,WAAU;EAC/B;EAEA,MAAM,gBAAgB,MAA+B;AACnD,WAAO,QAAQ;MACb,GAAG;MACH,QAAQ,KAAK;MACb,WAAW,KAAK;KACjB;EACH;EAEA,MAAM,yBACJ,UACA,MACA,aAAoB;AAEpB,WAAO,uBAAuB;MAC5B,YAAY;MACZ,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB;MACA;KACD;EACH;EAEA,MAAM,mBACJ,YACA,cAAqB;AAErB,WAAO,KAAK,KAAK,mBAAmB,YAAY,YAAY;EAC9D;;;;EAKA,MAAM,aACJ,MAAoD;AAEpD,UAAM,WAAW,KAAK;AACtB,YAAQ,UAAU;MAChB,KAAK;AACH,eAAO,UAAU;UACf,GAAG;UACH,QAAQ,KAAK;UACb,WAAW,KAAK;SACjB;MACH,KAAK;AACH,eAAO,UAAU;UACf,GAAG;UACH,QAAQ,KAAK;UACb,WAAW,KAAK;SACjB;MACH,KAAK,iBAAiB;AACpB,eAAO,aAAa;UAClB,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,WAAW,KAAK;SACjB;MACH;MACA,KAAK;AACH,eAAO,UAAU;UACf,KAAK,KAAK;UACV,QAAQ,KAAK;UACb,WAAW,KAAK;SACjB;MACH,KAAK,WAAW;AACd,eAAO,KAAK,YAAY,IAAI;MAC9B;MACA,KAAK,6BAA6B;AAChC,eAAO,KAAK,KAAK,uBAAuB;UACtC,OAAO,KAAK;SACb;MACH;MACA,KAAK,UAAU;AACb,eAAO,KAAK,KAAK,sBAAqB;MACxC;MACA,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK,WAAW;AACd,eAAO,eAAe;UACpB,YAAY;UACZ,QAAQ,KAAK;UACb,WAAW,KAAK;UAChB,mBAAmB,KAAK;UACxB,cAAc,KAAK;SACpB;MACH;MACA,KAAK,SAAS;AACZ,eAAO,kBAAkB;UACvB,QAAQ,KAAK;UACb,WAAW,KAAK;UAChB,SAAS;SACV;MACH;MACA,KAAK,WAAW;AACd,eAAO,oBAAoB;UACzB,QAAQ,KAAK;UACb,cAAc,KAAK;UACnB,WAAW,KAAK;SACjB;MACH;MACA,KAAK,UAAU;AACb,eAAO,iBAAiB;UACtB,WAAW,KAAK;UAChB,QAAQ,KAAK;UACb,QAAQ,KAAK;UACb,OAAO,KAAK;SACb;MACH;IACF;EACF;;;;EAKA,MAAM,QACJ,MAAoD;AAEpD,UAAM,WAAW,KAAK;AACtB,YAAQ,UAAU;MAChB,KAAK;MACL,KAAK,OAAO;AACV,cAAM,YAAY,MAAM,KAAK,aAAa,IAAI;AAC9C,eAAO,MAAM,KAAK,mBAAmB,WAAW,KAAK,aAAa;MACpE;MACA,KAAK,6BAA6B;AAChC,eAAO,KAAK,KAAK,gBAAgB;UAC/B,OAAO,KAAK;SACb;MACH;MACA,KAAK,UAAU;AACb,eAAO,KAAK,KAAK,eAAc;MACjC;MACA,KAAK,WAAW;AACd,cAAM,YAAY,MAAM,KAAK,YAAY,IAAI;AAC7C,eAAO,KAAK,mBAAmB,SAAS;MAC1C;MACA,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK,WAAW;AACd,cAAM,YAAY,MAAM,KAAK,aAAa,IAAI;AAC9C,eAAO,MAAM,KAAK,KAAK,mBAAmB,SAAS;MACrD;MAEA;AACE,0BAAkB,QAAQ;IAC9B;EACF;EAEA,MAAM,SAAM;AACV,WAAO,MAAM,KAAK,KAAK,OAAM;EAC/B;EAEQ,MAAM,YACZ,MAA8D;AAE9D,UAAM,EAAE,iBAAgB,IAAK,MAAM,OAAO,wBAAoB;AAC9D,UAAM,EAAE,aAAa,uBAAuB,KAAI,IAAK;AACrD,UAAM,gBAAgB,IAAI,iBAAgB;AAC1C,UAAM,UAAU,KAAK;AACrB,QAAI,KAAK,SAAS,WAAW;AAC3B,aAAO,gBAAgB;QACrB,QAAQ,KAAK;QACb,WAAW,KAAK;QAChB,UAAU;QACV;QACA,SAAS,uBAAuB,UAAU;QAC1C,IAAI;UACF,IAAI,KAAK,iBAAiB,OAAO,SAAS;UAC1C,MAAM,KAAK,iBAAiB,OAAO,SAAS;;OAE/C;IACH;AACA,WAAO,iBAAiB;MACtB,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB;MACA,SAAS,uBAAuB,UAAU;MAC1C,IAAI;QACF,IAAI,KAAK,iBAAiB,OAAO,SAAS;QAC1C,MAAM,KAAK,iBAAiB,OAAO,SAAS;;KAE/C;EACH;EAEA,MAAM,YAAY,MAAkB;AAClC,UAAM,EAAE,YAAW,IAAK,MAAM,KAAK,aAAa,IAAI;AACpD,WAAO,MAAM,YAAY;MACvB,QAAQ,KAAK;MACb,aAAa,YAAY;MACzB,SAAS,KAAK;MACd,WAAW,KAAK,aAAa,KAAK;KACnC;EACH;EAEA,MAAM,cAAc,SAAgB;AAClC,WAAO,MAAM,cAAc;MACzB,QAAQ,KAAK;MACb,SAAS,KAAK;MACd,WAAW,KAAK;MAChB,iBAAiB;KAClB;EACH;EAEA,MAAM,cAAW;AACf,WAAO,0BAA0B;MAC/B,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB,SAAS,KAAK;KACf;EACH;;AAGF,SAAS,kBAAkB,GAAU,SAAgB;AACnD,QAAM,IAAI,MAAM,WAAW,kBAAkB,CAAC,EAAE;AAClD;AAEA,SAAS,oBAAiB;AACxB,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,WAAO;EACT;AAEA,SAAO;AACT;", "names": ["result", "_a"]}