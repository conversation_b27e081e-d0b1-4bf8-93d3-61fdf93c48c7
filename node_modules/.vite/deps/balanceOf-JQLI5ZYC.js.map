{"version": 3, "sources": ["../../thirdweb/src/extensions/erc1155/__generated__/IERC1155/read/balanceOf.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"balanceOf\" function.\n */\nexport type BalanceOfParams = {\n  owner: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"_owner\" }>;\n  tokenId: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"tokenId\" }>;\n};\n\nexport const FN_SELECTOR = \"0x00fdd58e\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"_owner\",\n  },\n  {\n    type: \"uint256\",\n    name: \"tokenId\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n  },\n] as const;\n\n/**\n * Checks if the `balanceOf` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `balanceOf` method is supported.\n * @extension ERC1155\n * @example\n * ```ts\n * import { isBalanceOfSupported } from \"thirdweb/extensions/erc1155\";\n * const supported = isBalanceOfSupported([\"0x...\"]);\n * ```\n */\nexport function isBalanceOfSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"balanceOf\" function.\n * @param options - The options for the balanceOf function.\n * @returns The encoded ABI parameters.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeBalanceOfParams } from \"thirdweb/extensions/erc1155\";\n * const result = encodeBalanceOfParams({\n *  owner: ...,\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeBalanceOfParams(options: BalanceOfParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.owner, options.tokenId]);\n}\n\n/**\n * Encodes the \"balanceOf\" function into a Hex string with its parameters.\n * @param options - The options for the balanceOf function.\n * @returns The encoded hexadecimal string.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeBalanceOf } from \"thirdweb/extensions/erc1155\";\n * const result = encodeBalanceOf({\n *  owner: ...,\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeBalanceOf(options: BalanceOfParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeBalanceOfParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the balanceOf function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC1155\n * @example\n * ```ts\n * import { decodeBalanceOfResult } from \"thirdweb/extensions/erc1155\";\n * const result = decodeBalanceOfResultResult(\"...\");\n * ```\n */\nexport function decodeBalanceOfResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"balanceOf\" function on the contract.\n * @param options - The options for the balanceOf function.\n * @returns The parsed result of the function call.\n * @extension ERC1155\n * @example\n * ```ts\n * import { balanceOf } from \"thirdweb/extensions/erc1155\";\n *\n * const result = await balanceOf({\n *  contract,\n *  owner: ...,\n *  tokenId: ...,\n * });\n *\n * ```\n */\nexport async function balanceOf(\n  options: BaseTransactionOptions<BalanceOfParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.owner, options.tokenId],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,qBAAqB,oBAA4B;AAC/D,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAgBM,SAAU,sBAAsB,SAAwB;AAC5D,SAAO,oBAAoB,WAAW,CAAC,QAAQ,OAAO,QAAQ,OAAO,CAAC;AACxE;AAgBM,SAAU,gBAAgB,SAAwB;AAGtD,SAAQ,cACN,sBAAsB,OAAO,EAAE,MAC7B,CAAC;AAEP;AAaM,SAAU,sBAAsB,QAAW;AAC/C,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAmBA,eAAsB,UACpB,SAAgD;AAEhD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO,QAAQ,OAAO;GACxC;AACH;", "names": []}