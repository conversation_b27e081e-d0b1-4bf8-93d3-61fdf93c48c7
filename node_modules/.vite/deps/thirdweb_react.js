import {
  getListing
} from "./chunk-ZFCPKXTP.js";
import {
  AccountAddress,
  AccountAvatar,
  AccountBalance,
  AccountBlobbie,
  AccountName,
  AccountProvider,
  AutoConnect,
  Blobbie,
  BuyScreen,
  ChainIcon,
  ChainName,
  ChainProvider,
  CoinsIcon,
  ConnectButton,
  ConnectModalContent,
  ConnectModal_default,
  CopyIcon,
  DynamicHeight,
  LazyBuyScreen,
  MediaRenderer,
  Modal,
  SetRootElementContext,
  TokenSymbol,
  WalletRow,
  canFitWideModal,
  defaultTokens,
  formatNumber,
  formatTokenBalance,
  getConnectLocale,
  getDefaultToken,
  getFunctionId,
  getTokenBalance,
  invalidateWalletBalance,
  isNativeToken,
  trackPayEvent,
  useActiveWalletChain,
  useActiveWalletConnectionStatus,
  useAdminWallet,
  useAutoConnect,
  useBuyWithCryptoQuote,
  useBuyWithCryptoStatus,
  useBuyWithFiatQuote,
  useBuyWithFiatStatus,
  useChainExplorers,
  useChainMetadata,
  useClipboard,
  useConnect,
  useConnectLocale,
  useConnectedWallets,
  useDisconnect,
  useNetworkSwitcherModal,
  usePreloadWalletProviders,
  useProfiles,
  useSetActiveWallet,
  useSetActiveWalletConnectionStatus,
  useSiweAuth,
  useSwitchActiveWalletChain,
  useTransactionCostAndData,
  useUnlinkProfile,
  useWaitForReceipt,
  useWalletDetailsModal
} from "./chunk-WVR6PXEX.js";
import {
  isERC1155,
  isERC721
} from "./chunk-WXVMGE2S.js";
import "./chunk-V4FF7QWG.js";
import {
  AccentFailIcon,
  ErrorState,
  QRCode
} from "./chunk-YOU65ZSC.js";
import {
  LoadingScreen,
  modalMaxWidthCompact,
  modalMaxWidthWide,
  reservedScreens,
  useActiveAccount,
  useSetupScreen,
  wideModalMaxHeight
} from "./chunk-DL2RM5LK.js";
import "./chunk-JCBJO7NI.js";
import "./chunk-N62JRGB3.js";
import {
  Spinner,
  WalletUIStatesProvider
} from "./chunk-QCLEFBDF.js";
import {
  Button,
  ButtonLink,
  CheckCircledIcon,
  ConnectionManagerCtx,
  Container,
  CustomThemeProvider,
  ExternalLinkIcon,
  ModalHeader,
  Skeleton,
  Spacer,
  StyledButton,
  StyledDiv,
  Text,
  WalletImage,
  WalletProvider,
  darkTheme,
  fontSize,
  getLastAuthProvider,
  getSocialIcon,
  hasSponsoredTransactionsEnabled,
  iconSize,
  keyframes,
  lightTheme,
  radius,
  spacing,
  useActiveWallet,
  useConnectionManager,
  useConnectionManagerCtx,
  useCustomTheme,
  useEnsAvatar,
  useEnsName,
  useSocialProfiles,
  useWalletBalance,
  useWalletContext,
  useWalletIcon,
  useWalletImage,
  useWalletInfo
} from "./chunk-FWLKKSQO.js";
import "./chunk-X3OF5GFG.js";
import {
  isWalletConnect
} from "./chunk-CS6XQ6DP.js";
import {
  formatExplorerTxUrl
} from "./chunk-HBZPOO7D.js";
import "./chunk-GYYHXJ3R.js";
import "./chunk-XMUJEFDX.js";
import {
  getNFT as getNFT2
} from "./chunk-23GJSJ6Q.js";
import "./chunk-DKG2WVWA.js";
import {
  getNFT
} from "./chunk-FRVFGGG3.js";
import "./chunk-FVFUN77Y.js";
import "./chunk-CYP47DRD.js";
import "./chunk-FTGVVEWF.js";
import "./chunk-IJLYJIJS.js";
import {
  getContractMetadata
} from "./chunk-GQC5QNIL.js";
import "./chunk-PPFWNLQY.js";
import "./chunk-DNDNTN4Q.js";
import {
  name,
  symbol
} from "./chunk-LDVL4EMN.js";
import "./chunk-3PSI24KF.js";
import "./chunk-43F4DZXD.js";
import {
  getBuyWithCryptoHistory,
  getContractEvents,
  watchContractEvents
} from "./chunk-LWO5BYVJ.js";
import {
  getBuyWithCryptoQuote,
  getPayBuyHistoryEndpoint,
  getPayBuyWithFiatHistoryEndpoint,
  routes
} from "./chunk-JCUZZQHJ.js";
import "./chunk-AD5IFQ7G.js";
import "./chunk-IVKLLG7V.js";
import {
  isBaseTransactionOptions,
  simulateTransaction
} from "./chunk-4KIC5UIL.js";
import {
  sendAndConfirmTransaction,
  sendBatchTransaction
} from "./chunk-7LVBRAYW.js";
import {
  eth_blockNumber,
  waitForReceipt,
  watchBlockNumber
} from "./chunk-LU6EOOP6.js";
import "./chunk-QWTK625L.js";
import {
  isObjectWithKeys
} from "./chunk-WWY7S4YD.js";
import "./chunk-V4XDO7Z5.js";
import "./chunk-H5DL3D6Z.js";
import "./chunk-5PGDMUKH.js";
import {
  createConnectionManager
} from "./chunk-SNFNA35G.js";
import "./chunk-YSGJZKRF.js";
import "./chunk-SNLJIEF2.js";
import "./chunk-TR45YI6T.js";
import {
  getDefaultWallets
} from "./chunk-GWKPF6JP.js";
import {
  linkProfile
} from "./chunk-TW2EKXAY.js";
import {
  getWalletBalance
} from "./chunk-6PLZ73QQ.js";
import "./chunk-7N62H5IB.js";
import "./chunk-OJRBY574.js";
import "./chunk-TJPCO3UF.js";
import "./chunk-FUPOJN5U.js";
import "./chunk-NCVQ56IM.js";
import "./chunk-67YIWUOQ.js";
import {
  getWalletInfo
} from "./chunk-MZEIIHE4.js";
import {
  isInAppWallet
} from "./chunk-RZIS2W4Z.js";
import {
  isSmartWallet
} from "./chunk-QXWT2LHJ.js";
import "./chunk-AKEHYN4S.js";
import {
  ClientScopedStorage
} from "./chunk-FUW7UPWG.js";
import {
  getInjectedProvider
} from "./chunk-5BDJHUZI.js";
import "./chunk-SDE6XNUJ.js";
import {
  isEcosystemWallet
} from "./chunk-QDEEV5NE.js";
import "./chunk-34X5PL56.js";
import {
  isCoinbaseSDKWallet
} from "./chunk-QXYVYYMB.js";
import "./chunk-KZXPW4P4.js";
import "./chunk-IA4CMUWX.js";
import "./chunk-ZVAAMTP5.js";
import "./chunk-R4YODVWY.js";
import {
  webLocalStorage
} from "./chunk-G4H2UJKK.js";
import "./chunk-LWBFBP2R.js";
import "./chunk-LJJPSJ3C.js";
import "./chunk-K43SHKO4.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import "./chunk-3WB2EUD3.js";
import "./chunk-4XJYATTE.js";
import "./chunk-RKW6PCRI.js";
import "./chunk-OHXFGHHA.js";
import "./chunk-FEE2SXO2.js";
import "./chunk-RJGRI3EY.js";
import "./chunk-35SUGYXY.js";
import "./chunk-X2XBGGU2.js";
import "./chunk-3S7RRRP4.js";
import "./chunk-S6FQMGF4.js";
import "./chunk-QWTIS6CA.js";
import {
  NATIVE_TOKEN_ADDRESS,
  isNativeTokenAddress
} from "./chunk-YCZ3YGMG.js";
import "./chunk-54TJVF2D.js";
import "./chunk-V2MC2I4R.js";
import "./chunk-FSZ5BSZ7.js";
import "./chunk-4L6F72MM.js";
import "./chunk-M5BKQRCL.js";
import "./chunk-CMXLKATA.js";
import "./chunk-LM2644TQ.js";
import {
  prepareContractCall
} from "./chunk-3GH3RYOE.js";
import "./chunk-DUYIIKDP.js";
import {
  readContract
} from "./chunk-OIHZCUZQ.js";
import {
  estimateGasCost,
  getTransactionGasCost
} from "./chunk-PLFYO732.js";
import "./chunk-2M7BVURO.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-7PVLC7PF.js";
import "./chunk-MBPUCXB7.js";
import "./chunk-ITS5WTXH.js";
import "./chunk-RF7FDP72.js";
import {
  QueryClient,
  QueryClientProvider,
  queryOptions,
  replaceEqualDeep,
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-H6ZS2PAJ.js";
import "./chunk-JMHMJ42H.js";
import {
  sendTransaction
} from "./chunk-K4XAEHXR.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-R462U44Z.js";
import "./chunk-WLZN2VO2.js";
import {
  encode
} from "./chunk-DVJU3TKU.js";
import {
  estimateGas,
  eth_getBlockByNumber,
  resolvePromisedValue
} from "./chunk-LFHG7EDC.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import {
  toTokens,
  toUnits
} from "./chunk-HAADYJEF.js";
import {
  resolveScheme
} from "./chunk-FYKFURXC.js";
import "./chunk-6NM2KW2J.js";
import {
  randomBytesHex
} from "./chunk-N3KXRWQX.js";
import "./chunk-QGXAPRFG.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-HGMV3JDR.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  getAddress,
  shortenAddress
} from "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import {
  hexToBigInt,
  hexToNumber,
  numberToHex
} from "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-4LB33PYO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import {
  getChainMetadata
} from "./chunk-KQKMGIQ6.js";
import {
  withCache
} from "./chunk-MTFDOOBS.js";
import {
  getClientFetch
} from "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-XYVGN7BE.js";
import "./chunk-OPXRDTDC.js";
import "./chunk-GXYGOJMY.js";
import "./chunk-SKRUXVEK.js";
import {
  require_jsx_runtime
} from "./chunk-67GKA7K4.js";
import {
  require_react
} from "./chunk-XDVZA6Z7.js";
import {
  __toESM
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/ui/ConnectWallet/Modal/ConnectEmbed.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/react/core/hooks/wallets/useIsAutoConnecting.js
var import_react = __toESM(require_react(), 1);
function useIsAutoConnecting() {
  const manager = useConnectionManagerCtx("useIsAutoConnecting");
  const store = manager.isAutoConnecting;
  return (0, import_react.useSyncExternalStore)(store.subscribe, store.getValue, store.getValue);
}

// node_modules/thirdweb/dist/esm/react/web/ui/ConnectWallet/Modal/ConnectEmbed.js
function ConnectEmbed(props) {
  var _a, _b, _c;
  const activeWallet = useActiveWallet();
  const activeAccount = useActiveAccount();
  const siweAuth = useSiweAuth(activeWallet, activeAccount, props.auth);
  const show = !activeAccount || siweAuth.requiresAuth && !siweAuth.isLoggedIn;
  const connectionManager = useConnectionManager();
  (0, import_react2.useEffect)(() => {
    if (props.chain) {
      connectionManager.defineChains([props.chain]);
    }
  }, [props.chain, connectionManager]);
  (0, import_react2.useEffect)(() => {
    if (props.chains) {
      connectionManager.defineChains(props.chains);
    }
  }, [props.chains, connectionManager]);
  const wallets = (0, import_react2.useMemo)(() => props.wallets || getDefaultWallets({
    appMetadata: props.appMetadata,
    chains: props.chains
  }), [props.wallets, props.appMetadata, props.chains]);
  const localeId = props.locale || "en_US";
  const localeQuery = useConnectLocale(localeId);
  usePreloadWalletProviders({
    wallets
  });
  const modalSize = (0, import_react2.useMemo)(() => {
    return !canFitWideModal() || wallets.length === 1 ? "compact" : props.modalSize || "compact";
  }, [wallets.length, props.modalSize]);
  const meta = (0, import_react2.useMemo)(() => {
    return {
      privacyPolicyUrl: props.privacyPolicyUrl,
      showThirdwebBranding: props.showThirdwebBranding !== false,
      termsOfServiceUrl: props.termsOfServiceUrl,
      title: void 0,
      titleIconUrl: void 0,
      requireApproval: props.requireApproval
    };
  }, [
    props.privacyPolicyUrl,
    props.showThirdwebBranding,
    props.termsOfServiceUrl,
    props.requireApproval
  ]);
  const preferredChain = ((_a = props.accountAbstraction) == null ? void 0 : _a.chain) || props.chain || ((_b = props.chains) == null ? void 0 : _b[0]);
  const autoConnectComp = props.autoConnect !== false && (0, import_jsx_runtime.jsx)(AutoConnect, { chain: preferredChain, appMetadata: props.appMetadata, client: props.client, siweAuth, wallets, accountAbstraction: props.accountAbstraction, timeout: typeof props.autoConnect === "boolean" ? void 0 : (_c = props.autoConnect) == null ? void 0 : _c.timeout, onConnect: props.onConnect });
  if (show) {
    if (!localeQuery.data) {
      return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [autoConnectComp, (0, import_jsx_runtime.jsx)(CustomThemeProvider, { theme: props.theme, children: (0, import_jsx_runtime.jsx)(EmbedContainer, { modalSize, children: (0, import_jsx_runtime.jsx)(LoadingScreen, {}) }) })] });
    }
    return (0, import_jsx_runtime.jsxs)(WalletUIStatesProvider, { theme: props.theme, isOpen: true, children: [(0, import_jsx_runtime.jsx)(ConnectEmbedContent, { auth: props.auth, accountAbstraction: props.accountAbstraction, chain: preferredChain, chains: props.chains, client: props.client, connectLocale: localeQuery.data, size: modalSize, meta, header: props.header, localeId: props.locale || "en_US", onConnect: props.onConnect, recommendedWallets: props.recommendedWallets, showAllWallets: props.showAllWallets, walletConnect: props.walletConnect, wallets, className: props.className, modalSize, style: props.style, welcomeScreen: props.welcomeScreen }), autoConnectComp] });
  }
  return (0, import_jsx_runtime.jsx)("div", { children: autoConnectComp });
}
var ConnectEmbedContent = (props) => {
  const screenSetup = useSetupScreen({
    size: props.size,
    welcomeScreen: void 0,
    wallets: props.wallets
  });
  const { setScreen, initialScreen, screen } = screenSetup;
  const activeWallet = useActiveWallet();
  const activeAccount = useActiveAccount();
  const siweAuth = useSiweAuth(activeWallet, activeAccount, props.auth);
  const isAutoConnecting = useIsAutoConnecting();
  let content = null;
  (0, import_react2.useEffect)(() => {
    if (siweAuth.requiresAuth && !siweAuth.isLoggedIn && activeAccount && screen === initialScreen) {
      setScreen(reservedScreens.signIn);
    }
  }, [siweAuth, setScreen, activeAccount, screen, initialScreen]);
  const modalSize = !canFitWideModal() ? "compact" : props.modalSize || "compact";
  if (isAutoConnecting) {
    content = (0, import_jsx_runtime.jsx)(LoadingScreen, {});
  } else {
    content = (0, import_jsx_runtime.jsx)(ConnectModalContent, { shouldSetActive: true, screenSetup, isOpen: true, onClose: () => {
      setScreen(initialScreen);
    }, setModalVisibility: () => {
    }, accountAbstraction: props.accountAbstraction, auth: props.auth, chain: props.chain, chains: props.chains, client: props.client, connectLocale: props.connectLocale, meta: {
      ...props.meta,
      title: typeof props.header === "object" ? props.header.title : void 0,
      titleIconUrl: typeof props.header === "object" ? props.header.titleIcon : void 0
    }, size: props.size, welcomeScreen: props.welcomeScreen, hideHeader: !props.header, onConnect: props.onConnect, recommendedWallets: props.recommendedWallets, showAllWallets: props.showAllWallets, walletConnect: props.walletConnect, wallets: props.wallets, modalHeader: void 0, walletIdsToHide: void 0 });
  }
  return (0, import_jsx_runtime.jsx)(EmbedContainer, { modalSize, className: props.className, style: props.style, children: modalSize === "wide" ? content : (0, import_jsx_runtime.jsxs)(DynamicHeight, { children: [" ", content, " "] }) });
};
var EmbedContainer = StyledDiv((props) => {
  const { modalSize } = props;
  const theme = useCustomTheme();
  return {
    color: theme.colors.primaryText,
    background: theme.colors.modalBg,
    height: modalSize === "compact" ? "auto" : wideModalMaxHeight,
    width: modalSize === "compact" ? modalMaxWidthCompact : modalMaxWidthWide,
    boxSizing: "border-box",
    position: "relative",
    lineHeight: "normal",
    borderRadius: radius.xl,
    border: `1px solid ${theme.colors.borderColor}`,
    overflow: "hidden",
    fontFamily: theme.fontFamily,
    "& *::selection": {
      backgroundColor: theme.colors.selectedTextBg,
      color: theme.colors.selectedTextColor
    },
    "& *": {
      boxSizing: "border-box"
    }
  };
});

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/index.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/transaction-button-utils.js
var useTransactionButtonMutation = (props, sendTransactionFn) => {
  const { transaction, onTransactionSent, onTransactionConfirmed, onError, onClick } = props;
  return useMutation({
    mutationFn: async () => {
      if (onClick) {
        onClick();
      }
      try {
        const resolvedTx = await transaction();
        const result = await sendTransactionFn(resolvedTx);
        if (onTransactionSent) {
          onTransactionSent(result);
        }
        if (onTransactionConfirmed) {
          const receipt = await waitForReceipt(result);
          if (receipt.status === "reverted") {
            throw new Error(`Execution reverted: ${stringify(receipt, null, 2)}`);
          }
          onTransactionConfirmed(receipt);
        }
      } catch (error) {
        if (onError) {
          onError(error);
        }
      } finally {
      }
    }
  });
};

// node_modules/thirdweb/dist/esm/react/web/hooks/transaction/useSendTransaction.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useSendTransaction.js
function useSendTransactionCore(args) {
  const { showPayModal, gasless, wallet, switchChain } = args;
  let _account = wallet == null ? void 0 : wallet.getAccount();
  return useMutation({
    mutationFn: async (tx) => {
      var _a;
      if (wallet && tx.chain.id !== ((_a = wallet.getChain()) == null ? void 0 : _a.id)) {
        await switchChain(tx.chain);
        _account = wallet.getAccount();
      }
      const account = _account;
      if (!account) {
        throw new Error("No active account");
      }
      if (!showPayModal) {
        trackPayEvent({
          client: tx.client,
          walletAddress: account.address,
          walletType: wallet == null ? void 0 : wallet.id,
          chainId: tx.chain.id,
          event: "pay_transaction_modal_disabled"
        });
        return sendTransaction({
          transaction: tx,
          account,
          gasless
        });
      }
      return new Promise((resolve, reject) => {
        const sendTx = async () => {
          try {
            const res = await sendTransaction({
              transaction: tx,
              account,
              gasless
            });
            resolve(res);
          } catch (e) {
            reject(e);
          }
        };
        (async () => {
          try {
            const [_nativeValue, _erc20Value] = await Promise.all([
              resolvePromisedValue(tx.value),
              resolvePromisedValue(tx.erc20Value)
            ]);
            const nativeValue = _nativeValue || 0n;
            const erc20Value = (_erc20Value == null ? void 0 : _erc20Value.amountWei) || 0n;
            const [nativeBalance, erc20Balance, gasCost] = await Promise.all([
              getWalletBalance({
                client: tx.client,
                address: account.address,
                chain: tx.chain
              }),
              (_erc20Value == null ? void 0 : _erc20Value.tokenAddress) ? getTokenBalance({
                client: tx.client,
                account,
                chain: tx.chain,
                tokenAddress: _erc20Value.tokenAddress
              }) : void 0,
              getTransactionGasCost(tx, account.address)
            ]);
            const gasSponsored = hasSponsoredTransactionsEnabled(wallet);
            const txGasCost = gasSponsored ? 0n : gasCost;
            const nativeCost = nativeValue + txGasCost;
            const shouldShowModal = erc20Value > 0n && erc20Balance && erc20Balance.value < erc20Value || nativeCost > 0n && nativeBalance.value < nativeCost;
            if (shouldShowModal) {
              const supportedDestinations = await routes({
                client: tx.client,
                destinationChainId: tx.chain.id,
                destinationTokenAddress: _erc20Value == null ? void 0 : _erc20Value.tokenAddress
              }).catch((err) => {
                trackPayEvent({
                  client: tx.client,
                  walletAddress: account.address,
                  walletType: wallet == null ? void 0 : wallet.id,
                  toChainId: tx.chain.id,
                  event: "pay_transaction_modal_pay_api_error",
                  error: err == null ? void 0 : err.message
                });
                return null;
              });
              if (!supportedDestinations || supportedDestinations.length === 0) {
                trackPayEvent({
                  client: tx.client,
                  walletAddress: account.address,
                  walletType: wallet == null ? void 0 : wallet.id,
                  toChainId: tx.chain.id,
                  toToken: (_erc20Value == null ? void 0 : _erc20Value.tokenAddress) || void 0,
                  event: "pay_transaction_modal_chain_token_not_supported",
                  error: JSON.stringify({
                    chain: tx.chain.id,
                    token: _erc20Value == null ? void 0 : _erc20Value.tokenAddress,
                    message: "chain/token not supported"
                  })
                });
                showPayModal({
                  mode: "deposit",
                  tx,
                  sendTx,
                  rejectTx: reject,
                  resolveTx: resolve
                });
                return;
              }
              showPayModal({
                mode: "buy",
                tx,
                sendTx,
                rejectTx: reject,
                resolveTx: resolve
              });
            } else {
              trackPayEvent({
                client: tx.client,
                walletAddress: account.address,
                walletType: wallet == null ? void 0 : wallet.id,
                toChainId: tx.chain.id,
                toToken: (_erc20Value == null ? void 0 : _erc20Value.tokenAddress) || void 0,
                event: "pay_transaction_modal_has_enough_funds"
              });
              sendTx();
            }
          } catch (e) {
            console.error("Failed to estimate cost", e);
            sendTx();
          }
        })();
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/TransactionModal.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/DepositScreen.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var pulseAnimation = keyframes`
0% {
  opacity: 1;
  transform: scale(0.5);
}
100% {
  opacity: 0;
  transform: scale(1.5);
}
`;
var WaitingBadge = StyledDiv(() => {
  const theme = useCustomTheme();
  return {
    display: "flex",
    alignItems: "center",
    gap: spacing.sm,
    backgroundColor: theme.colors.tertiaryBg,
    border: `1px solid ${theme.colors.borderColor}`,
    padding: `${spacing.md} ${spacing.sm}`,
    borderRadius: radius.lg,
    color: theme.colors.secondaryText,
    fontSize: fontSize.sm,
    fontWeight: 500,
    position: "relative",
    "&::before": {
      content: '""',
      width: "8px",
      height: "8px",
      borderRadius: "50%",
      backgroundColor: theme.colors.accentText,
      animation: `${pulseAnimation} 1s infinite`
    }
  };
});
function DepositScreen(props) {
  const activeWallet = useActiveWallet();
  const activeAccount = useActiveAccount();
  const address = activeAccount == null ? void 0 : activeAccount.address;
  const { hasCopied, onCopy } = useClipboard(address || "");
  const { connectLocale, client } = props;
  const locale = connectLocale.receiveFundsScreen;
  const isTestnet = props.tx.chain.testnet === true;
  const { data: transactionCostAndData, error: transactionCostAndDataError, isFetching: transactionCostAndDataFetching, refetch: transactionCostAndDataRefetch } = useTransactionCostAndData({
    transaction: props.tx,
    account: activeAccount,
    supportedDestinations: [],
    refetchIntervalMs: 1e4
  });
  const theme = useCustomTheme();
  const sponsoredTransactionsEnabled = hasSponsoredTransactionsEnabled(activeWallet);
  if (transactionCostAndDataError) {
    return (0, import_jsx_runtime2.jsx)(Container, { style: {
      minHeight: "350px"
    }, fullHeight: true, flex: "row", center: "both", children: (0, import_jsx_runtime2.jsx)(ErrorState, { title: (transactionCostAndDataError == null ? void 0 : transactionCostAndDataError.message) || "Something went wrong", onTryAgain: transactionCostAndDataRefetch }) });
  }
  if (!transactionCostAndData) {
    return (0, import_jsx_runtime2.jsx)(LoadingScreen, {});
  }
  const totalCost = isNativeToken(transactionCostAndData.token) && !sponsoredTransactionsEnabled ? transactionCostAndData.transactionValueWei + transactionCostAndData.gasCostWei : transactionCostAndData.transactionValueWei;
  const insufficientFunds = transactionCostAndData.walletBalance.value < totalCost;
  const requiredFunds = transactionCostAndData.walletBalance.value ? totalCost - transactionCostAndData.walletBalance.value : totalCost;
  const openFaucetLink = () => {
    window.open(`https://thirdweb.com/${props.tx.chain.id}?utm_source=ub_deposit`);
  };
  return (0, import_jsx_runtime2.jsxs)(Container, { p: "lg", children: [(0, import_jsx_runtime2.jsx)(ModalHeader, { title: "Insufficient funds", onBack: props.onBack }), (0, import_jsx_runtime2.jsx)(Spacer, { y: "lg" }), (0, import_jsx_runtime2.jsxs)(Container, { flex: "column", gap: "sm", children: [insufficientFunds && (0, import_jsx_runtime2.jsx)("div", { children: (0, import_jsx_runtime2.jsxs)(Text, { size: "xs", center: true, color: "danger", multiline: true, children: ["You need", " ", formatNumber(Number.parseFloat(toTokens(requiredFunds, transactionCostAndData.decimals)), 5), " ", transactionCostAndData.token.symbol, " to continue"] }) }), (0, import_jsx_runtime2.jsxs)(Container, { flex: "row", style: {
    justifyContent: "space-between",
    padding: spacing.sm,
    marginBottom: spacing.sm,
    borderRadius: spacing.md,
    backgroundColor: theme.colors.tertiaryBg,
    border: `1px solid ${theme.colors.borderColor}`
  }, children: [activeAccount && (0, import_jsx_runtime2.jsx)(WalletRow, { address: activeAccount == null ? void 0 : activeAccount.address, iconSize: "md", client }), transactionCostAndData.walletBalance.value !== void 0 && !transactionCostAndDataFetching ? (0, import_jsx_runtime2.jsxs)(Container, { flex: "row", gap: "3xs", center: "y", children: [(0, import_jsx_runtime2.jsx)(Text, { size: "xs", color: "secondaryText", weight: 500, children: formatTokenBalance(transactionCostAndData.walletBalance, false) }), (0, import_jsx_runtime2.jsx)(TokenSymbol, { token: transactionCostAndData.token, chain: props.tx.chain, size: "xs", color: "secondaryText" })] }) : (0, import_jsx_runtime2.jsx)(Container, { flex: "row", gap: "3xs", center: "y", children: (0, import_jsx_runtime2.jsx)(Skeleton, { width: "70px", height: fontSize.xs }) })] })] }), (0, import_jsx_runtime2.jsx)(WalletAddressContainer, { onClick: onCopy, children: (0, import_jsx_runtime2.jsxs)(Container, { flex: "column", gap: "md", center: "both", expand: true, children: [(0, import_jsx_runtime2.jsx)(Container, { flex: "row", center: "x", children: (0, import_jsx_runtime2.jsx)(QRCode, { qrCodeUri: address, size: 250, QRIcon: activeWallet && (0, import_jsx_runtime2.jsx)(WalletImage, { id: activeWallet.id, size: iconSize.xl, client }) }) }), (0, import_jsx_runtime2.jsxs)(Container, { flex: "row", center: "x", gap: "xs", children: [(0, import_jsx_runtime2.jsx)(Text, { color: "primaryText", size: "md", children: address ? shortenAddress(address) : "" }), (0, import_jsx_runtime2.jsx)(CopyIcon, { text: address || "", tip: "Copy address", hasCopied })] })] }) }), (0, import_jsx_runtime2.jsx)(Spacer, { y: "md" }), (0, import_jsx_runtime2.jsx)(Text, { multiline: true, center: true, balance: true, size: "sm", className: "receive_fund_screen_instruction", children: locale.instruction }), (0, import_jsx_runtime2.jsx)(Spacer, { y: "md" }), insufficientFunds ? (0, import_jsx_runtime2.jsxs)(WaitingBadge, { children: ["Waiting for funds on ", transactionCostAndData.chainMetadata.name, "..."] }) : (0, import_jsx_runtime2.jsx)(Button, { variant: "accent", onClick: props.onDone, fullWidth: true, children: "Continue" }), insufficientFunds && isTestnet && (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, { children: [(0, import_jsx_runtime2.jsx)(Spacer, { y: "md" }), (0, import_jsx_runtime2.jsx)(Button, { variant: "link", onClick: openFaucetLink, fullWidth: true, children: (0, import_jsx_runtime2.jsxs)(Container, { flex: "row", center: "x", gap: "xs", color: "accentText", children: [(0, import_jsx_runtime2.jsx)(CoinsIcon, { size: iconSize.sm }), (0, import_jsx_runtime2.jsx)(Text, { size: "xs", color: "accentText", weight: 500, center: true, children: "Get testnet funds" })] }) })] })] });
}
var WalletAddressContainer = StyledButton((_) => {
  const theme = useCustomTheme();
  return {
    all: "unset",
    width: "100%",
    boxSizing: "border-box",
    cursor: "pointer",
    padding: spacing.md,
    display: "flex",
    justifyContent: "space-between",
    border: `1px solid ${theme.colors.borderColor}`,
    borderRadius: radius.lg,
    transition: "border-color 200ms ease",
    "&:hover": {
      borderColor: theme.colors.accentText
    }
  };
});

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/ExecutingScreen.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
function ExecutingTxScreen(props) {
  var _a;
  const sendTxCore = useSendTransaction({
    payModal: false
  });
  const [txHash, setTxHash] = (0, import_react4.useState)();
  const [txError, setTxError] = (0, import_react4.useState)();
  const chainExplorers = useChainExplorers(props.tx.chain);
  const [status, setStatus] = (0, import_react4.useState)("loading");
  const sendTx = (0, import_react4.useCallback)(async () => {
    setStatus("loading");
    setTxError(void 0);
    try {
      const txData = await sendTxCore.mutateAsync(props.tx);
      setTxHash(txData.transactionHash);
      props.onTxSent(txData);
      setStatus("sent");
    } catch (e) {
      console.error(e);
      setTxError(e);
      setStatus("failed");
    }
  }, [sendTxCore, props.tx, props.onTxSent]);
  const done = (0, import_react4.useRef)(false);
  (0, import_react4.useEffect)(() => {
    if (done.current) {
      return;
    }
    done.current = true;
    sendTx();
  }, [sendTx]);
  return (0, import_jsx_runtime3.jsxs)(Container, { p: "lg", children: [(0, import_jsx_runtime3.jsx)(ModalHeader, { title: "Transaction", onBack: props.onBack }), (0, import_jsx_runtime3.jsx)(Spacer, { y: "xxl" }), (0, import_jsx_runtime3.jsxs)(Container, { flex: "row", center: "x", children: [status === "loading" && (0, import_jsx_runtime3.jsx)(Spinner, { size: "xxl", color: "accentText" }), status === "failed" && (0, import_jsx_runtime3.jsx)(AccentFailIcon, { size: iconSize["3xl"] }), status === "sent" && (0, import_jsx_runtime3.jsx)(Container, { color: "success", flex: "row", center: "both", children: (0, import_jsx_runtime3.jsx)(CheckCircledIcon, { width: iconSize["3xl"], height: iconSize["3xl"] }) })] }), (0, import_jsx_runtime3.jsx)(Spacer, { y: "lg" }), (0, import_jsx_runtime3.jsxs)(Text, { color: "primaryText", center: true, size: "lg", children: [status === "loading" && "Sending transaction", status === "failed" && "Transaction failed", status === "sent" && "Transaction sent"] }), (0, import_jsx_runtime3.jsx)(Spacer, { y: "sm" }), (0, import_jsx_runtime3.jsx)(Text, { color: "danger", center: true, size: "sm", children: status === "failed" && txError ? txError.message || "" : "" }), (0, import_jsx_runtime3.jsx)(Spacer, { y: "xxl" }), status === "failed" && (0, import_jsx_runtime3.jsx)(Button, { variant: "accent", fullWidth: true, onClick: sendTx, children: "Try Again" }), status === "sent" && (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [(0, import_jsx_runtime3.jsx)(Button, { variant: "accent", fullWidth: true, onClick: props.closeModal, children: "Done" }), txHash && (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [(0, import_jsx_runtime3.jsx)(Spacer, { y: "sm" }), (0, import_jsx_runtime3.jsxs)(ButtonLink, { fullWidth: true, variant: "outline", href: formatExplorerTxUrl(((_a = chainExplorers.explorers[0]) == null ? void 0 : _a.url) ?? "", txHash), target: "_blank", as: "a", gap: "xs", style: {
    textDecoration: "none",
    color: "inherit"
  }, children: ["View on Explorer", (0, import_jsx_runtime3.jsx)(ExternalLinkIcon, { width: iconSize.sm, height: iconSize.sm })] })] })] })] });
}

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/TransactionModal.js
function TransactionModal(props) {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  useQuery({
    queryKey: ["transaction-modal-event", props.txId],
    queryFn: async () => {
      var _a;
      if (!account || !wallet) {
        throw new Error();
      }
      trackPayEvent({
        client: props.client,
        walletAddress: account.address,
        walletType: wallet.id,
        toChainId: props.tx.chain.id,
        toToken: props.tx.erc20Value ? (_a = await resolvePromisedValue(props.tx.erc20Value)) == null ? void 0 : _a.tokenAddress : void 0,
        event: props.modalMode === "buy" ? "open_pay_transaction_modal" : "open_pay_deposit_modal"
      });
      return null;
    },
    enabled: !!wallet && !!account
  });
  return (0, import_jsx_runtime4.jsx)(CustomThemeProvider, { theme: props.theme, children: (0, import_jsx_runtime4.jsx)(Modal, { open: true, size: "compact", setOpen: (_open) => {
    if (!_open) {
      props.onClose();
    }
  }, children: (0, import_jsx_runtime4.jsx)(TransactionModalContent, { ...props }) }) });
}
function TransactionModalContent(props) {
  const localeQuery = useConnectLocale(props.localeId);
  const [screen, setScreen] = (0, import_react5.useState)("buy");
  if (!localeQuery.data) {
    return (0, import_jsx_runtime4.jsx)(LoadingScreen, {});
  }
  if (screen === "execute-tx") {
    return (0, import_jsx_runtime4.jsx)(ExecutingTxScreen, { tx: props.tx, closeModal: props.onClose, onTxSent: props.onTxSent });
  }
  if (props.modalMode === "deposit") {
    return (0, import_jsx_runtime4.jsx)(DepositScreen, { client: props.client, onBack: props.onBack, tx: props.tx, connectLocale: localeQuery.data, onDone: () => {
      setScreen("execute-tx");
    } });
  }
  return (0, import_jsx_runtime4.jsx)(LazyBuyScreen, { title: props.title, isEmbed: false, client: props.client, onBack: props.onBack, supportedTokens: props.supportedTokens, connectLocale: localeQuery.data, theme: typeof props.theme === "string" ? props.theme : props.theme.type, payOptions: props.payOptions, onDone: () => {
    setScreen("execute-tx");
  }, connectOptions: void 0 });
}

// node_modules/thirdweb/dist/esm/react/web/hooks/transaction/useSendTransaction.js
function useSendTransaction(config = {}) {
  const switchChain = useSwitchActiveWalletChain();
  const wallet = useActiveWallet();
  const setRootEl = (0, import_react6.useContext)(SetRootElementContext);
  const payModal = config.payModal;
  let payModalEnabled = true;
  if (payModal === false || config.gasless) {
    payModalEnabled = false;
  }
  const showPayModal = (data) => {
    var _a;
    if (payModal === false)
      return;
    setRootEl((0, import_jsx_runtime5.jsx)(TransactionModal, { title: ((_a = payModal == null ? void 0 : payModal.metadata) == null ? void 0 : _a.name) || "Transaction", txId: randomBytesHex(), tx: data.tx, onComplete: data.sendTx, onClose: () => {
      setRootEl(null);
      data.rejectTx(new Error("User rejected transaction by closing modal"));
    }, onTxSent: data.resolveTx, client: data.tx.client, localeId: (payModal == null ? void 0 : payModal.locale) || "en_US", supportedTokens: payModal == null ? void 0 : payModal.supportedTokens, theme: (payModal == null ? void 0 : payModal.theme) || "dark", modalMode: data.mode, payOptions: {
      buyWithCrypto: payModal == null ? void 0 : payModal.buyWithCrypto,
      buyWithFiat: payModal == null ? void 0 : payModal.buyWithFiat,
      purchaseData: payModal == null ? void 0 : payModal.purchaseData,
      mode: "transaction",
      transaction: data.tx,
      metadata: payModal == null ? void 0 : payModal.metadata,
      onPurchaseSuccess: payModal == null ? void 0 : payModal.onPurchaseSuccess,
      showThirdwebBranding: payModal == null ? void 0 : payModal.showThirdwebBranding
    } }));
  };
  return useSendTransactionCore({
    showPayModal: !payModalEnabled || payModal === false ? void 0 : showPayModal,
    gasless: config.gasless,
    switchChain,
    wallet
  });
}

// node_modules/thirdweb/dist/esm/react/web/ui/TransactionButton/index.js
function TransactionButton(props) {
  const { children, transaction, onTransactionSent, onTransactionConfirmed, onError, onClick, gasless, payModal, disabled, unstyled, ...buttonProps } = props;
  const account = useActiveAccount();
  const sendTransaction2 = useSendTransaction({ gasless, payModal });
  const { mutate: handleClick, isPending } = useTransactionButtonMutation(props, sendTransaction2.mutateAsync);
  return (0, import_jsx_runtime6.jsx)(CustomThemeProvider, { theme: props.theme, children: (0, import_jsx_runtime6.jsxs)(Button, { gap: "xs", disabled: !account || disabled || isPending, variant: "primary", unstyled, "data-is-loading": isPending, onClick: () => handleClick(), ...buttonProps, style: !unstyled ? {
    opacity: !account || disabled ? 0.5 : 1,
    minWidth: "165px",
    position: "relative",
    ...buttonProps.style
  } : {
    position: "relative",
    ...buttonProps.style
  }, children: [(0, import_jsx_runtime6.jsx)("span", { style: { visibility: isPending ? "hidden" : "visible" }, children }), isPending && (0, import_jsx_runtime6.jsx)("div", { style: {
    position: "absolute",
    display: "flex",
    alignItems: "center",
    height: "100%",
    top: 0,
    bottom: 0,
    margin: "auto"
  }, children: (0, import_jsx_runtime6.jsx)(Spinner, { size: "md", color: "primaryButtonText" }) })] }) });
}

// node_modules/thirdweb/dist/esm/react/web/providers/thirdweb-provider.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_react8 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/react/core/providers/thirdweb-provider.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/react/core/utils/structuralSharing.js
function deepEqual(a, b) {
  if (a === b)
    return true;
  if (a && b && typeof a === "object" && typeof b === "object") {
    if (a.constructor !== b.constructor)
      return false;
    let length;
    let i;
    if (Array.isArray(a) && Array.isArray(b)) {
      length = a.length;
      if (length !== b.length)
        return false;
      for (i = length; i-- !== 0; )
        if (!deepEqual(a[i], b[i]))
          return false;
      return true;
    }
    if (a.valueOf !== Object.prototype.valueOf)
      return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString)
      return a.toString() === b.toString();
    const keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length)
      return false;
    for (i = length; i-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b, keys[i]))
        return false;
    for (i = length; i-- !== 0; ) {
      const key = keys[i];
      if (key && !deepEqual(a[key], b[key]))
        return false;
    }
    return true;
  }
  return a !== a && b !== b;
}
function structuralSharing(oldData, newData) {
  if (deepEqual(oldData, newData)) {
    return oldData;
  }
  return replaceEqualDeep(oldData, newData);
}

// node_modules/thirdweb/dist/esm/react/core/providers/thirdweb-provider.js
function ThirdwebProviderCore(props) {
  const [el, setEl] = (0, import_react7.useState)(null);
  const [queryClient] = (0, import_react7.useState)(() => new QueryClient({
    defaultOptions: {
      mutations: {
        onSettled: (data, _error, variables) => {
          if (isBaseTransactionOptions(variables)) {
            if (isObjectWithKeys(data, ["transactionHash"]) && isObjectWithKeys(variables, ["client", "chain"])) {
              waitForReceipt({
                transactionHash: data.transactionHash,
                // We know it exists from the if
                client: variables.client,
                chain: variables.chain
              }).catch((e) => {
                console.error("[Transaction Error]", e);
              }).then(() => {
                var _a, _b, _c;
                return Promise.all([
                  queryClient.invalidateQueries({
                    queryKey: (
                      // invalidate any readContract queries for this chainId:contractAddress
                      [
                        "readContract",
                        ((_a = variables.__contract) == null ? void 0 : _a.chain.id) || variables.chain.id,
                        ((_b = variables.__contract) == null ? void 0 : _b.address) || variables.to
                      ]
                    )
                  }),
                  invalidateWalletBalance(queryClient, ((_c = variables.__contract) == null ? void 0 : _c.chain.id) || variables.chain.id)
                ]);
              });
            }
          }
        }
      },
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1e3,
        structuralSharing
      }
    }
  }));
  return (0, import_jsx_runtime7.jsx)(ConnectionManagerCtx.Provider, { value: props.manager, children: (0, import_jsx_runtime7.jsxs)(QueryClientProvider, { client: queryClient, children: [(0, import_jsx_runtime7.jsx)(SetRootElementContext.Provider, { value: setEl, children: props.children }), el] }) });
}

// node_modules/thirdweb/dist/esm/react/web/providers/thirdweb-provider.js
function ThirdwebProvider(props) {
  const connectionManager = (0, import_react8.useMemo)(() => props.connectionManager || createConnectionManager(webLocalStorage), [props.connectionManager]);
  return (0, import_jsx_runtime8.jsx)(ThirdwebProviderCore, { manager: connectionManager, children: props.children });
}

// node_modules/thirdweb/dist/esm/react/web/hooks/wallets/useLinkProfile.js
function useLinkProfile() {
  const wallets = useConnectedWallets();
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["profiles"],
    mutationFn: async (options) => {
      var _a;
      const ecosystemWallet = wallets.find((w) => isEcosystemWallet(w));
      const ecosystem = ecosystemWallet ? {
        id: ecosystemWallet.id,
        partnerId: (_a = ecosystemWallet.getConfig()) == null ? void 0 : _a.partnerId
      } : void 0;
      const optionsWithEcosystem = { ...options, ecosystem };
      return linkProfile(optionsWithEcosystem);
    },
    onSuccess() {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["profiles"] });
      }, 500);
    }
  });
}

// node_modules/thirdweb/dist/esm/wallets/eip5792/get-capabilities.js
async function getCapabilities({ wallet, chainId }) {
  const account = wallet.getAccount();
  if (!account) {
    return {
      message: `Can't get capabilities, no account connected for wallet: ${wallet.id}`
    };
  }
  if (wallet.id === "smart") {
    const { smartWalletGetCapabilities } = await import("./smart-wallet-capabilities-SC5BEBDQ.js");
    return smartWalletGetCapabilities({ wallet });
  }
  if (isInAppWallet(wallet)) {
    const { inAppWalletGetCapabilities } = await import("./in-app-wallet-capabilities-OBNDS5NC.js");
    return inAppWalletGetCapabilities({ wallet });
  }
  if (isWalletConnect(wallet)) {
    return {
      message: "getCapabilities is not yet supported with Wallet Connect"
    };
  }
  let provider;
  if (isCoinbaseSDKWallet(wallet)) {
    const { getCoinbaseWebProvider } = await import("./coinbase-web-RYTN5W4O.js");
    const config = wallet.getConfig();
    provider = await getCoinbaseWebProvider(config);
  } else {
    provider = getInjectedProvider(wallet.id);
  }
  try {
    const result = await provider.request({
      method: "wallet_getCapabilities",
      params: [getAddress(account.address)]
    });
    const capabilities = {};
    for (const [chainId2, capabilities_] of Object.entries(result)) {
      capabilities[Number(chainId2)] = {};
      const capabilitiesCopy = {};
      for (const [key, value] of Object.entries(capabilities_)) {
        capabilitiesCopy[key] = value;
      }
      capabilities[Number(chainId2)] = capabilitiesCopy;
    }
    return typeof chainId === "number" ? capabilities[chainId] : capabilities;
  } catch (error) {
    if (/unsupport|not support|not available/i.test(error.message)) {
      return {
        message: `${wallet.id} does not support wallet_getCapabilities, reach out to them directly to request EIP-5792 support.`
      };
    }
    throw error;
  }
}

// node_modules/thirdweb/dist/esm/react/core/hooks/wallets/useCapabilities.js
function useCapabilities(options) {
  const wallet = useActiveWallet();
  return useQuery({
    queryKey: ["getCapabilities", wallet == null ? void 0 : wallet.id, options == null ? void 0 : options.chainId],
    queryFn: async () => {
      if (!wallet) {
        return {
          message: "Can't get capabilities, no wallet connected"
        };
      }
      return getCapabilities({
        wallet,
        chainId: options == null ? void 0 : options.chainId
      });
    },
    retry: false,
    ...options == null ? void 0 : options.queryOptions
  });
}

// node_modules/thirdweb/dist/esm/wallets/eip5792/send-calls.js
async function sendCalls(options) {
  const { wallet, calls, capabilities, version = "2.0.0", chain = wallet.getChain() } = options;
  if (!chain) {
    throw new Error(`Cannot send calls, no active chain found for wallet: ${wallet.id}`);
  }
  const account = wallet.getAccount();
  if (!account) {
    throw new Error(`Cannot send calls, no account connected for wallet: ${wallet.id}`);
  }
  const firstCall = options.calls[0];
  if (!firstCall) {
    throw new Error("No calls to send");
  }
  const client = firstCall.client;
  if (isSmartWallet(wallet) || isInAppWallet(wallet)) {
    const { inAppWalletSendCalls } = await import("./in-app-wallet-calls-SHVD5VLA.js");
    const id = await inAppWalletSendCalls({ account, calls });
    return { id, client, chain, wallet };
  }
  const preparedCalls = await Promise.all(calls.map(async (call) => {
    const { to, value } = call;
    if (to === void 0 && call.data === void 0) {
      throw new Error("Cannot send call, `to` or `data` must be provided.");
    }
    const [_to, _data, _value] = await Promise.all([
      resolvePromisedValue(to),
      encode(call),
      resolvePromisedValue(value)
    ]);
    return {
      to: _to,
      data: _data,
      value: typeof _value === "bigint" || typeof _value === "number" ? numberToHex(_value) : void 0
    };
  }));
  const injectedWalletCallParams = [
    {
      from: getAddress(account.address),
      calls: preparedCalls,
      capabilities,
      version,
      chainId: numberToHex(chain.id),
      // see: https://eips.ethereum.org/EIPS/eip-5792#wallet_sendcalls
      atomicRequired: options.atomicRequired ?? false
    }
  ];
  if (isWalletConnect(wallet)) {
    throw new Error("sendCalls is not yet supported for Wallet Connect");
  }
  let provider;
  if (isCoinbaseSDKWallet(wallet)) {
    const { getCoinbaseWebProvider } = await import("./coinbase-web-RYTN5W4O.js");
    const config = wallet.getConfig();
    provider = await getCoinbaseWebProvider(config);
  } else {
    provider = getInjectedProvider(wallet.id);
  }
  try {
    const callId = await provider.request({
      method: "wallet_sendCalls",
      params: injectedWalletCallParams
      // The viem type definition is slightly different
    });
    if (typeof callId === "object" && "id" in callId) {
      return { id: callId.id, client, chain, wallet };
    }
    return { id: callId, client, chain, wallet };
  } catch (error) {
    if (/unsupport|not support/i.test(error.message)) {
      throw new Error(`${wallet.id} errored calling wallet_sendCalls, with error: ${error instanceof Error ? error.message : stringify(error)}`);
    }
    throw error;
  }
}

// node_modules/thirdweb/dist/esm/wallets/eip5792/get-calls-status.js
async function getCallsStatus({ wallet, client, id }) {
  const account = wallet.getAccount();
  if (!account) {
    throw new Error(`Failed to get call status, no account found for wallet ${wallet.id}`);
  }
  if (isSmartWallet(wallet) || isInAppWallet(wallet)) {
    const { inAppWalletGetCallsStatus } = await import("./in-app-wallet-calls-SHVD5VLA.js");
    return inAppWalletGetCallsStatus({ wallet, client, id });
  }
  if (isWalletConnect(wallet)) {
    throw new Error("getCallsStatus is not yet supported for Wallet Connect");
  }
  let provider;
  if (isCoinbaseSDKWallet(wallet)) {
    const { getCoinbaseWebProvider } = await import("./coinbase-web-RYTN5W4O.js");
    const config = wallet.getConfig();
    provider = await getCoinbaseWebProvider(config);
  } else {
    provider = getInjectedProvider(wallet.id);
  }
  try {
    const { atomic = false, chainId, receipts, version = "2.0.0", ...response } = await provider.request({
      method: "wallet_getCallsStatus",
      params: [id]
    });
    const [status, statusCode] = (() => {
      const statusCode2 = response.status;
      if (statusCode2 >= 100 && statusCode2 < 200)
        return ["pending", statusCode2];
      if (statusCode2 >= 200 && statusCode2 < 300)
        return ["success", statusCode2];
      if (statusCode2 >= 300 && statusCode2 < 700)
        return ["failure", statusCode2];
      if (statusCode2 === "CONFIRMED")
        return ["success", 200];
      if (statusCode2 === "PENDING")
        return ["pending", 100];
      return [void 0, statusCode2];
    })();
    return {
      ...response,
      atomic,
      // @ts-expect-error: for backwards compatibility
      chainId: chainId ? hexToNumber(chainId) : void 0,
      receipts: (receipts == null ? void 0 : receipts.map((receipt) => ({
        ...receipt,
        blockNumber: hexToBigInt(receipt.blockNumber),
        gasUsed: hexToBigInt(receipt.gasUsed),
        status: receiptStatuses[receipt.status]
      }))) ?? [],
      statusCode,
      status,
      version
    };
  } catch (error) {
    if (/unsupport|not support/i.test(error.message)) {
      throw new Error(`${wallet.id} does not support wallet_getCallsStatus, reach out to them directly to request EIP-5792 support.`);
    }
    throw error;
  }
}
var receiptStatuses = {
  "0x0": "reverted",
  "0x1": "success"
};

// node_modules/thirdweb/dist/esm/wallets/eip5792/wait-for-calls-receipt.js
var DEFAULT_MAX_BLOCKS_WAIT_TIME = 100;
var map = /* @__PURE__ */ new Map();
function waitForCallsReceipt(options) {
  const { id, chain, wallet, client } = options;
  const chainId = chain.id;
  const key = `${chainId}:calls_${id}`;
  const maxBlocksWaitTime = options.maxBlocksWaitTime ?? DEFAULT_MAX_BLOCKS_WAIT_TIME;
  if (map.has(key)) {
    return map.get(key);
  }
  const promise = new Promise((resolve, reject) => {
    let blocksWaited = -1;
    const unwatch = watchBlockNumber({
      client,
      chain,
      onNewBlockNumber: async () => {
        blocksWaited++;
        if (blocksWaited >= maxBlocksWaitTime) {
          unwatch();
          reject(new Error(`Bundle not confirmed after ${maxBlocksWaitTime} blocks`));
          return;
        }
        try {
          const result = await getCallsStatus({
            wallet,
            client,
            id
          });
          if (result.status === "success" || result.status === "failure") {
            unwatch();
            resolve(result);
            return;
          }
        } catch {
        }
      }
    });
  }).finally(() => {
    map.delete(key);
  });
  map.set(key, promise);
  return promise;
}

// node_modules/thirdweb/dist/esm/react/core/hooks/wallets/useSendCalls.js
function useSendCalls() {
  const activeWallet = useActiveWallet();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (options) => {
      const { wallet = activeWallet } = options;
      const chain = wallet == null ? void 0 : wallet.getChain();
      if (!wallet || !chain) {
        throw new Error("Failed to send transactions, no connected wallet found.");
      }
      return sendCalls({ ...options, wallet });
    },
    onSettled: async (result, _error, variables) => {
      var _a;
      if (!result) {
        return;
      }
      const call = variables.calls[0];
      if (!call) {
        return;
      }
      const chain = ((_a = call.__contract) == null ? void 0 : _a.chain) || call.chain;
      waitForCallsReceipt(result).then(() => {
        var _a2, _b;
        for (const call2 of variables.calls) {
          queryClient.invalidateQueries({
            queryKey: [
              "readContract",
              ((_a2 = call2.__contract) == null ? void 0 : _a2.chain.id) || chain.id,
              ((_b = call2.__contract) == null ? void 0 : _b.address) || call2.to
            ]
          });
        }
        invalidateWalletBalance(queryClient, chain.id);
      }).catch((error) => {
        console.error("Failed to confirm sent bundle and invalidate queries", result, error);
        return void 0;
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/wallets/eip5792/send-and-confirm-calls.js
async function sendAndConfirmCalls(options) {
  const sendCallsResult = await sendCalls(options);
  return waitForCallsReceipt({
    ...sendCallsResult,
    maxBlocksWaitTime: options.maxBlocksWaitTime
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/wallets/useSendAndConfirmCalls.js
function useSendAndConfirmCalls(args) {
  const activeWallet = useActiveWallet();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (options) => {
      const { wallet = activeWallet } = options;
      if (!wallet) {
        throw new Error("Failed to send transactions, no connected wallet found.");
      }
      return sendAndConfirmCalls({
        ...options,
        wallet,
        maxBlocksWaitTime: args == null ? void 0 : args.maxBlocksWaitTime
      });
    },
    onSettled: async (_result, _error, variables) => {
      var _a, _b, _c;
      const call = variables.calls[0];
      if (!call) {
        return;
      }
      const chain = ((_a = call.__contract) == null ? void 0 : _a.chain) || call.chain;
      for (const call2 of variables.calls) {
        queryClient.invalidateQueries({
          queryKey: [
            "readContract",
            ((_b = call2.__contract) == null ? void 0 : _b.chain.id) || call2.chain.id,
            ((_c = call2.__contract) == null ? void 0 : _c.address) || call2.to
          ]
        });
      }
      invalidateWalletBalance(queryClient, chain.id);
    }
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/wallets/useWaitForCallsReceipt.js
function useWaitForCallsReceipt(args) {
  var _a;
  return useQuery({
    queryKey: ["waitForCallsReceipt", args == null ? void 0 : args.id],
    queryFn: async () => {
      if (!(args == null ? void 0 : args.id)) {
        throw new Error("No call result provided");
      }
      return waitForCallsReceipt({
        ...args,
        maxBlocksWaitTime: args.maxBlocksWaitTime
      });
    },
    enabled: !!(args == null ? void 0 : args.id) && (((_a = args == null ? void 0 : args.queryOptions) == null ? void 0 : _a.enabled) ?? true),
    retry: false
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/contract/useReadContract.js
function useReadContract(extensionOrOptions, options) {
  let queryKey;
  let queryFn;
  let queryOpts;
  if (typeof extensionOrOptions === "function") {
    if (!options) {
      throw new Error(`Missing second argument for "useReadContract(<extension>, <options>)" hook.`);
    }
    const { queryOptions: queryOptions2, contract, ...params } = options;
    queryOpts = queryOptions2;
    queryKey = [
      "readContract",
      contract.chain.id,
      contract.address,
      getFunctionId(extensionOrOptions),
      stringify(params)
    ];
    queryFn = () => extensionOrOptions({
      ...params,
      contract
    });
  }
  if ("method" in extensionOrOptions) {
    const { queryOptions: queryOptions2, ...tx } = extensionOrOptions;
    queryOpts = queryOptions2;
    queryKey = [
      "readContract",
      tx.contract.chain.id,
      tx.contract.address,
      tx.method,
      stringify(tx.params)
    ];
    queryFn = () => readContract(extensionOrOptions);
  }
  if (!queryKey || !queryFn) {
    throw new Error(`Invalid "useReadContract" options. Expected either a read extension or a transaction object.`);
  }
  return useQuery(queryOptions({
    queryKey,
    queryFn,
    ...queryOpts ?? {}
  }));
}

// node_modules/thirdweb/dist/esm/react/core/hooks/contract/useContractEvents.js
var import_react9 = __toESM(require_react(), 1);
function useContractEvents(options) {
  const { contract, events, blockRange = 2e3, enabled = true, watch = true } = options;
  const latestBlockNumber = (0, import_react9.useRef)(void 0);
  const queryClient = useQueryClient();
  const eventsKey = (0, import_react9.useMemo)(() => (events == null ? void 0 : events.reduce((acc, curr) => {
    return `${acc}${curr.hash}_`;
  }, "")) || "__all__", [events]);
  const queryKey = (0, import_react9.useMemo)(() => [contract.chain.id, contract.address, "logs", eventsKey], [contract.address, contract.chain, eventsKey]);
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      const rpcRequest = getRpcClient(contract);
      const currentBlockNumber = await eth_blockNumber(rpcRequest);
      latestBlockNumber.current = currentBlockNumber;
      const initialEvents = await getContractEvents({
        contract,
        events,
        fromBlock: currentBlockNumber - BigInt(blockRange)
      });
      return initialEvents;
    },
    enabled
  });
  (0, import_react9.useEffect)(() => {
    if (!enabled || !watch) {
      return;
    }
    return watchContractEvents({
      contract,
      onEvents: (newEvents) => {
        if (newEvents.length > 0 && newEvents[0]) {
          latestBlockNumber.current = newEvents[0].blockNumber;
        }
        queryClient.setQueryData(queryKey, (oldEvents = []) => [
          ...oldEvents,
          ...newEvents
        ]);
      },
      events,
      latestBlockNumber: latestBlockNumber.current
    });
  }, [contract, enabled, events, queryClient, queryKey, watch]);
  return query;
}

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useSimulateTransaction.js
function useSimulateTransaction() {
  return useMutation({
    mutationFn: (options) => simulateTransaction(options)
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useSendBatchTransaction.js
function useSendBatchTransaction() {
  const account = useActiveAccount();
  return useMutation({
    mutationFn: async (transactions) => {
      if (!account) {
        throw new Error("No active account");
      }
      return await sendBatchTransaction({
        transactions,
        account
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useSendAndConfirmTransaction.js
function useSendAndConfirmTransaction(config = {}) {
  const account = useActiveAccount();
  const { gasless } = config;
  return useMutation({
    mutationFn: async (transaction) => {
      if (!account) {
        throw new Error("No active account");
      }
      return await sendAndConfirmTransaction({
        transaction,
        account,
        gasless
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useEstimateGas.js
function useEstimateGas() {
  const account = useActiveAccount();
  return useMutation({
    mutationFn: (transaction) => estimateGas({ transaction, account })
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/transaction/useEstimateGasCost.js
function useEstimateGasCost() {
  const account = useActiveAccount();
  return useMutation({
    mutationFn: (transaction) => estimateGasCost({ transaction, account })
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/rpc/useBlockNumber.js
var import_react10 = __toESM(require_react(), 1);
function useBlockNumber(options) {
  const { client, chain, enabled = true, watch = true } = options;
  const queryClient = useQueryClient();
  const queryKey = (0, import_react10.useMemo)(() => [chain.id, "blockNumber"], [chain]);
  const query = useQuery({
    // TODO: technically client should be part of the queryKey here...
    queryKey,
    queryFn: async () => {
      const rpcRequest = getRpcClient({ client, chain });
      return await eth_blockNumber(rpcRequest);
    },
    enabled
  });
  (0, import_react10.useEffect)(() => {
    if (!enabled || !watch) {
      return;
    }
    return watchBlockNumber({
      client,
      chain,
      onNewBlockNumber: (newBlockNumber) => {
        queryClient.setQueryData(queryKey, newBlockNumber);
      }
    });
  }, [client, chain, enabled, queryClient, queryKey, watch]);
  return query.data;
}

// node_modules/thirdweb/dist/esm/react/core/utils/createQuery.js
var CONTRACT_QUERY_CACHE = /* @__PURE__ */ new WeakMap();
function createContractQuery(readCall) {
  if (CONTRACT_QUERY_CACHE.has(readCall)) {
    return CONTRACT_QUERY_CACHE.get(readCall);
  }
  function useRead(options) {
    const { contract, queryOptions: queryOptions2, ...params } = options;
    return useQuery({
      queryKey: [
        "readContract",
        contract.chain.id,
        contract.address,
        getFunctionId(readCall),
        stringify(params)
      ],
      queryFn: () => readCall(options),
      ...queryOptions2
    });
  }
  CONTRACT_QUERY_CACHE.set(readCall, useRead);
  return useRead;
}

// node_modules/thirdweb/dist/esm/react/core/hooks/others/useInvalidateQueries.js
function useInvalidateContractQuery() {
  const queryClient = useQueryClient();
  return ({ chainId, contractAddress }) => {
    queryClient.invalidateQueries({
      queryKey: ["readContract", chainId, contractAddress]
    });
  };
}

// node_modules/thirdweb/dist/esm/react/core/hooks/pay/useBuyWithCryptoHistory.js
function useBuyWithCryptoHistory(params, queryParams) {
  return useQuery({
    ...queryParams,
    queryKey: ["getBuyWithCryptoHistory", params],
    queryFn: () => {
      if (!params) {
        throw new Error("Swap params are required");
      }
      return getBuyWithCryptoHistory(params);
    },
    enabled: !!params
  });
}

// node_modules/thirdweb/dist/esm/pay/buyWithFiat/getHistory.js
async function getBuyWithFiatHistory(params) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("walletAddress", params.walletAddress);
    queryParams.append("start", params.start.toString());
    queryParams.append("count", params.count.toString());
    const queryString = queryParams.toString();
    const url = `${getPayBuyWithFiatHistoryEndpoint()}?${queryString}`;
    const response = await getClientFetch(params.client)(url);
    if (!response.ok) {
      const error = await response.text().catch(() => null);
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}: ${error || "unknown error"}`);
    }
    const data = (await response.json()).result;
    return data;
  } catch (error) {
    throw new Error(`Fetch failed: ${error}`);
  }
}

// node_modules/thirdweb/dist/esm/react/core/hooks/pay/useBuyWithFiatHistory.js
function useBuyWithFiatHistory(params, queryParams) {
  return useQuery({
    ...queryParams,
    queryKey: ["buyWithFiatHistory", params],
    queryFn: () => {
      if (!params) {
        throw new Error("params are required");
      }
      return getBuyWithFiatHistory(params);
    },
    enabled: !!params
  });
}

// node_modules/thirdweb/dist/esm/pay/getBuyHistory.js
async function getBuyHistory(params) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("walletAddress", params.walletAddress);
    queryParams.append("start", params.start.toString());
    queryParams.append("count", params.count.toString());
    const queryString = queryParams.toString();
    const url = `${getPayBuyHistoryEndpoint()}?${queryString}`;
    const response = await getClientFetch(params.client)(url);
    if (!response.ok) {
      const error = await response.text().catch(() => null);
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}: ${error || "unknown error"}`);
    }
    const data = (await response.json()).result;
    return data;
  } catch (error) {
    throw new Error(`Fetch failed: ${error}`);
  }
}

// node_modules/thirdweb/dist/esm/react/core/hooks/pay/useBuyHistory.js
function useBuyHistory(params, queryParams) {
  return useQuery({
    ...queryParams,
    queryKey: ["getBuyHistory", params],
    queryFn: () => {
      if (!params) {
        throw new Error("params are required");
      }
      return getBuyHistory(params);
    },
    enabled: !!params
  });
}

// node_modules/thirdweb/dist/esm/pay/buyWithFiat/getPostOnRampQuote.js
async function getPostOnRampQuote({ client, buyWithFiatStatus }) {
  if (buyWithFiatStatus.status === "NOT_FOUND") {
    throw new Error("Invalid buyWithFiatStatus");
  }
  return getBuyWithCryptoQuote({
    client,
    intentId: buyWithFiatStatus.intentId,
    // onramp always happens to fromAddress, and then swap is done from - fromAddress to toAddress
    fromAddress: buyWithFiatStatus.fromAddress,
    toAddress: buyWithFiatStatus.toAddress,
    fromChainId: buyWithFiatStatus.quote.onRampToken.chainId,
    fromTokenAddress: buyWithFiatStatus.quote.onRampToken.tokenAddress,
    toChainId: buyWithFiatStatus.quote.toToken.chainId,
    toTokenAddress: buyWithFiatStatus.quote.toToken.tokenAddress,
    toAmount: buyWithFiatStatus.quote.estimatedToTokenAmount
  });
}

// node_modules/thirdweb/dist/esm/react/core/hooks/pay/usePostOnrampQuote.js
function usePostOnRampQuote(params, queryOptions2) {
  return useQuery({
    ...queryOptions2,
    queryKey: ["getPostOnRampQuote", params],
    queryFn: async () => {
      if (!params) {
        throw new Error("No params provided");
      }
      return getPostOnRampQuote(params);
    },
    enabled: !!params
  });
}

// node_modules/thirdweb/dist/esm/react/web/ui/PayEmbed.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_react11 = __toESM(require_react(), 1);
function PayEmbed(props) {
  var _a, _b, _c, _d;
  const localeQuery = useConnectLocale(props.locale || "en_US");
  const [screen, setScreen] = (0, import_react11.useState)("buy");
  const theme = props.theme || "dark";
  const connectionManager = useConnectionManager();
  const activeAccount = useActiveAccount();
  const activeWallet = useActiveWallet();
  const siweAuth = useSiweAuth(activeWallet, activeAccount, (_a = props.connectOptions) == null ? void 0 : _a.auth);
  (0, import_react11.useEffect)(() => {
    var _a2, _b2;
    if ((_a2 = props.connectOptions) == null ? void 0 : _a2.chain) {
      connectionManager.defineChains([(_b2 = props.connectOptions) == null ? void 0 : _b2.chain]);
    }
  }, [(_b = props.connectOptions) == null ? void 0 : _b.chain, connectionManager]);
  (0, import_react11.useEffect)(() => {
    var _a2, _b2;
    if ((_a2 = props.connectOptions) == null ? void 0 : _a2.chains) {
      connectionManager.defineChains((_b2 = props.connectOptions) == null ? void 0 : _b2.chains);
    }
  }, [(_c = props.connectOptions) == null ? void 0 : _c.chains, connectionManager]);
  (0, import_react11.useEffect)(() => {
    if (props.activeWallet) {
      connectionManager.setActiveWallet(props.activeWallet);
    }
  }, [props.activeWallet, connectionManager]);
  let content = null;
  const metadata = props.payOptions && "metadata" in props.payOptions ? props.payOptions.metadata : null;
  if (!localeQuery.data) {
    content = (0, import_jsx_runtime9.jsx)("div", { style: {
      minHeight: "350px",
      display: "flex",
      justifyContent: "center",
      alignItems: "center"
    }, children: (0, import_jsx_runtime9.jsx)(Spinner, { size: "xl", color: "secondaryText" }) });
  } else {
    content = (0, import_jsx_runtime9.jsxs)(import_jsx_runtime9.Fragment, { children: [(0, import_jsx_runtime9.jsx)(AutoConnect, { client: props.client, siweAuth }), screen === "buy" && (0, import_jsx_runtime9.jsx)(BuyScreen, { title: (metadata == null ? void 0 : metadata.name) || "Buy", isEmbed: true, supportedTokens: props.supportedTokens, theme, client: props.client, connectLocale: localeQuery.data, hiddenWallets: props.hiddenWallets, payOptions: props.payOptions || {
      mode: "fund_wallet"
    }, onDone: () => {
      var _a2;
      if (((_a2 = props.payOptions) == null ? void 0 : _a2.mode) === "transaction") {
        setScreen("execute-tx");
      }
    }, connectOptions: props.connectOptions, onBack: void 0 }), screen === "execute-tx" && ((_d = props.payOptions) == null ? void 0 : _d.mode) === "transaction" && props.payOptions.transaction && (0, import_jsx_runtime9.jsx)(ExecutingTxScreen, { tx: props.payOptions.transaction, closeModal: () => {
      setScreen("buy");
    }, onBack: () => {
      setScreen("buy");
    }, onTxSent: (data) => {
      var _a2, _b2;
      (_b2 = (_a2 = props.payOptions) == null ? void 0 : _a2.onPurchaseSuccess) == null ? void 0 : _b2.call(_a2, {
        type: "transaction",
        chainId: data.chain.id,
        transactionHash: data.transactionHash
      });
    } })] });
  }
  return (0, import_jsx_runtime9.jsx)(CustomThemeProvider, { theme, children: (0, import_jsx_runtime9.jsx)(EmbedContainer, { modalSize: "compact", style: props.style, className: props.className, children: (0, import_jsx_runtime9.jsx)(DynamicHeight, { children: content }) }) });
}

// node_modules/thirdweb/dist/esm/react/web/ui/ConnectWallet/useConnectModal.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_react12 = __toESM(require_react(), 1);
function useConnectModal() {
  const setRootEl = (0, import_react12.useContext)(SetRootElementContext);
  const [isConnecting, setIsConnecting] = (0, import_react12.useState)(false);
  const connect = (0, import_react12.useCallback)((props) => {
    function cleanup() {
      setIsConnecting(false);
      setRootEl(void 0);
    }
    return new Promise((resolve, reject) => {
      setIsConnecting(true);
      getConnectLocale(props.locale || "en_US").then((locale) => {
        setRootEl((0, import_jsx_runtime10.jsx)(Modal2, { ...props, onConnect: (w) => {
          if (props.auth)
            return;
          resolve(w);
          cleanup();
        }, onClose: () => {
          reject();
          cleanup();
        }, connectLocale: locale }));
      }).catch(() => {
        reject();
        cleanup();
      });
    });
  }, [setRootEl]);
  return { connect, isConnecting };
}
function Modal2(props) {
  const wallets = (0, import_react12.useMemo)(() => props.wallets || getDefaultWallets({
    appMetadata: props.appMetadata,
    chains: props.chains
  }), [props.wallets, props.appMetadata, props.chains]);
  const size = (0, import_react12.useMemo)(() => {
    return !canFitWideModal() || wallets.length === 1 ? "compact" : props.size || "wide";
  }, [props.size, wallets.length]);
  const meta = (0, import_react12.useMemo)(() => {
    return {
      privacyPolicyUrl: props.privacyPolicyUrl,
      showThirdwebBranding: props.showThirdwebBranding,
      termsOfServiceUrl: props.termsOfServiceUrl,
      title: props.title,
      titleIconUrl: props.titleIcon
    };
  }, [
    props.privacyPolicyUrl,
    props.showThirdwebBranding,
    props.termsOfServiceUrl,
    props.title,
    props.titleIcon
  ]);
  return (0, import_jsx_runtime10.jsx)(WalletUIStatesProvider, { theme: props.theme, isOpen: true, children: (0, import_jsx_runtime10.jsx)(ConnectModal_default, { onClose: props.onClose, shouldSetActive: props.setActive === void 0 ? true : props.setActive, accountAbstraction: props.accountAbstraction, auth: props.auth, chain: props.chain, client: props.client, connectLocale: props.connectLocale, meta, size, welcomeScreen: props.welcomeScreen, localeId: props.locale || "en_US", onConnect: props.onConnect, recommendedWallets: props.recommendedWallets, showAllWallets: props.showAllWallets, wallets, chains: props.chains, walletConnect: props.walletConnect }) });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/thirdweb/ClaimButton/index.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
function ClaimButton(props) {
  const { children, contractAddress, client, chain, claimParams, payModal } = props;
  const defaultPayModalMetadata = payModal ? payModal.metadata : void 0;
  const contract = getContract({
    address: contractAddress,
    client,
    chain
  });
  const { data: payMetadata } = useReadContract(getPayMetadata, {
    contract,
    tokenId: claimParams.type === "ERC1155" ? claimParams.tokenId : void 0,
    queryOptions: {
      enabled: !defaultPayModalMetadata
    }
  });
  const account = useActiveAccount();
  const { mutateAsync } = useSendAndConfirmTransaction();
  return (0, import_jsx_runtime11.jsx)(TransactionButton, { payModal: {
    metadata: defaultPayModalMetadata || payMetadata,
    ...payModal
  }, transaction: async () => {
    if (!account) {
      throw new Error("No account detected");
    }
    const [claimTx, { getApprovalForTransaction }] = await Promise.all([
      getClaimTransaction({
        contract,
        account,
        claimParams
      }),
      import("./getApprovalForTransaction-R4PMZZSM.js")
    ]);
    const approveTx = await getApprovalForTransaction({
      transaction: claimTx,
      account
    });
    if (approveTx) {
      await mutateAsync(approveTx);
    }
    return claimTx;
  }, ...props, children });
}
async function getPayMetadata(options) {
  var _a, _b;
  const { contract, tokenId } = options;
  const [contractMetadata, nft] = await Promise.all([
    getContractMetadata(options),
    tokenId ? getNFT2({ contract, tokenId }) : void 0
  ]);
  if (tokenId) {
    return {
      image: (_a = nft == null ? void 0 : nft.metadata) == null ? void 0 : _a.image,
      name: (_b = nft == null ? void 0 : nft.metadata) == null ? void 0 : _b.name
    };
  }
  return {
    image: contractMetadata == null ? void 0 : contractMetadata.image,
    name: contractMetadata == null ? void 0 : contractMetadata.name
  };
}
async function getClaimTransaction({ contract, account, claimParams }) {
  switch (claimParams.type) {
    case "ERC721":
      return await getERC721ClaimTo({ contract, account, claimParams });
    case "ERC1155":
      return await getERC1155ClaimTo({ contract, account, claimParams });
    case "ERC20": {
      return await getERC20ClaimTo({ contract, account, claimParams });
    }
    default:
      throw new Error("Invalid contract type. Must be either NFT Drop (ERC721), Edition Drop (ERC1155) or Token Drop (ERC20)");
  }
}
async function getERC721ClaimTo({ contract, account, claimParams }) {
  const { claimTo } = await import("./claimTo-2NUUPL5O.js");
  return claimTo({
    contract,
    to: claimParams.to || (account == null ? void 0 : account.address) || "",
    quantity: claimParams.quantity,
    from: claimParams.from
  });
}
async function getERC1155ClaimTo({ contract, account, claimParams }) {
  const { claimTo } = await import("./claimTo-SDVGNNDG.js");
  return claimTo({
    contract,
    to: claimParams.to || (account == null ? void 0 : account.address) || "",
    quantity: claimParams.quantity,
    tokenId: claimParams.tokenId,
    from: claimParams.from
  });
}
async function getERC20ClaimTo({ contract, account, claimParams }) {
  const { claimTo } = await import("./claimTo-HHS526YN.js");
  if ("quantity" in claimParams) {
    return claimTo({
      contract,
      to: claimParams.to || (account == null ? void 0 : account.address) || "",
      quantity: claimParams.quantity,
      from: claimParams.from
    });
  }
  if ("quantityInWei" in claimParams) {
    return claimTo({
      contract,
      to: claimParams.to || (account == null ? void 0 : account.address) || "",
      quantityInWei: claimParams.quantityInWei,
      from: claimParams.from
    });
  }
  throw new Error("Missing quantity or quantityInWei");
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/thirdweb/BuyDirectListingButton/index.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_react13 = __toESM(require_react(), 1);
function BuyDirectListingButton(props) {
  const { contractAddress, listingId, children, chain, client, quantity, payModal } = props;
  const defaultPayModalMetadata = payModal ? payModal.metadata : void 0;
  const account = useActiveAccount();
  const contract = getContract({
    address: contractAddress,
    client,
    chain
  });
  const { data: payMetadata } = useReadContract(getPayMetadata2, {
    contract,
    listingId,
    queryOptions: {
      enabled: !defaultPayModalMetadata
    }
  });
  const { mutateAsync } = useSendAndConfirmTransaction();
  const prepareBuyTransaction = (0, import_react13.useCallback)(async () => {
    if (!account) {
      throw new Error("No account detected");
    }
    const [listing, { getApprovalForTransaction }, { buyFromListing }] = await Promise.all([
      getListing({
        contract,
        listingId
      }),
      import("./getApprovalForTransaction-R4PMZZSM.js"),
      import("./buyFromListing-SMXZHZZM.js")
    ]);
    if (!listing) {
      throw new Error(`Could not retrieve listing with ID: ${listingId}`);
    }
    let _quantity = 1n;
    if (listing.asset.type === "ERC721") {
      if (typeof quantity === "bigint" && (quantity !== 1n || quantity < 0n)) {
        throw new Error("Invalid quantity. This is an ERC721 listing & quantity is always `1n`");
      }
    } else if (listing.asset.type === "ERC1155") {
      if (typeof quantity === "bigint") {
        if (quantity > listing.quantity) {
          throw new Error(`quantity exceeds available amount. Available: ${listing.quantity.toString()}`);
        }
        if (quantity < 0n) {
          throw new Error("Invalid quantity. Should be at least 1n");
        }
        _quantity = quantity;
      }
      _quantity = listing.quantity;
    }
    const buyTx = buyFromListing({
      contract,
      listingId,
      quantity: _quantity,
      recipient: (account == null ? void 0 : account.address) || ""
    });
    const approveTx = await getApprovalForTransaction({
      transaction: buyTx,
      account
    });
    if (approveTx) {
      await mutateAsync(approveTx);
    }
    return buyTx;
  }, [account, contract, quantity, listingId, mutateAsync]);
  return (0, import_jsx_runtime12.jsx)(TransactionButton, { payModal: {
    metadata: defaultPayModalMetadata || payMetadata,
    ...payModal
  }, transaction: () => prepareBuyTransaction(), ...props, children });
}
async function getPayMetadata2(options) {
  var _a, _b, _c, _d;
  const listing = await getListing(options);
  if (!listing) {
    return { name: void 0, image: void 0 };
  }
  return {
    name: (_b = (_a = listing.asset) == null ? void 0 : _a.metadata) == null ? void 0 : _b.name,
    image: (_d = (_c = listing.asset) == null ? void 0 : _c.metadata) == null ? void 0 : _d.image
  };
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/thirdweb/CreateDirectListingButton/index.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var import_react14 = __toESM(require_react(), 1);

// node_modules/thirdweb/dist/esm/extensions/marketplace/__generated__/IDirectListings/write/createListing.js
var FN_SELECTOR = "0x746415b5";
var FN_INPUTS = [
  {
    type: "tuple",
    name: "_params",
    components: [
      {
        type: "address",
        name: "assetContract"
      },
      {
        type: "uint256",
        name: "tokenId"
      },
      {
        type: "uint256",
        name: "quantity"
      },
      {
        type: "address",
        name: "currency"
      },
      {
        type: "uint256",
        name: "pricePerToken"
      },
      {
        type: "uint128",
        name: "startTimestamp"
      },
      {
        type: "uint128",
        name: "endTimestamp"
      },
      {
        type: "bool",
        name: "reserved"
      }
    ]
  }
];
var FN_OUTPUTS = [
  {
    type: "uint256",
    name: "listingId"
  }
];
function createListing(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.params];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/marketplace/direct-listings/write/createListing.js
function createListing2(options) {
  return createListing({
    contract: options.contract,
    asyncParams: async () => {
      const assetContract = getContract({
        ...options.contract,
        address: options.assetContractAddress
      });
      const rpcClient = getRpcClient(options.contract);
      const [assetIsERC721, assetIsERC1155, lastestBlock] = await Promise.all([
        isERC721({ contract: assetContract }),
        isERC1155({ contract: assetContract }),
        eth_getBlockByNumber(rpcClient, { blockTag: "latest" })
      ]);
      if (!assetIsERC721 && !assetIsERC1155) {
        throw new Error("AssetContract must implement ERC 1155 or ERC 721.");
      }
      let startTimestamp = BigInt(Math.floor((options.startTimestamp ?? /* @__PURE__ */ new Date()).getTime() / 1e3));
      const endTimestamp = BigInt(Math.floor((options.endTimestamp ?? new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1e3)).getTime() / 1e3));
      if (startTimestamp <= lastestBlock.timestamp) {
        startTimestamp = lastestBlock.timestamp + 1n;
      }
      if (startTimestamp >= endTimestamp) {
        throw new Error("Start time must be before end time.");
      }
      let quantity;
      if (assetIsERC721) {
        quantity = 1n;
      } else {
        quantity = options.quantity ?? 1n;
      }
      const currencyAddress = options.currencyContractAddress ?? NATIVE_TOKEN_ADDRESS;
      let pricePerToken;
      if ("pricePerToken" in options) {
        if (isNativeTokenAddress(currencyAddress)) {
          pricePerToken = toUnits(options.pricePerToken, 18);
        } else {
          const currencyContract = getContract({
            ...options.contract,
            address: currencyAddress
          });
          const { decimals } = await import("./decimals-K3BF34VF.js");
          const currencyDecimals = await decimals({
            contract: currencyContract
          });
          pricePerToken = toUnits(options.pricePerToken, currencyDecimals);
        }
      } else {
        pricePerToken = BigInt(options.pricePerTokenWei);
      }
      return {
        params: {
          assetContract: options.assetContractAddress,
          tokenId: options.tokenId,
          currency: options.currencyContractAddress ?? NATIVE_TOKEN_ADDRESS,
          quantity,
          pricePerToken,
          startTimestamp,
          endTimestamp,
          reserved: options.isReservedListing ?? false
        },
        overrides: {
          extraGas: 50000n
          // add extra gas to account for router call
        }
      };
    }
  });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/thirdweb/CreateDirectListingButton/index.js
function CreateDirectListingButton(props) {
  const { contractAddress, chain, client, children, payModal, assetContractAddress, tokenId } = props;
  const marketplaceContract = getContract({
    address: contractAddress,
    chain,
    client
  });
  const account = useActiveAccount();
  const defaultPayModalMetadata = payModal ? payModal.metadata : void 0;
  const nftContract = getContract({
    address: assetContractAddress,
    chain,
    client
  });
  const { data: payMetadata } = useReadContract(getPayMetadata3, {
    contract: nftContract,
    tokenId,
    queryOptions: {
      enabled: !defaultPayModalMetadata
    }
  });
  const { mutateAsync } = useSendAndConfirmTransaction();
  const prepareTransaction = (0, import_react14.useCallback)(async () => {
    if (!account) {
      throw new Error("No account detected");
    }
    const [is721, is1155] = await Promise.all([
      isERC721({ contract: nftContract }),
      isERC1155({ contract: nftContract })
    ]);
    if (!is1155 && !is721) {
      throw new Error("Asset must either be ERC721 or ERC1155");
    }
    if (is1155) {
      const [{ isApprovedForAll }, { setApprovalForAll }] = await Promise.all([
        import("./isApprovedForAll-YK5VEV4R.js"),
        import("./setApprovalForAll-44IA7NUE.js")
      ]);
      const isApproved = await isApprovedForAll({
        contract: nftContract,
        operator: marketplaceContract.address,
        owner: account.address
      });
      if (!isApproved) {
        const transaction = setApprovalForAll({
          contract: nftContract,
          operator: marketplaceContract.address,
          approved: true
        });
        await mutateAsync(transaction);
      }
    } else {
      const [{ isApprovedForAll }, { setApprovalForAll }, { getApproved }] = await Promise.all([
        import("./isApprovedForAll-TVBECPIH.js"),
        import("./setApprovalForAll-425GKOQX.js"),
        import("./getApproved-QF4DKI44.js")
      ]);
      const [isApproved, tokenApproved] = await Promise.all([
        isApprovedForAll({
          contract: nftContract,
          operator: marketplaceContract.address,
          owner: account.address
        }),
        getApproved({ contract: nftContract, tokenId: props.tokenId })
      ]);
      if (!isApproved && tokenApproved.toLowerCase() !== marketplaceContract.address.toLowerCase()) {
        const transaction = setApprovalForAll({
          contract: nftContract,
          operator: marketplaceContract.address,
          approved: true
        });
        await mutateAsync(transaction);
      }
    }
    const listingTx = createListing2({
      contract: marketplaceContract,
      ...props
    });
    return listingTx;
  }, [marketplaceContract, props, account, mutateAsync, nftContract]);
  return (0, import_jsx_runtime13.jsx)(TransactionButton, { transaction: () => prepareTransaction(), payModal: {
    metadata: defaultPayModalMetadata || payMetadata,
    ...payModal
  }, ...props, children });
}
async function getPayMetadata3(options) {
  var _a, _b, _c, _d;
  const [{ getContractMetadata: getContractMetadata2 }, { getNFT: getERC721 }, { getNFT: getERC1155 }] = await Promise.all([
    import("./getContractMetadata-XBN6KWXK.js"),
    import("./getNFT-7ELYSCCT.js"),
    import("./getNFT-V2KF4SDD.js")
  ]);
  const [is721, is1155, contractMetadata] = await Promise.all([
    isERC721(options),
    isERC1155(options),
    getContractMetadata2(options)
  ]);
  if (is721) {
    const nft = await getERC721(options);
    return {
      image: (_a = nft == null ? void 0 : nft.metadata) == null ? void 0 : _a.image,
      name: (_b = nft == null ? void 0 : nft.metadata) == null ? void 0 : _b.name
    };
  }
  if (is1155) {
    const nft = await getERC1155(options);
    return {
      image: (_c = nft == null ? void 0 : nft.metadata) == null ? void 0 : _c.image,
      name: (_d = nft == null ? void 0 : nft.metadata) == null ? void 0 : _d.name
    };
  }
  return {
    image: contractMetadata == null ? void 0 : contractMetadata.image,
    name: contractMetadata == null ? void 0 : contractMetadata.name
  };
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/provider.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var import_react15 = __toESM(require_react(), 1);
var NFTProviderContext = (0, import_react15.createContext)(void 0);
function useNFTContext() {
  const ctx = (0, import_react15.useContext)(NFTProviderContext);
  if (!ctx) {
    throw new Error("NFTProviderContext not found. Make sure you are using NFTMedia, NFTDescription, etc. inside a <NFTProvider /> component");
  }
  return ctx;
}
function NFTProvider(props) {
  return (0, import_jsx_runtime14.jsx)(NFTProviderContext.Provider, { value: props, children: props.children });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/name.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/utils.js
async function getNFTInfo(options) {
  return withCache(async () => {
    const nft = await Promise.allSettled([
      getNFT({
        ...options,
        useIndexer: false
        // TODO (insight): switch this call to only call insight once
      }),
      getNFT2({
        ...options,
        useIndexer: false
        // TODO (insight): switch this call to only call insight once
      })
    ]).then(([possibleNFT721, possibleNFT1155]) => {
      if (possibleNFT721.status === "fulfilled" && possibleNFT721.value.tokenURI) {
        return possibleNFT721.value;
      }
      if (possibleNFT1155.status === "fulfilled" && possibleNFT1155.value.tokenURI) {
        return possibleNFT1155.value;
      }
      throw new Error("Failed to load NFT metadata");
    });
    return nft;
  }, {
    cacheKey: `nft_info:${options.contract.chain.id}:${options.contract.address}:${options.tokenId.toString()}`,
    cacheTime: 15 * 60 * 1e3
  });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/name.js
function NFTName({ loadingComponent, fallbackComponent, queryOptions: queryOptions2, nameResolver, ...restProps }) {
  const { contract, tokenId } = useNFTContext();
  const nameQuery = useQuery({
    queryKey: getQueryKey({
      contractAddress: contract.address,
      chainId: contract.chain.id,
      tokenId,
      nameResolver
    }),
    queryFn: async () => fetchNftName({ nameResolver, contract, tokenId }),
    ...queryOptions2
  });
  if (nameQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!nameQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime15.jsx)("span", { ...restProps, children: nameQuery.data });
}
function getQueryKey(props) {
  const { chainId, tokenId, nameResolver, contractAddress } = props;
  return [
    "_internal_nft_name_",
    chainId,
    contractAddress,
    tokenId.toString(),
    {
      resolver: typeof nameResolver === "string" ? nameResolver : typeof nameResolver === "function" ? getFunctionId(nameResolver) : void 0
    }
  ];
}
async function fetchNftName(props) {
  const { nameResolver, contract, tokenId } = props;
  if (typeof nameResolver === "string") {
    return nameResolver;
  }
  if (typeof nameResolver === "function") {
    return nameResolver();
  }
  const nft = await getNFTInfo({ contract, tokenId }).catch(() => void 0);
  if (!nft) {
    throw new Error("Failed to resolve NFT info");
  }
  if (typeof nft.metadata.name !== "string") {
    throw new Error("Failed to resolve NFT name");
  }
  return nft.metadata.name;
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/description.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
function NFTDescription({ loadingComponent, fallbackComponent, queryOptions: queryOptions2, descriptionResolver, ...restProps }) {
  const { contract, tokenId } = useNFTContext();
  const descQuery = useQuery({
    queryKey: [
      "_internal_nft_description_",
      contract.chain.id,
      contract.address,
      tokenId.toString(),
      {
        resolver: typeof descriptionResolver === "string" ? descriptionResolver : typeof descriptionResolver === "function" ? getFunctionId(descriptionResolver) : void 0
      }
    ],
    queryFn: async () => fetchNftDescription({ descriptionResolver, contract, tokenId }),
    ...queryOptions2
  });
  if (descQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!descQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime16.jsx)("span", { ...restProps, children: descQuery.data });
}
async function fetchNftDescription(props) {
  const { descriptionResolver, contract, tokenId } = props;
  if (typeof descriptionResolver === "string") {
    return descriptionResolver;
  }
  if (typeof descriptionResolver === "function") {
    return descriptionResolver();
  }
  const nft = await getNFTInfo({ contract, tokenId }).catch(() => void 0);
  if (!nft) {
    throw new Error("Failed to resolve NFT info");
  }
  if (typeof nft.metadata.description !== "string") {
    throw new Error("Failed to resolve NFT description");
  }
  return nft.metadata.description;
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/NFT/media.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
function NFTMedia({ loadingComponent, fallbackComponent, queryOptions: queryOptions2, mediaResolver, ...mediaRendererProps }) {
  const { contract, tokenId } = useNFTContext();
  const mediaQuery = useQuery({
    queryKey: getQueryKey2({
      contractAddress: contract.address,
      chainId: contract.chain.id,
      tokenId,
      mediaResolver
    }),
    queryFn: async () => fetchNftMedia({ mediaResolver, contract, tokenId }),
    ...queryOptions2
  });
  if (mediaQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!mediaQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime17.jsx)(MediaRenderer, { client: contract.client, src: mediaQuery.data.src, poster: mediaQuery.data.poster, ...mediaRendererProps });
}
function getQueryKey2(props) {
  const { chainId, tokenId, mediaResolver, contractAddress } = props;
  return [
    "_internal_nft_media_",
    chainId,
    contractAddress,
    tokenId.toString(),
    {
      resolver: typeof mediaResolver === "object" ? mediaResolver : typeof mediaResolver === "function" ? getFunctionId(mediaResolver) : void 0
    }
  ];
}
async function fetchNftMedia(props) {
  const { mediaResolver, contract, tokenId } = props;
  if (typeof mediaResolver === "object") {
    return mediaResolver;
  }
  if (typeof mediaResolver === "function") {
    return mediaResolver();
  }
  const nft = await getNFTInfo({ contract, tokenId }).catch(() => void 0);
  if (!nft) {
    throw new Error("Failed to resolve NFT info");
  }
  const animation_url = nft.metadata.animation_url;
  const image = nft.metadata.image || nft.metadata.image_url;
  if (animation_url) {
    return {
      src: animation_url,
      poster: image || void 0
    };
  }
  if (image) {
    return {
      src: image,
      poster: void 0
    };
  }
  throw new Error("Failed to resolve NFT media");
}

// node_modules/thirdweb/dist/esm/react/web/ui/SiteEmbed.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
function SiteEmbed({ src, client, ecosystem, ...props }) {
  if (!client.clientId) {
    throw new Error("The SiteEmbed client must have a clientId");
  }
  const activeWallet = useActiveWallet();
  const walletId = activeWallet == null ? void 0 : activeWallet.id;
  const { data: { authProvider, authCookie } = {} } = useQuery({
    queryKey: ["site-embed", walletId, src, client.clientId, ecosystem],
    enabled: activeWallet && (isEcosystemWallet(activeWallet) || walletId === "inApp" || walletId === "smart"),
    queryFn: async () => {
      const storage = new ClientScopedStorage({
        storage: webLocalStorage,
        clientId: client.clientId,
        ecosystem
      });
      const authProvider2 = await getLastAuthProvider(webLocalStorage);
      const authCookie2 = await storage.getAuthCookie();
      return { authProvider: authProvider2, authCookie: authCookie2 };
    }
  });
  const url = new URL(src);
  if (walletId) {
    url.searchParams.set("walletId", walletId === "smart" ? "inApp" : walletId);
  }
  if (authProvider) {
    url.searchParams.set("authProvider", authProvider);
  }
  if (authCookie) {
    url.searchParams.set("authCookie", authCookie);
  }
  return (0, import_jsx_runtime18.jsx)("iframe", { src: encodeURI(url.toString()), width: "100%", height: "100%", allowFullScreen: true, ...props });
}

// node_modules/thirdweb/dist/esm/react/web/ui/SiteLink.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
function SiteLink({ href, client, ecosystem, children, ...props }) {
  if (!client.clientId) {
    throw new Error("The SiteLink client must have a clientId");
  }
  const activeWallet = useActiveWallet();
  const walletId = activeWallet == null ? void 0 : activeWallet.id;
  const { data: { authProvider, authCookie } = {} } = useQuery({
    queryKey: ["site-link", walletId, href, client.clientId, ecosystem],
    enabled: activeWallet && (isEcosystemWallet(activeWallet) || walletId === "inApp" || walletId === "smart"),
    queryFn: async () => {
      const storage = new ClientScopedStorage({
        storage: webLocalStorage,
        clientId: client.clientId,
        ecosystem
      });
      const authProvider2 = await getLastAuthProvider(webLocalStorage);
      const authCookie2 = await storage.getAuthCookie();
      return { authProvider: authProvider2, authCookie: authCookie2 };
    }
  });
  const url = new URL(href);
  if (walletId) {
    url.searchParams.set("walletId", walletId === "smart" ? "inApp" : walletId);
  }
  if (authProvider) {
    url.searchParams.set("authProvider", authProvider);
  }
  if (authCookie) {
    url.searchParams.set("authCookie", authCookie);
  }
  return (0, import_jsx_runtime19.jsx)("a", { href: encodeURI(url.toString()), ...props, children });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Token/provider.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
var import_react16 = __toESM(require_react(), 1);
var TokenProviderContext = (0, import_react16.createContext)(void 0);
function TokenProvider(props) {
  return (0, import_jsx_runtime20.jsx)(TokenProviderContext.Provider, { value: props, children: props.children });
}
function useTokenContext() {
  const ctx = (0, import_react16.useContext)(TokenProviderContext);
  if (!ctx) {
    throw new Error("TokenProviderContext not found. Make sure you are using TokenName, TokenIcon, TokenSymbol etc. inside a <TokenProvider /> component");
  }
  return ctx;
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Token/name.js
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
function TokenName({ nameResolver, formatFn, loadingComponent, fallbackComponent, queryOptions: queryOptions2, ...restProps }) {
  const { address, client, chain } = useTokenContext();
  const nameQuery = useQuery({
    queryKey: getQueryKeys({ chainId: chain.id, nameResolver, address }),
    queryFn: async () => fetchTokenName({ address, chain, client, nameResolver }),
    ...queryOptions2
  });
  if (nameQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!nameQuery.data) {
    return fallbackComponent || null;
  }
  if (formatFn && typeof formatFn === "function") {
    return (0, import_jsx_runtime21.jsx)("span", { ...restProps, children: formatFn(nameQuery.data) });
  }
  return (0, import_jsx_runtime21.jsx)("span", { ...restProps, children: nameQuery.data });
}
async function fetchTokenName(props) {
  const { nameResolver, address, client, chain } = props;
  if (typeof nameResolver === "string") {
    return nameResolver;
  }
  if (typeof nameResolver === "function") {
    return nameResolver();
  }
  if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {
    return getChainMetadata(chain).then((data) => data.nativeCurrency.name);
  }
  const contract = getContract({ address, client, chain });
  const [_name, contractMetadata] = await Promise.all([
    name({ contract }).catch(() => void 0),
    getContractMetadata({ contract }).catch(() => void 0)
  ]);
  if (typeof _name === "string") {
    return _name;
  }
  if (typeof (contractMetadata == null ? void 0 : contractMetadata.name) === "string") {
    return contractMetadata.name;
  }
  throw new Error("Failed to resolve name from both name() and contract metadata");
}
function getQueryKeys(props) {
  const { chainId, address, nameResolver } = props;
  return [
    "_internal_token_name_",
    chainId,
    address,
    {
      resolver: typeof nameResolver === "string" ? nameResolver : typeof nameResolver === "function" ? getFunctionId(nameResolver) : void 0
    }
  ];
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Token/symbol.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime(), 1);
function TokenSymbol2({ symbolResolver, formatFn, loadingComponent, fallbackComponent, queryOptions: queryOptions2, ...restProps }) {
  const { address, client, chain } = useTokenContext();
  const symbolQuery = useQuery({
    queryKey: getQueryKeys2({ chainId: chain.id, address, symbolResolver }),
    queryFn: async () => fetchTokenSymbol({ symbolResolver, address, chain, client }),
    ...queryOptions2
  });
  if (symbolQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!symbolQuery.data) {
    return fallbackComponent || null;
  }
  if (formatFn && typeof formatFn === "function") {
    return (0, import_jsx_runtime22.jsx)("span", { ...restProps, children: formatFn(symbolQuery.data) });
  }
  return (0, import_jsx_runtime22.jsx)("span", { ...restProps, children: symbolQuery.data });
}
async function fetchTokenSymbol(props) {
  const { symbolResolver, address, client, chain } = props;
  if (typeof symbolResolver === "string") {
    return symbolResolver;
  }
  if (typeof symbolResolver === "function") {
    return symbolResolver();
  }
  if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {
    return getChainMetadata(chain).then((data) => data.nativeCurrency.symbol);
  }
  const contract = getContract({ address, client, chain });
  const [_symbol, contractMetadata] = await Promise.all([
    symbol({ contract }).catch(() => void 0),
    getContractMetadata({ contract }).catch(() => void 0)
  ]);
  if (typeof _symbol === "string") {
    return _symbol;
  }
  if (typeof (contractMetadata == null ? void 0 : contractMetadata.symbol) === "string") {
    return contractMetadata.symbol;
  }
  throw new Error("Failed to resolve symbol from both symbol() and contract metadata");
}
function getQueryKeys2(props) {
  const { chainId, address, symbolResolver } = props;
  return [
    "_internal_token_symbol_",
    chainId,
    address,
    {
      resolver: typeof symbolResolver === "string" ? symbolResolver : typeof symbolResolver === "function" ? getFunctionId(symbolResolver) : void 0
    }
  ];
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Token/icon.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime(), 1);
function TokenIcon({ iconResolver, loadingComponent, fallbackComponent, queryOptions: queryOptions2, ...restProps }) {
  const { address, client, chain } = useTokenContext();
  const iconQuery = useQuery({
    queryKey: [
      "_internal_token_icon_",
      chain.id,
      address,
      {
        resolver: typeof iconResolver === "string" ? iconResolver : typeof iconResolver === "function" ? getFunctionId(iconResolver) : void 0
      }
    ],
    queryFn: async () => {
      if (typeof iconResolver === "string") {
        return iconResolver;
      }
      if (typeof iconResolver === "function") {
        return iconResolver();
      }
      if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {
        const possibleUrl = await getChainMetadata(chain).then((data) => {
          var _a;
          return (_a = data.icon) == null ? void 0 : _a.url;
        });
        if (!possibleUrl) {
          throw new Error("Failed to resolve icon for native token");
        }
        return resolveScheme({ uri: possibleUrl, client });
      }
      const contractMetadata = await getContractMetadata({
        contract: getContract({
          address,
          chain,
          client
        })
      });
      if (!contractMetadata.image || typeof contractMetadata.image !== "string") {
        throw new Error("Failed to resolve token icon from contract metadata");
      }
      return resolveScheme({
        uri: contractMetadata.image,
        client
      });
    },
    ...queryOptions2
  });
  if (iconQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!iconQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime23.jsx)("img", { src: iconQuery.data, ...restProps, alt: restProps.alt });
}

// node_modules/thirdweb/dist/esm/react/web/utils/storage.js
async function getLastAuthProvider2() {
  return getLastAuthProvider(webLocalStorage);
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Wallet/icon.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime(), 1);
function WalletIcon({ loadingComponent, fallbackComponent, queryOptions: queryOptions2, ...restProps }) {
  const imageQuery = useWalletIcon({ queryOptions: queryOptions2 });
  if (imageQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!imageQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime24.jsx)("img", { src: imageQuery.data, ...restProps, alt: restProps.alt });
}
function SocialIcon({ provider, ...restProps }) {
  const src = getSocialIcon(provider);
  return (0, import_jsx_runtime24.jsx)("img", { src, ...restProps, alt: restProps.alt });
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Wallet/name.js
var import_jsx_runtime25 = __toESM(require_jsx_runtime(), 1);

// node_modules/thirdweb/dist/esm/react/core/utils/walletname.js
function useWalletName(props) {
  const { id } = useWalletContext();
  const nameQuery = useQuery({
    queryKey: getQueryKeys3({ id, formatFn: props.formatFn }),
    queryFn: async () => fetchWalletName({ id, formatFn: props.formatFn }),
    ...props.queryOptions
  });
  return nameQuery;
}
function getQueryKeys3(props) {
  if (typeof props.formatFn === "function") {
    return [
      "walletName",
      props.id,
      { resolver: getFunctionId(props.formatFn) }
    ];
  }
  return ["walletName", props.id];
}
async function fetchWalletName(props) {
  const info = await getWalletInfo(props.id);
  if (typeof props.formatFn === "function") {
    return props.formatFn(info.name);
  }
  return info.name;
}

// node_modules/thirdweb/dist/esm/react/web/ui/prebuilt/Wallet/name.js
function WalletName({ loadingComponent, fallbackComponent, queryOptions: queryOptions2, formatFn, ...restProps }) {
  const nameQuery = useWalletName({ queryOptions: queryOptions2, formatFn });
  if (nameQuery.isLoading) {
    return loadingComponent || null;
  }
  if (!nameQuery.data) {
    return fallbackComponent || null;
  }
  return (0, import_jsx_runtime25.jsx)("span", { ...restProps, children: nameQuery.data });
}
export {
  AccountAddress,
  AccountAvatar,
  AccountBalance,
  AccountBlobbie,
  AccountName,
  AccountProvider,
  AutoConnect,
  Blobbie,
  BuyDirectListingButton,
  ChainIcon,
  ChainName,
  ChainProvider,
  ClaimButton,
  ConnectButton,
  ConnectEmbed,
  CreateDirectListingButton,
  MediaRenderer,
  NFTDescription,
  NFTMedia,
  NFTName,
  NFTProvider,
  PayEmbed,
  SiteEmbed,
  SiteLink,
  SocialIcon,
  ThirdwebProvider,
  TokenIcon,
  TokenName,
  TokenProvider,
  TokenSymbol2 as TokenSymbol,
  TransactionButton,
  WalletIcon,
  WalletName,
  WalletProvider,
  createContractQuery,
  darkTheme,
  defaultTokens,
  getDefaultToken,
  getLastAuthProvider2 as getLastAuthProvider,
  lightTheme,
  useActiveAccount,
  useActiveWallet,
  useActiveWalletChain,
  useActiveWalletConnectionStatus,
  useAdminWallet,
  useAutoConnect,
  useBlockNumber,
  useBuyHistory,
  useBuyWithCryptoHistory,
  useBuyWithCryptoQuote,
  useBuyWithCryptoStatus,
  useBuyWithFiatHistory,
  useBuyWithFiatQuote,
  useBuyWithFiatStatus,
  useCapabilities,
  useChainMetadata,
  useConnect,
  useConnectModal,
  useConnectedWallets,
  useConnectionManager,
  useContractEvents,
  useDisconnect,
  useEnsAvatar,
  useEnsName,
  useEstimateGas,
  useEstimateGasCost,
  useInvalidateContractQuery,
  useIsAutoConnecting,
  useLinkProfile,
  useNetworkSwitcherModal,
  usePostOnRampQuote,
  useProfiles,
  useReadContract,
  useSendAndConfirmCalls,
  useSendAndConfirmTransaction,
  useSendBatchTransaction,
  useSendCalls,
  useSendTransaction,
  useSetActiveWallet,
  useSetActiveWalletConnectionStatus,
  useSimulateTransaction,
  useSiweAuth,
  useSocialProfiles,
  useSwitchActiveWalletChain,
  useUnlinkProfile,
  useWaitForCallsReceipt,
  useWaitForReceipt,
  useWalletBalance,
  useWalletDetailsModal,
  useWalletImage,
  useWalletInfo
};
//# sourceMappingURL=thirdweb_react.js.map
