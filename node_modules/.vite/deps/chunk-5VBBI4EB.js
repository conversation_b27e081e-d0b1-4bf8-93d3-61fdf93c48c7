import {
  resolveContractAbi
} from "./chunk-LFHG7EDC.js";
import {
  parseAbiItem
} from "./chunk-HXWRQBIO.js";

// node_modules/thirdweb/dist/esm/transaction/resolve-method.js
function resolveMethod(method) {
  return async (contract) => {
    var _a;
    if (typeof method === "string" && method.startsWith("function ")) {
      return parseAbiItem(method);
    }
    const resolvedAbi = ((_a = contract.abi) == null ? void 0 : _a.length) ? contract.abi : await resolveContractAbi(contract);
    const abiFunction = resolvedAbi.find((item) => {
      if (item.type !== "function") {
        return false;
      }
      return item.name === method;
    });
    if (!abiFunction) {
      throw new Error(`could not find function with name "${method}" in abi`);
    }
    return abiFunction;
  };
}

// node_modules/thirdweb/dist/esm/transaction/actions/eip7702/authorization.js
async function signAuthorization(options) {
  const { account, request } = options;
  if (typeof account.signAuthorization === "undefined") {
    throw new Error("This account type does not yet support signing EIP-7702 authorizations");
  }
  return account.signAuthorization(request);
}

export {
  resolveMethod,
  signAuthorization
};
//# sourceMappingURL=chunk-5VBBI4EB.js.map
