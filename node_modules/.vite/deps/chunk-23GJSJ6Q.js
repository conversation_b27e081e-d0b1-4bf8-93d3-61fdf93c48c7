import {
  totalSupply
} from "./chunk-DKG2WVWA.js";
import {
  fetchTokenMetadata
} from "./chunk-FVFUN77Y.js";
import {
  parseNFT
} from "./chunk-CYP47DRD.js";
import {
  getNFT
} from "./chunk-H5DL3D6Z.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  readContract
} from "./chunk-OIHZCUZQ.js";

// node_modules/thirdweb/dist/esm/extensions/erc1155/__generated__/IERC1155/read/uri.js
var FN_SELECTOR = "0x0e89341c";
var FN_INPUTS = [
  {
    type: "uint256",
    name: "tokenId"
  }
];
var FN_OUTPUTS = [
  {
    type: "string"
  }
];
function isUriSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
async function uri(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: [options.tokenId]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc1155/read/getNFT.js
async function getNFT2(options) {
  const { useIndexer = true } = options;
  if (useIndexer) {
    try {
      return await getNFTFromInsight(options);
    } catch {
      return await getNFTFromRPC(options);
    }
  }
  return await getNFTFromRPC(options);
}
async function getNFTFromInsight(options) {
  const nft = await getNFT({
    client: options.contract.client,
    chain: options.contract.chain,
    contractAddress: options.contract.address,
    tokenId: options.tokenId
  });
  if (!nft) {
    return getNFTFromRPC(options);
  }
  return nft;
}
async function getNFTFromRPC(options) {
  const [tokenUri, supply] = await Promise.all([
    uri({
      contract: options.contract,
      tokenId: options.tokenId
    }),
    totalSupply({
      contract: options.contract,
      id: options.tokenId
      // in cases where the supply is not available -> fall back to 0
    }).catch(() => 0n)
  ]);
  return parseNFT(await fetchTokenMetadata({
    client: options.contract.client,
    tokenId: options.tokenId,
    tokenUri
  }).catch(() => ({
    id: options.tokenId,
    type: "ERC1155",
    uri: tokenUri
  })), {
    tokenId: options.tokenId,
    tokenUri,
    type: "ERC1155",
    owner: null,
    supply,
    tokenAddress: options.contract.address,
    chainId: options.contract.chain.id
  });
}

export {
  isUriSupported,
  getNFT2 as getNFT
};
//# sourceMappingURL=chunk-23GJSJ6Q.js.map
