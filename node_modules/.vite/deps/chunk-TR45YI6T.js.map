{"version": 3, "sources": ["../../thirdweb/src/wallets/wallet-connect/receiver/request-handlers/send-raw-transaction.ts", "../../thirdweb/src/wallets/wallet-connect/receiver/utils.ts", "../../thirdweb/src/wallets/wallet-connect/receiver/request-handlers/send-transaction.ts", "../../thirdweb/src/wallets/wallet-connect/receiver/request-handlers/sign-transaction.ts", "../../thirdweb/src/wallets/wallet-connect/receiver/request-handlers/sign-typed-data.ts", "../../thirdweb/src/wallets/wallet-connect/receiver/request-handlers/sign.ts"], "sourcesContent": ["import type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport type { WalletConnectRawTransactionRequestParams } from \"../types.js\";\n\n/**\n * @internal\n */\nexport async function handleSendRawTransactionRequest(options: {\n  account: Account;\n  chainId: number;\n  params: WalletConnectRawTransactionRequestParams;\n}): Promise<Hex> {\n  const {\n    account,\n    chainId,\n    params: [rawTransaction],\n  } = options;\n\n  if (!account.sendRawTransaction) {\n    throw new Error(\n      \"The current account does not support sending raw transactions\",\n    );\n  }\n\n  const txResult = await account.sendRawTransaction({\n    rawTransaction,\n    chainId,\n  });\n  return txResult.transactionHash;\n}\n", "import { type Address, checksumAddress } from \"../../../utils/address.js\";\nimport type { Account } from \"../../interfaces/wallet.js\";\n\n/**\n * @internal\n */\nexport function validateAccountAddress(account: Account, address: Address) {\n  if (checksumAddress(account.address) !== checksumAddress(address)) {\n    throw new Error(\n      `Failed to validate account address (${account.address}), differs from ${address}`,\n    );\n  }\n}\n\n/**\n * @internal\n */\nexport function parseEip155ChainId(chainId: string): number {\n  const chainIdParts = chainId.split(\":\");\n  const chainIdAsNumber = Number.parseInt(chainIdParts[1] ?? \"0\");\n  if (\n    chainIdParts.length !== 2 ||\n    chainIdParts[0] !== \"eip155\" ||\n    chainIdAsNumber === 0 ||\n    !chainIdAsNumber\n  ) {\n    throw new Error(\n      `Invalid chainId ${chainId}, should have the format 'eip155:1'`,\n    );\n  }\n  return chainIdAsNumber;\n}\n", "import { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { sendTransaction } from \"../../../../transaction/actions/send-transaction.js\";\nimport { prepareTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { type Hex, hexToBigInt } from \"../../../../utils/encoding/hex.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport type { WalletConnectTransactionRequestParams } from \"../types.js\";\nimport { validateAccountAddress } from \"../utils.js\";\n\n/**\n * @internal\n */\nexport async function handleSendTransactionRequest(options: {\n  account: Account;\n  chainId: number;\n  thirdwebClient: ThirdwebClient;\n  params: WalletConnectTransactionRequestParams;\n}): Promise<Hex> {\n  const {\n    account,\n    chainId,\n    thirdwebClient,\n    params: [transaction],\n  } = options;\n\n  if (transaction.from !== undefined) {\n    validateAccountAddress(account, transaction.from);\n  }\n\n  const preparedTransaction = prepareTransaction({\n    gas: transaction.gas ? hexToBigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice\n      ? hexToBigInt(transaction.gasPrice)\n      : undefined,\n    value: transaction.value ? hexToBigInt(transaction.value) : undefined,\n    to: transaction.to,\n    data: transaction.data,\n    chain: getCachedChain(chainId),\n    client: thirdwebClient,\n  });\n\n  const txResult = await sendTransaction({\n    transaction: preparedTransaction,\n    account,\n  });\n\n  return txResult.transactionHash;\n}\n", "import {\n  type Hex,\n  hexToBigInt,\n  hexToNumber,\n} from \"../../../../utils/encoding/hex.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport type { WalletConnectTransactionRequestParams } from \"../types.js\";\nimport { validateAccountAddress } from \"../utils.js\";\n\n/**\n * @internal\n */\nexport async function handleSignTransactionRequest(options: {\n  account: Account;\n  params: WalletConnectTransactionRequestParams;\n}): Promise<Hex> {\n  const {\n    account,\n    params: [transaction],\n  } = options;\n\n  if (!account.signTransaction) {\n    throw new Error(\n      \"The current account does not support signing transactions\",\n    );\n  }\n\n  if (transaction.from !== undefined) {\n    validateAccountAddress(account, transaction.from);\n  }\n\n  return account.signTransaction({\n    gas: transaction.gas ? hexToBigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice\n      ? hexToBigInt(transaction.gasPrice)\n      : undefined,\n    value: transaction.value ? hexToBigInt(transaction.value) : undefined,\n    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : undefined,\n    to: transaction.to,\n    data: transaction.data,\n  });\n}\n", "import type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport type { WalletConnectSignTypedDataRequestParams } from \"../types.js\";\nimport { validateAccountAddress } from \"../utils.js\";\n\n/**\n * @internal\n */\nexport async function handleSignTypedDataRequest(options: {\n  account: Account;\n  params: WalletConnectSignTypedDataRequestParams;\n}): Promise<Hex> {\n  const { account, params } = options;\n\n  validateAccountAddress(account, params[0]);\n\n  return account.signTypedData(\n    // The data could be sent to us as a string or object, depending on the level of parsing on the client side\n    typeof params[1] === \"string\" ? JSON.parse(params[1]) : params[1],\n  );\n}\n", "import type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport type { Account } from \"../../../interfaces/wallet.js\";\nimport type { WalletConnectSignRequestPrams } from \"../types.js\";\nimport { validateAccountAddress } from \"../utils.js\";\n\n/**\n * @internal\n */\nexport async function handleSignRequest(options: {\n  account: Account;\n  params: WalletConnectSignRequestPrams;\n}): Promise<Hex> {\n  const { account, params } = options;\n\n  validateAccountAddress(account, params[1]);\n  return account.signMessage({ message: { raw: params[0] as Hex } });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,eAAsB,gCAAgC,SAIrD;AACC,QAAM,EACJ,SACA,SACA,QAAQ,CAAC,cAAc,EAAC,IACtB;AAEJ,MAAI,CAAC,QAAQ,oBAAoB;AAC/B,UAAM,IAAI,MACR,+DAA+D;EAEnE;AAEA,QAAM,WAAW,MAAM,QAAQ,mBAAmB;IAChD;IACA;GACD;AACD,SAAO,SAAS;AAClB;;;ACvBM,SAAU,uBAAuB,SAAkB,SAAgB;AACvE,MAAI,gBAAgB,QAAQ,OAAO,MAAM,gBAAgB,OAAO,GAAG;AACjE,UAAM,IAAI,MACR,uCAAuC,QAAQ,OAAO,mBAAmB,OAAO,EAAE;EAEtF;AACF;AAKM,SAAU,mBAAmB,SAAe;AAChD,QAAM,eAAe,QAAQ,MAAM,GAAG;AACtC,QAAM,kBAAkB,OAAO,SAAS,aAAa,CAAC,KAAK,GAAG;AAC9D,MACE,aAAa,WAAW,KACxB,aAAa,CAAC,MAAM,YACpB,oBAAoB,KACpB,CAAC,iBACD;AACA,UAAM,IAAI,MACR,mBAAmB,OAAO,qCAAqC;EAEnE;AACA,SAAO;AACT;;;ACnBA,eAAsB,6BAA6B,SAKlD;AACC,QAAM,EACJ,SACA,SACA,gBACA,QAAQ,CAAC,WAAW,EAAC,IACnB;AAEJ,MAAI,YAAY,SAAS,QAAW;AAClC,2BAAuB,SAAS,YAAY,IAAI;EAClD;AAEA,QAAM,sBAAsB,mBAAmB;IAC7C,KAAK,YAAY,MAAM,YAAY,YAAY,GAAG,IAAI;IACtD,UAAU,YAAY,WAClB,YAAY,YAAY,QAAQ,IAChC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY;IAChB,MAAM,YAAY;IAClB,OAAO,eAAe,OAAO;IAC7B,QAAQ;GACT;AAED,QAAM,WAAW,MAAM,gBAAgB;IACrC,aAAa;IACb;GACD;AAED,SAAO,SAAS;AAClB;;;ACnCA,eAAsB,6BAA6B,SAGlD;AACC,QAAM,EACJ,SACA,QAAQ,CAAC,WAAW,EAAC,IACnB;AAEJ,MAAI,CAAC,QAAQ,iBAAiB;AAC5B,UAAM,IAAI,MACR,2DAA2D;EAE/D;AAEA,MAAI,YAAY,SAAS,QAAW;AAClC,2BAAuB,SAAS,YAAY,IAAI;EAClD;AAEA,SAAO,QAAQ,gBAAgB;IAC7B,KAAK,YAAY,MAAM,YAAY,YAAY,GAAG,IAAI;IACtD,UAAU,YAAY,WAClB,YAAY,YAAY,QAAQ,IAChC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY;IAChB,MAAM,YAAY;GACnB;AACH;;;ACjCA,eAAsB,2BAA2B,SAGhD;AACC,QAAM,EAAE,SAAS,OAAM,IAAK;AAE5B,yBAAuB,SAAS,OAAO,CAAC,CAAC;AAEzC,SAAO,QAAQ;;IAEb,OAAO,OAAO,CAAC,MAAM,WAAW,KAAK,MAAM,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC;EAAC;AAErE;;;ACZA,eAAsB,kBAAkB,SAGvC;AACC,QAAM,EAAE,SAAS,OAAM,IAAK;AAE5B,yBAAuB,SAAS,OAAO,CAAC,CAAC;AACzC,SAAO,QAAQ,YAAY,EAAE,SAAS,EAAE,KAAK,OAAO,CAAC,EAAQ,EAAE,CAAE;AACnE;", "names": []}