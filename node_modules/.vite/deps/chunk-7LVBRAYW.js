import {
  waitFor<PERSON><PERSON>eipt
} from "./chunk-LU6EOOP6.js";
import {
  sendTransaction
} from "./chunk-K4XAEHXR.js";
import {
  encode
} from "./chunk-DVJU3TKU.js";
import {
  resolvePromisedValue
} from "./chunk-LFHG7EDC.js";

// node_modules/thirdweb/dist/esm/transaction/actions/send-and-confirm-transaction.js
async function sendAndConfirmTransaction(options) {
  const submittedTx = await sendTransaction(options);
  return waitForReceipt(submittedTx);
}

// node_modules/thirdweb/dist/esm/transaction/actions/send-batch-transaction.js
async function sendBatchTransaction(options) {
  const { account, transactions } = options;
  if (!account) {
    throw new Error("not connected");
  }
  if (transactions.length === 0) {
    throw new Error("No transactions to send");
  }
  const firstTx = transactions[0];
  if (!firstTx) {
    throw new Error("No transactions to send");
  }
  if (account.sendBatchTransaction) {
    const serializedTxs = await Promise.all(transactions.map(async (tx) => {
      const [data, to, accessList, value] = await Promise.all([
        encode(tx),
        resolvePromisedValue(tx.to),
        resolvePromisedValue(tx.accessList),
        resolvePromisedValue(tx.value)
      ]);
      const serializedTx = {
        data,
        chainId: tx.chain.id,
        to,
        value,
        accessList
      };
      return serializedTx;
    }));
    const result = await account.sendBatchTransaction(serializedTxs);
    return {
      ...result,
      chain: firstTx.chain,
      client: firstTx.client
    };
  }
  throw new Error("Account doesn't implement sendBatchTransaction");
}

export {
  sendAndConfirmTransaction,
  sendBatchTransaction
};
//# sourceMappingURL=chunk-7LVBRAYW.js.map
