{"version": 3, "sources": ["../../thirdweb/src/wallets/smart/lib/calls.ts"], "sourcesContent": ["import type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport {\n  type ThirdwebContract,\n  getContract,\n} from \"../../../contract/contract.js\";\nimport { prepareContractCall } from \"../../../transaction/prepare-contract-call.js\";\nimport type { PreparedTransaction } from \"../../../transaction/prepare-transaction.js\";\nimport { readContract } from \"../../../transaction/read-contract.js\";\nimport { isHex, stringToHex } from \"../../../utils/encoding/hex.js\";\nimport { withCache } from \"../../../utils/promise/withCache.js\";\nimport type { SendTransactionOption } from \"../../interfaces/wallet.js\";\nimport { DEFAULT_ACCOUNT_FACTORY_V0_6 } from \"./constants.js\";\n\n/**\n * Predict the address of a smart account.\n * @param args - The options for predicting the address of a smart account.\n * @returns The predicted address of the smart account.\n * @example\n * ```ts\n * import { predictSmartAccountAddress } from \"thirdweb/wallets/smart\";\n *\n * const predictedAddress = await predictSmartAccountAddress({\n *  client,\n *  chain,\n *  adminAddress,\n * });\n * ```\n * @walletUtils\n */\nexport async function predictSmartAccountAddress(args: {\n  client: ThirdwebClient;\n  chain: Chain;\n  adminAddress: string;\n  factoryAddress?: string;\n  accountSalt?: string;\n}): Promise<string> {\n  return predictAddress({\n    adminAddress: args.adminAddress,\n    accountSalt: args.accountSalt,\n    factoryContract: getContract({\n      address: args.factoryAddress ?? DEFAULT_ACCOUNT_FACTORY_V0_6,\n      chain: args.chain,\n      client: args.client,\n    }),\n  });\n}\n\n/**\n * Predict the address of a smart account.\n * @param args - The options for predicting the address of a smart account.\n * @returns The predicted address of the smart account.\n * @example\n * ```ts\n * import { predictAddress } from \"thirdweb/wallets/smart\";\n *\n * const predictedAddress = await predictAddress({\n *  factoryContract,\n *  adminAddress,\n *  accountSalt,\n * });\n * ```\n * @walletUtils\n * @deprecated Use `predictSmartAccountAddress` instead.\n */\nexport async function predictAddress(args: {\n  factoryContract: ThirdwebContract;\n  predictAddressOverride?: (\n    factoryContract: ThirdwebContract,\n    admin: string,\n  ) => Promise<string>;\n  adminAddress: string;\n  accountSalt?: string;\n  accountAddress?: string;\n}): Promise<string> {\n  const {\n    factoryContract,\n    predictAddressOverride: predictAddress,\n    adminAddress,\n    accountSalt,\n    accountAddress,\n  } = args;\n  if (predictAddress) {\n    return predictAddress(factoryContract, adminAddress);\n  }\n  if (accountAddress) {\n    return accountAddress;\n  }\n  if (!adminAddress) {\n    throw new Error(\n      \"Account address is required to predict the smart wallet address.\",\n    );\n  }\n  return withCache(\n    async () => {\n      const saltHex =\n        accountSalt && isHex(accountSalt)\n          ? accountSalt\n          : stringToHex(accountSalt ?? \"\");\n      let result: string | undefined;\n      let retries = 0;\n      const maxRetries = 3;\n\n      while (retries <= maxRetries) {\n        try {\n          result = await readContract({\n            contract: factoryContract,\n            method: \"function getAddress(address, bytes) returns (address)\",\n            params: [adminAddress, saltHex],\n          });\n          break;\n        } catch (error) {\n          if (retries === maxRetries) {\n            throw error;\n          }\n\n          // Exponential backoff: 2^(retries + 1) * 200ms (400ms, 800ms, 1600ms)\n          const delay = 2 ** (retries + 1) * 200;\n          await new Promise((resolve) => setTimeout(resolve, delay));\n          retries++;\n        }\n      }\n      if (!result) {\n        throw new Error(\n          `No smart account address found for admin address ${adminAddress} and salt ${accountSalt}`,\n        );\n      }\n      return result;\n    },\n    {\n      cacheKey: `${args.factoryContract.chain.id}-${args.factoryContract.address}-${args.adminAddress}-${args.accountSalt}`,\n      cacheTime: 1000 * 60 * 60 * 24, // 1 day\n    },\n  );\n}\n\n/**\n * @internal\n */\nexport function prepareCreateAccount(args: {\n  factoryContract: ThirdwebContract;\n  adminAddress: string;\n  accountSalt?: string;\n  createAccountOverride?: (\n    factoryContract: ThirdwebContract,\n    admin: string,\n  ) => PreparedTransaction;\n}): PreparedTransaction {\n  const {\n    adminAddress,\n    factoryContract,\n    createAccountOverride: createAccount,\n    accountSalt,\n  } = args;\n  if (createAccount) {\n    return createAccount(factoryContract, adminAddress);\n  }\n  const saltHex =\n    accountSalt && isHex(accountSalt)\n      ? accountSalt\n      : stringToHex(accountSalt ?? \"\");\n  return prepareContractCall({\n    contract: factoryContract,\n    method: \"function createAccount(address, bytes) returns (address)\",\n    params: [adminAddress, saltHex],\n  });\n}\n\n/**\n * @internal\n */\nexport function prepareExecute(args: {\n  accountContract: ThirdwebContract;\n  transaction: SendTransactionOption;\n  executeOverride?: (\n    accountContract: ThirdwebContract,\n    transaction: SendTransactionOption,\n  ) => PreparedTransaction;\n}): PreparedTransaction {\n  const { accountContract, transaction, executeOverride: execute } = args;\n  if (execute) {\n    return execute(accountContract, transaction);\n  }\n  let value = transaction.value || 0n;\n  // special handling of hedera chains, decimals for native value is 8 instead of 18 when passed as contract params\n  if (transaction.chainId === 295 || transaction.chainId === 296) {\n    value = BigInt(value) / BigInt(10 ** 10);\n  }\n  return prepareContractCall({\n    contract: accountContract,\n    method: \"function execute(address, uint256, bytes)\",\n    params: [transaction.to || \"\", value, transaction.data || \"0x\"],\n    // if gas is specified for the inner tx, use that and add 21k for the execute call on the account contract\n    // this avoids another estimateGas call when bundling the userOp\n    // and also allows for passing custom gas limits for the inner tx\n    gas: transaction.gas ? transaction.gas + 21000n : undefined,\n  });\n}\n\n/**\n * @internal\n */\nexport function prepareBatchExecute(args: {\n  accountContract: ThirdwebContract;\n  transactions: SendTransactionOption[];\n  executeBatchOverride?: (\n    accountContract: ThirdwebContract,\n    transactions: SendTransactionOption[],\n  ) => PreparedTransaction;\n}): PreparedTransaction {\n  const {\n    accountContract,\n    transactions,\n    executeBatchOverride: executeBatch,\n  } = args;\n  if (executeBatch) {\n    return executeBatch(accountContract, transactions);\n  }\n  let values = transactions.map((tx) => tx.value || 0n);\n  const chainId = transactions[0]?.chainId;\n  // special handling of hedera chains, decimals for native value is 8 instead of 18 when passed as contract params\n  if (chainId === 295 || chainId === 296) {\n    values = values.map((value) => BigInt(value) / BigInt(10 ** 10));\n  }\n  return prepareContractCall({\n    contract: accountContract,\n    method: \"function executeBatch(address[], uint256[], bytes[])\",\n    params: [\n      transactions.map((tx) => tx.to || \"\"),\n      values,\n      transactions.map((tx) => tx.data || \"0x\"),\n    ],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAiEA,eAAsB,eAAe,MASpC;AACC,QAAM,EACJ,iBACA,wBAAwBA,iBACxB,cACA,aACA,eAAc,IACZ;AACJ,MAAIA,iBAAgB;AAClB,WAAOA,gBAAe,iBAAiB,YAAY;EACrD;AACA,MAAI,gBAAgB;AAClB,WAAO;EACT;AACA,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MACR,kEAAkE;EAEtE;AACA,SAAO,UACL,YAAW;AACT,UAAM,UACJ,eAAe,MAAM,WAAW,IAC5B,cACA,YAAY,eAAe,EAAE;AACnC,QAAI;AACJ,QAAI,UAAU;AACd,UAAM,aAAa;AAEnB,WAAO,WAAW,YAAY;AAC5B,UAAI;AACF,iBAAS,MAAM,aAAa;UAC1B,UAAU;UACV,QAAQ;UACR,QAAQ,CAAC,cAAc,OAAO;SAC/B;AACD;MACF,SAAS,OAAO;AACd,YAAI,YAAY,YAAY;AAC1B,gBAAM;QACR;AAGA,cAAM,QAAQ,MAAM,UAAU,KAAK;AACnC,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,KAAK,CAAC;AACzD;MACF;IACF;AACA,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MACR,oDAAoD,YAAY,aAAa,WAAW,EAAE;IAE9F;AACA,WAAO;EACT,GACA;IACE,UAAU,GAAG,KAAK,gBAAgB,MAAM,EAAE,IAAI,KAAK,gBAAgB,OAAO,IAAI,KAAK,YAAY,IAAI,KAAK,WAAW;IACnH,WAAW,MAAO,KAAK,KAAK;;GAC7B;AAEL;AAKM,SAAU,qBAAqB,MAQpC;AACC,QAAM,EACJ,cACA,iBACA,uBAAuB,eACvB,YAAW,IACT;AACJ,MAAI,eAAe;AACjB,WAAO,cAAc,iBAAiB,YAAY;EACpD;AACA,QAAM,UACJ,eAAe,MAAM,WAAW,IAC5B,cACA,YAAY,eAAe,EAAE;AACnC,SAAO,oBAAoB;IACzB,UAAU;IACV,QAAQ;IACR,QAAQ,CAAC,cAAc,OAAO;GAC/B;AACH;AAKM,SAAU,eAAe,MAO9B;AACC,QAAM,EAAE,iBAAiB,aAAa,iBAAiB,QAAO,IAAK;AACnE,MAAI,SAAS;AACX,WAAO,QAAQ,iBAAiB,WAAW;EAC7C;AACA,MAAI,QAAQ,YAAY,SAAS;AAEjC,MAAI,YAAY,YAAY,OAAO,YAAY,YAAY,KAAK;AAC9D,YAAQ,OAAO,KAAK,IAAI,OAAO,MAAM,EAAE;EACzC;AACA,SAAO,oBAAoB;IACzB,UAAU;IACV,QAAQ;IACR,QAAQ,CAAC,YAAY,MAAM,IAAI,OAAO,YAAY,QAAQ,IAAI;;;;IAI9D,KAAK,YAAY,MAAM,YAAY,MAAM,SAAS;GACnD;AACH;AAKM,SAAU,oBAAoB,MAOnC;AA/MD;AAgNE,QAAM,EACJ,iBACA,cACA,sBAAsB,aAAY,IAChC;AACJ,MAAI,cAAc;AAChB,WAAO,aAAa,iBAAiB,YAAY;EACnD;AACA,MAAI,SAAS,aAAa,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE;AACpD,QAAM,WAAU,kBAAa,CAAC,MAAd,mBAAiB;AAEjC,MAAI,YAAY,OAAO,YAAY,KAAK;AACtC,aAAS,OAAO,IAAI,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,MAAM,EAAE,CAAC;EACjE;AACA,SAAO,oBAAoB;IACzB,UAAU;IACV,QAAQ;IACR,QAAQ;MACN,aAAa,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE;MACpC;MACA,aAAa,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI;;GAE3C;AACH;", "names": ["predictAddress"]}