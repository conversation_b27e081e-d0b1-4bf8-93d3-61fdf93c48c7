{"version": 3, "sources": ["../../thirdweb/src/extensions/marketplace/__generated__/IDirectListings/write/buyFromListing.ts", "../../thirdweb/src/extensions/marketplace/direct-listings/write/buyFromListing.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"buyFromListing\" function.\n */\nexport type BuyFromListingParams = WithOverrides<{\n  listingId: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"_listingId\";\n  }>;\n  buyFor: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"_buyFor\" }>;\n  quantity: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"_quantity\" }>;\n  currency: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"_currency\" }>;\n  expectedTotalPrice: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"_expectedTotalPrice\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x704232dc\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"_listingId\",\n  },\n  {\n    type: \"address\",\n    name: \"_buyFor\",\n  },\n  {\n    type: \"uint256\",\n    name: \"_quantity\",\n  },\n  {\n    type: \"address\",\n    name: \"_currency\",\n  },\n  {\n    type: \"uint256\",\n    name: \"_expectedTotalPrice\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `buyFromListing` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `buyFromListing` method is supported.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { isBuyFromListingSupported } from \"thirdweb/extensions/marketplace\";\n *\n * const supported = isBuyFromListingSupported([\"0x...\"]);\n * ```\n */\nexport function isBuyFromListingSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"buyFromListing\" function.\n * @param options - The options for the buyFromListing function.\n * @returns The encoded ABI parameters.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { encodeBuyFromListingParams } from \"thirdweb/extensions/marketplace\";\n * const result = encodeBuyFromListingParams({\n *  listingId: ...,\n *  buyFor: ...,\n *  quantity: ...,\n *  currency: ...,\n *  expectedTotalPrice: ...,\n * });\n * ```\n */\nexport function encodeBuyFromListingParams(options: BuyFromListingParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.listingId,\n    options.buyFor,\n    options.quantity,\n    options.currency,\n    options.expectedTotalPrice,\n  ]);\n}\n\n/**\n * Encodes the \"buyFromListing\" function into a Hex string with its parameters.\n * @param options - The options for the buyFromListing function.\n * @returns The encoded hexadecimal string.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { encodeBuyFromListing } from \"thirdweb/extensions/marketplace\";\n * const result = encodeBuyFromListing({\n *  listingId: ...,\n *  buyFor: ...,\n *  quantity: ...,\n *  currency: ...,\n *  expectedTotalPrice: ...,\n * });\n * ```\n */\nexport function encodeBuyFromListing(options: BuyFromListingParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeBuyFromListingParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"buyFromListing\" function on the contract.\n * @param options - The options for the \"buyFromListing\" function.\n * @returns A prepared transaction object.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { buyFromListing } from \"thirdweb/extensions/marketplace\";\n *\n * const transaction = buyFromListing({\n *  contract,\n *  listingId: ...,\n *  buyFor: ...,\n *  quantity: ...,\n *  currency: ...,\n *  expectedTotalPrice: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function buyFromListing(\n  options: BaseTransactionOptions<\n    | BuyFromListingParams\n    | {\n        asyncParams: () => Promise<BuyFromListingParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.listingId,\n        resolvedOptions.buyFor,\n        resolvedOptions.quantity,\n        resolvedOptions.currency,\n        resolvedOptions.expectedTotalPrice,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { Address } from \"abitype\";\nimport { isNativeTokenAddress } from \"../../../../constants/addresses.js\";\nimport type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport * as BuyFromListing from \"../../__generated__/IDirectListings/write/buyFromListing.js\";\nimport * as GetListing from \"../read/getListing.js\";\nimport { isListingValid } from \"../utils.js\";\n\n/**\n * @extension MARKETPLACE\n */\nexport type BuyFromListingParams = {\n  listingId: bigint;\n  quantity: bigint;\n  recipient: Address;\n};\n\n/**\n * Buys a listing from the marketplace.\n *\n * @param options - The options for buying from a listing.\n * @returns A promise that resolves to the transaction result.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { buyFromListing } from \"thirdweb/extensions/marketplace\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = buyFromListing({\n *  contract,\n *  listingId: 1n,\n *  quantity: 1n,\n *  recipient: \"0x...\",\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n *\n * When using `buyFromListing` with Pay, the `erc20Value` will be automatically set to the listing currency.\n */\nexport function buyFromListing(\n  options: BaseTransactionOptions<BuyFromListingParams>,\n) {\n  return BuyFromListing.buyFromListing({\n    contract: options.contract,\n    asyncParams: async () => {\n      const listing = await GetListing.getListing({\n        contract: options.contract,\n        listingId: options.listingId,\n      });\n      const listingValidity = await isListingValid({\n        contract: options.contract,\n        listing: listing,\n        quantity: options.quantity,\n      });\n\n      if (!listingValidity.valid) {\n        throw new Error(listingValidity.reason);\n      }\n\n      return {\n        listingId: options.listingId,\n        quantity: options.quantity,\n        buyFor: options.recipient,\n        currency: listing.currencyContractAddress,\n        expectedTotalPrice: listing.pricePerToken * options.quantity,\n        overrides: {\n          value: isNativeTokenAddress(listing.currencyContractAddress)\n            ? listing.pricePerToken * options.quantity\n            : 0n,\n          extraGas: 50_000n, // add extra gas to account for router call\n          erc20Value: isNativeTokenAddress(listing.currencyContractAddress)\n            ? undefined\n            : {\n                amountWei: listing.pricePerToken * options.quantity,\n                tokenAddress: listing.currencyContractAddress,\n              },\n        },\n      };\n    },\n  });\n}\n\n/**\n * Checks if the `buyFromListing` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `buyFromListing` method is supported.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { isBuyFromListingSupported } from \"thirdweb/extensions/marketplace\";\n *\n * const supported = isBuyFromListingSupported([\"0x...\"]);\n * ```\n */\nexport function isBuyFromListingSupported(availableSelectors: string[]) {\n  return (\n    BuyFromListing.isBuyFromListingSupported(availableSelectors) &&\n    GetListing.isGetListingSupported(availableSelectors)\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa,CAAA;AAcb,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAiFM,SAAU,eACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AA1KpB;AA0KwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA3KzB;AA2K6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA5KlB;AA4KsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA7KvB;AA6K2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA9K3B;AA8K+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA/KnC;AAgLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAjLpB;AAiLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAlLvB;AAkL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAnLzB;AAmL6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AApLhC;AAqLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACrJM,SAAUA,gBACd,SAAqD;AAErD,SAAsB,eAAe;IACnC,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,YAAM,UAAU,MAAiB,WAAW;QAC1C,UAAU,QAAQ;QAClB,WAAW,QAAQ;OACpB;AACD,YAAM,kBAAkB,MAAM,eAAe;QAC3C,UAAU,QAAQ;QAClB;QACA,UAAU,QAAQ;OACnB;AAED,UAAI,CAAC,gBAAgB,OAAO;AAC1B,cAAM,IAAI,MAAM,gBAAgB,MAAM;MACxC;AAEA,aAAO;QACL,WAAW,QAAQ;QACnB,UAAU,QAAQ;QAClB,QAAQ,QAAQ;QAChB,UAAU,QAAQ;QAClB,oBAAoB,QAAQ,gBAAgB,QAAQ;QACpD,WAAW;UACT,OAAO,qBAAqB,QAAQ,uBAAuB,IACvD,QAAQ,gBAAgB,QAAQ,WAChC;UACJ,UAAU;;UACV,YAAY,qBAAqB,QAAQ,uBAAuB,IAC5D,SACA;YACE,WAAW,QAAQ,gBAAgB,QAAQ;YAC3C,cAAc,QAAQ;;;;IAIlC;GACD;AACH;AAcM,SAAUC,2BAA0B,oBAA4B;AACpE,SACiB,0BAA0B,kBAAkB,KAChD,sBAAsB,kBAAkB;AAEvD;", "names": ["buyFromListing", "isBuyFromListingSupported"]}