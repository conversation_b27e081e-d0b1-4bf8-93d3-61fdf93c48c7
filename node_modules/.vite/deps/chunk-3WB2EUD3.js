import {
  eth_sendRawTransaction
} from "./chunk-CMXLKATA.js";
import {
  concatHex
} from "./chunk-LM2644TQ.js";
import {
  replaceBigInts,
  toBigInt
} from "./chunk-JMHMJ42H.js";
import {
  toSerializableTransaction
} from "./chunk-R462U44Z.js";
import {
  encode
} from "./chunk-DVJU3TKU.js";
import {
  hashBytecode,
  resolvePromisedValue
} from "./chunk-LFHG7EDC.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import {
  toRlp
} from "./chunk-YXD4WFHV.js";
import {
  hexToBytes
} from "./chunk-6CMZOK3K.js";
import {
  getAddress
} from "./chunk-FFXQ6EIY.js";
import {
  toHex
} from "./chunk-OLGC3KE4.js";

// node_modules/viem/_esm/zksync/constants/address.js
var addressModulo = 2n ** 160n;

// node_modules/thirdweb/dist/esm/transaction/actions/zksync/getEip721Domain.js
var gasPerPubdataDefault = 50000n;
var getEip712Domain2 = (transaction) => {
  const message = transactionToMessage(transaction);
  return {
    domain: {
      name: "zkSync",
      version: "2",
      chainId: transaction.chainId
    },
    types: {
      Transaction: [
        { name: "txType", type: "uint256" },
        { name: "from", type: "uint256" },
        { name: "to", type: "uint256" },
        { name: "gasLimit", type: "uint256" },
        { name: "gasPerPubdataByteLimit", type: "uint256" },
        { name: "maxFeePerGas", type: "uint256" },
        { name: "maxPriorityFeePerGas", type: "uint256" },
        { name: "paymaster", type: "uint256" },
        { name: "nonce", type: "uint256" },
        { name: "value", type: "uint256" },
        { name: "data", type: "bytes" },
        { name: "factoryDeps", type: "bytes32[]" },
        { name: "paymasterInput", type: "bytes" }
      ]
    },
    primaryType: "Transaction",
    message
  };
};
function transactionToMessage(transaction) {
  const { gas, nonce, to, from, value, maxFeePerGas, maxPriorityFeePerGas, paymaster, paymasterInput, gasPerPubdata, data, factoryDeps } = transaction;
  return {
    txType: 113n,
    from: BigInt(from),
    to: to ? BigInt(to) : 0n,
    gasLimit: gas ?? 0n,
    gasPerPubdataByteLimit: gasPerPubdata ?? gasPerPubdataDefault,
    maxFeePerGas: maxFeePerGas ?? 0n,
    maxPriorityFeePerGas: maxPriorityFeePerGas ?? 0n,
    paymaster: paymaster ? BigInt(paymaster) : 0n,
    nonce: nonce ? BigInt(nonce) : 0n,
    value: value ?? 0n,
    data: data ? data : "0x0",
    factoryDeps: (factoryDeps == null ? void 0 : factoryDeps.map((dep) => toHex(hashBytecode(dep)))) ?? [],
    paymasterInput: paymasterInput ? paymasterInput : "0x"
  };
}

// node_modules/thirdweb/dist/esm/transaction/actions/zksync/send-eip712-transaction.js
async function sendEip712Transaction2(options) {
  const { account, transaction } = options;
  const eip712Transaction = await populateEip712Transaction(options);
  const hash = await signEip712Transaction2({
    account,
    eip712Transaction,
    chainId: transaction.chain.id
  });
  const rpc = getRpcClient(transaction);
  const result = await eth_sendRawTransaction(rpc, hash);
  return {
    transactionHash: result,
    chain: transaction.chain,
    client: transaction.client
  };
}
async function signEip712Transaction2(options) {
  const { account, eip712Transaction, chainId } = options;
  const eip712Domain = getEip712Domain2(eip712Transaction);
  const customSignature = await account.signTypedData({
    // biome-ignore lint/suspicious/noExplicitAny: TODO type properly
    ...eip712Domain
  });
  return serializeTransactionEIP712({
    ...eip712Transaction,
    chainId,
    customSignature
  });
}
async function populateEip712Transaction(options) {
  const { account, transaction } = options;
  const { gas, maxFeePerGas, maxPriorityFeePerGas, gasPerPubdata } = await getZkGasFees({ transaction, from: getAddress(account.address) });
  const serializableTransaction = await toSerializableTransaction({
    transaction: {
      ...transaction,
      gas,
      maxFeePerGas,
      maxPriorityFeePerGas
    },
    from: account.address
  });
  return {
    ...serializableTransaction,
    ...transaction.eip712,
    gasPerPubdata,
    from: account.address
  };
}
function serializeTransactionEIP712(transaction) {
  const { chainId, gas, nonce, to, from, value, maxFeePerGas, maxPriorityFeePerGas, customSignature, factoryDeps, paymaster, paymasterInput, gasPerPubdata, data } = transaction;
  const serializedTransaction = [
    nonce ? toHex(nonce) : "0x",
    maxPriorityFeePerGas ? toHex(maxPriorityFeePerGas) : "0x",
    maxFeePerGas ? toHex(maxFeePerGas) : "0x",
    gas ? toHex(gas) : "0x",
    to ?? "0x",
    value ? toHex(value) : "0x",
    data ?? "0x0",
    toHex(chainId),
    toHex(""),
    toHex(""),
    toHex(chainId),
    from ?? "0x",
    gasPerPubdata ? toHex(gasPerPubdata) : toHex(gasPerPubdataDefault),
    factoryDeps ?? [],
    customSignature ?? "0x",
    // EIP712 signature
    paymaster && paymasterInput ? [paymaster, paymasterInput] : []
  ];
  return concatHex(["0x71", toRlp(serializedTransaction)]);
}
async function getZkGasFees(args) {
  const { transaction, from } = args;
  let [gas, maxFeePerGas, maxPriorityFeePerGas, eip712] = await Promise.all([
    resolvePromisedValue(transaction.gas),
    resolvePromisedValue(transaction.maxFeePerGas),
    resolvePromisedValue(transaction.maxPriorityFeePerGas),
    resolvePromisedValue(transaction.eip712)
  ]);
  let gasPerPubdata = eip712 == null ? void 0 : eip712.gasPerPubdata;
  if (gas === void 0 || maxFeePerGas === void 0 || maxPriorityFeePerGas === void 0) {
    const rpc = getRpcClient(transaction);
    const params = await formatTransaction({ transaction, from });
    const result = await rpc({
      // biome-ignore lint/suspicious/noExplicitAny: TODO add to RPC method types
      method: "zks_estimateFee",
      // biome-ignore lint/suspicious/noExplicitAny: TODO add to RPC method types
      params: [replaceBigInts(params, toHex)]
    });
    gas = toBigInt(result.gas_limit) * 2n;
    const baseFee = toBigInt(result.max_fee_per_gas);
    maxFeePerGas = baseFee * 2n;
    maxPriorityFeePerGas = toBigInt(result.max_priority_fee_per_gas) || 1n;
    gasPerPubdata = toBigInt(result.gas_per_pubdata_limit) * 2n;
    if (gasPerPubdata < 50000n) {
      gasPerPubdata = 50000n;
    }
  }
  return {
    gas,
    maxFeePerGas,
    maxPriorityFeePerGas,
    gasPerPubdata
  };
}
async function formatTransaction(args) {
  var _a;
  const { transaction, from } = args;
  const [data, to, value, eip712] = await Promise.all([
    encode(transaction),
    resolvePromisedValue(transaction.to),
    resolvePromisedValue(transaction.value),
    resolvePromisedValue(transaction.eip712)
  ]);
  const gasPerPubdata = eip712 == null ? void 0 : eip712.gasPerPubdata;
  return {
    from,
    to,
    data,
    value,
    gasPerPubdata,
    eip712Meta: {
      ...eip712,
      gasPerPubdata: gasPerPubdata || 50000n,
      factoryDeps: (_a = eip712 == null ? void 0 : eip712.factoryDeps) == null ? void 0 : _a.map((dep) => Array.from(hexToBytes(dep)))
    },
    type: "0x71"
  };
}

export {
  sendEip712Transaction2 as sendEip712Transaction,
  signEip712Transaction2 as signEip712Transaction,
  populateEip712Transaction,
  getZkGasFees
};
//# sourceMappingURL=chunk-3WB2EUD3.js.map
