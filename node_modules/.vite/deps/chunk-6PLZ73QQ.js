import {
  eth_getBalance
} from "./chunk-RKW6PCRI.js";
import {
  NATIVE_TOKEN_ADDRESS
} from "./chunk-YCZ3YGMG.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import {
  toTokens
} from "./chunk-HAADYJEF.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  getChainDecimals,
  getChainNativeCurrencyName,
  getChainSymbol
} from "./chunk-KQKMGIQ6.js";

// node_modules/thirdweb/dist/esm/wallets/utils/getWalletBalance.js
async function getWalletBalance(options) {
  const { address, client, chain, tokenAddress } = options;
  if (tokenAddress) {
    const { getBalance } = await import("./getBalance-VRKZ5MUT.js");
    return getBalance({
      contract: getContract({ client, chain, address: tokenAddress }),
      address
    });
  }
  const rpcRequest = getRpcClient({ client, chain });
  const [nativeSymbol, nativeDecimals, nativeName, nativeBalance] = await Promise.all([
    getChainSymbol(chain),
    getChainDecimals(chain),
    getChainNativeCurrencyName(chain),
    eth_getBalance(rpcRequest, { address })
  ]);
  return {
    value: nativeBalance,
    decimals: nativeDecimals,
    displayValue: toTokens(nativeBalance, nativeDecimals),
    symbol: nativeSymbol,
    name: nativeName,
    tokenAddress: tokenAddress ?? NATIVE_TOKEN_ADDRESS,
    chainId: chain.id
  };
}

export {
  getWalletBalance
};
//# sourceMappingURL=chunk-6PLZ73QQ.js.map
