import {
  getClientFetch
} from "./chunk-RJUQUX6Y.js";
import {
  getThirdwebBaseUrl
} from "./chunk-OSFP2VB7.js";

// node_modules/thirdweb/dist/esm/wallets/in-app/core/users/getUser.js
async function getUser({ client, walletAddress, email, phone, id, externalWalletAddress, ecosystem }) {
  if (!client.secretKey) {
    throw new Error("A secret key is required to query for users. If you're making this request from the server, please add a secret key to your client.");
  }
  const url = new URL(`${getThirdwebBaseUrl("inAppWallet")}/api/2023-11-30/embedded-wallet/user-details`);
  if (walletAddress) {
    url.searchParams.set("queryBy", "walletAddress");
    url.searchParams.set("walletAddress", walletAddress);
  } else if (email) {
    url.searchParams.set("queryBy", "email");
    url.searchParams.set("email", email);
  } else if (phone) {
    url.searchParams.set("queryBy", "phone");
    url.searchParams.set("phone", phone);
  } else if (id) {
    url.searchParams.set("queryBy", "id");
    url.searchParams.set("id", id);
  } else if (externalWalletAddress) {
    url.searchParams.set("queryBy", "externalWalletAddress");
    url.searchParams.set("externalWalletAddress", externalWalletAddress);
  } else {
    throw new Error("Please provide a walletAddress, email, phone, id, or externalWalletAddress to query for users.");
  }
  const clientFetch = getClientFetch(client, ecosystem);
  const res = await clientFetch(url.toString());
  if (!res.ok) {
    const error = await res.text().catch(() => "Unknown error");
    throw new Error(`Failed to get profiles. ${res.status} ${res.statusText}: ${error}`);
  }
  const data = await res.json();
  return data.map((item) => ({
    userId: item.userId,
    walletAddress: item.walletAddress,
    smartAccountAddress: item.smartAccountAddress,
    email: item.email,
    phone: item.phone,
    createdAt: item.createdAt,
    profiles: item.linkedAccounts.map((profile) => {
      return {
        type: profile.type === "siwe" ? "wallet" : profile.type,
        details: profile.details
      };
    })
  }))[0] || null;
}

export {
  getUser
};
//# sourceMappingURL=chunk-SBIK2FO7.js.map
