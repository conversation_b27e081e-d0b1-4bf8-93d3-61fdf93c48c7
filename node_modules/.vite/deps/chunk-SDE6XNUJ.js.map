{"version": 3, "sources": ["../../thirdweb/src/analytics/track/connect.ts", "../../thirdweb/src/utils/tiny-emitter.ts", "../../thirdweb/src/wallets/wallet-emitter.ts", "../../thirdweb/src/wallets/in-app/core/wallet/in-app-core.ts"], "sourcesContent": ["import type { ThirdwebClient } from \"../../client/client.js\";\nimport type { Ecosystem } from \"../../wallets/in-app/core/wallet/types.js\";\nimport { track } from \"./index.js\";\n\n/**\n * @internal\n */\nexport async function trackConnect(args: {\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n  walletType: string;\n  walletAddress: string;\n  chainId?: number;\n}) {\n  const { client, ecosystem, walletType, walletAddress, chainId } = args;\n  return track({\n    client,\n    ecosystem,\n    data: {\n      source: \"connectWallet\",\n      action: \"connect\",\n      walletType,\n      walletAddress,\n      chainId,\n    },\n  });\n}\n", "type GenericEmitterType = {\n  [key: string]: unknown;\n};\n\nexport type Emitter<T extends GenericEmitterType> = {\n  subscribe<K extends keyof T>(event: K, cb: (data: T[K]) => void): () => void;\n  emit<K extends keyof T>(event: K, data: T[K]): void;\n};\n\n/**\n * Creates an emitter object that allows subscribing to events and emitting events.\n * @returns An emitter object with `subscribe` and `emit` methods.\n * @template TEmitter - The type of the emitter.\n * @example\n * ```ts\n * const emitter = createEmitter<{\n *  event1: string;\n * event2: number;\n * }>();\n *\n * emitter.subscribe(\"event1\", (data) => {\n * console.log(data); // \"hello\"\n * });\n *\n * emitter.emit(\"event1\", \"hello\");\n * ```\n */\nexport function createEmitter<\n  const TEmitter extends GenericEmitterType,\n>(): Emitter<TEmitter> {\n  const subsribers = new Map<\n    keyof TEmitter,\n    // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n    Set<(data: any) => void>\n  >();\n\n  return {\n    subscribe(event, cb) {\n      if (!subsribers.has(event)) {\n        subsribers.set(event, new Set([cb]));\n      } else {\n        subsribers.get(event)?.add(cb);\n      }\n\n      return () => {\n        const subscribers = subsribers.get(event);\n        if (subscribers) {\n          subscribers.delete(cb);\n        }\n      };\n    },\n    emit(event, data) {\n      const subscribers = subsribers.get(event);\n      if (subscribers) {\n        for (const cb of subscribers) {\n          cb(data);\n        }\n      }\n    },\n  };\n}\n", "import type { Chain } from \"../chains/types.js\";\nimport { type Emitter, createEmitter } from \"../utils/tiny-emitter.js\";\nimport type { Account } from \"./interfaces/wallet.js\";\nimport type { WalletAutoConnectionOption, WalletId } from \"./wallet-types.js\";\n\nexport type WalletEmitterEvents<TWalletId extends WalletId> = {\n  accountChanged: Account;\n  accountsChanged: string[];\n  disconnect?: never;\n  chainChanged: Chain;\n  onConnect: WalletAutoConnectionOption<TWalletId>;\n};\n\nexport type WalletEmitter<TWalletId extends WalletId> = Emitter<\n  WalletEmitterEvents<TWalletId>\n>;\n\n/**\n\n * @internal\n */\nexport function createWalletEmitter<const TWalletId extends WalletId>() {\n  return createEmitter<WalletEmitterEvents<TWalletId>>();\n}\n", "import { trackConnect } from \"../../../../analytics/track/connect.js\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport {\n  getCached<PERSON>hain,\n  getCachedChainIfExists,\n} from \"../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport { getEcosystemInfo } from \"../../../ecosystem/get-ecosystem-wallet-auth-options.js\";\nimport type { Account, Wallet } from \"../../../interfaces/wallet.js\";\nimport { createWalletEmitter } from \"../../../wallet-emitter.js\";\nimport type {\n  CreateWalletArgs,\n  EcosystemWalletId,\n} from \"../../../wallet-types.js\";\nimport type { InAppConnector } from \"../interfaces/connector.js\";\nimport type { Ecosystem } from \"./types.js\";\n\nconst connectorCache = new Map<string, InAppConnector>();\n\n/**\n * @internal\n */\nexport async function getOrCreateInAppWalletConnector(\n  client: ThirdwebClient,\n  connectorFactory: (client: ThirdwebClient) => Promise<InAppConnector>,\n  ecosystem?: Ecosystem,\n) {\n  const key = stringify({\n    clientId: client.clientId,\n    ecosystem,\n    partialSecretKey: client.secretKey?.slice(0, 5),\n  });\n  if (connectorCache.has(key)) {\n    return connectorCache.get(key) as InAppConnector;\n  }\n  const connector = await connectorFactory(client);\n  connectorCache.set(key, connector);\n  return connector;\n}\n\n/**\n * @internal\n */\nexport function createInAppWallet(args: {\n  createOptions?: CreateWalletArgs<\"inApp\">[1];\n  connectorFactory: (client: ThirdwebClient) => Promise<InAppConnector>;\n  ecosystem?: Ecosystem;\n}): Wallet<\"inApp\" | EcosystemWalletId> {\n  const { createOptions: _createOptions, connectorFactory, ecosystem } = args;\n  const walletId = ecosystem ? ecosystem.id : \"inApp\";\n  const emitter = createWalletEmitter<\"inApp\">();\n  let createOptions = _createOptions;\n  let account: Account | undefined = undefined;\n  let adminAccount: Account | undefined = undefined; // Admin account if smartAccountOptions were provided with connection\n  let chain: Chain | undefined = undefined;\n  let client: ThirdwebClient | undefined;\n\n  return {\n    id: walletId,\n    subscribe: emitter.subscribe,\n    getChain() {\n      if (!chain) {\n        return undefined;\n      }\n\n      chain = getCachedChainIfExists(chain.id) || chain;\n      return chain;\n    },\n    getConfig: () => createOptions,\n    getAccount: () => account,\n    autoConnect: async (options) => {\n      const { autoConnectInAppWallet } = await import(\"./index.js\");\n\n      const connector = await getOrCreateInAppWalletConnector(\n        options.client,\n        connectorFactory,\n        ecosystem,\n      );\n\n      if (ecosystem) {\n        const ecosystemOptions = await getEcosystemInfo(ecosystem.id);\n        const smartAccountOptions = ecosystemOptions?.smartAccountOptions;\n        if (smartAccountOptions) {\n          const { defaultChainId } = ecosystemOptions.smartAccountOptions;\n          const preferredChain =\n            options.chain ??\n            (defaultChainId ? getCachedChain(defaultChainId) : undefined);\n          if (!preferredChain) {\n            throw new Error(\n              `A chain must be provided either via 'chain' in connect options or 'defaultChainId' in ecosystem configuration. Please pass it via connect() or update the ecosystem configuration.`,\n            );\n          }\n\n          createOptions = {\n            ...createOptions,\n            smartAccount: {\n              chain: preferredChain,\n              sponsorGas: smartAccountOptions.sponsorGas,\n              factoryAddress: smartAccountOptions.accountFactoryAddress,\n            },\n          };\n        }\n      }\n\n      const {\n        account: connectedAccount,\n        chain: connectedChain,\n        adminAccount: _adminAccount,\n      } = await autoConnectInAppWallet(options, createOptions, connector);\n\n      // set the states\n      client = options.client;\n      account = connectedAccount;\n      adminAccount = _adminAccount;\n      chain = connectedChain;\n      trackConnect({\n        client: options.client,\n        ecosystem,\n        walletType: walletId,\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return only the account\n      return account;\n    },\n    connect: async (options) => {\n      const { connectInAppWallet } = await import(\"./index.js\");\n      const connector = await getOrCreateInAppWalletConnector(\n        options.client,\n        connectorFactory,\n        ecosystem,\n      );\n\n      if (ecosystem) {\n        const ecosystemOptions = await getEcosystemInfo(ecosystem.id);\n        const smartAccountOptions = ecosystemOptions?.smartAccountOptions;\n        if (smartAccountOptions) {\n          const { defaultChainId } = ecosystemOptions.smartAccountOptions;\n          const preferredChain =\n            options.chain ??\n            (defaultChainId ? getCachedChain(defaultChainId) : undefined);\n          if (!preferredChain) {\n            throw new Error(\n              `A chain must be provided either via 'chain' in connect options or 'defaultChainId' in ecosystem configuration. Please pass it via connect() or update the ecosystem configuration.`,\n            );\n          }\n\n          createOptions = {\n            ...createOptions,\n            smartAccount: {\n              chain: preferredChain,\n              sponsorGas: smartAccountOptions.sponsorGas,\n              factoryAddress: smartAccountOptions.accountFactoryAddress,\n            },\n          };\n        }\n      }\n\n      const {\n        account: connectedAccount,\n        chain: connectedChain,\n        adminAccount: _adminAccount,\n      } = await connectInAppWallet(options, createOptions, connector);\n\n      // set the states\n      client = options.client;\n      account = connectedAccount;\n      adminAccount = _adminAccount;\n      chain = connectedChain;\n      trackConnect({\n        client: options.client,\n        ecosystem,\n        walletType: walletId,\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return only the account\n      return account;\n    },\n    disconnect: async () => {\n      // If no client is assigned, we should be fine just unsetting the states\n      if (client) {\n        const connector = await getOrCreateInAppWalletConnector(\n          client,\n          connectorFactory,\n          ecosystem,\n        );\n        const result = await connector.logout();\n        if (!result.success) {\n          throw new Error(\"Failed to logout\");\n        }\n      }\n      account = undefined;\n      adminAccount = undefined;\n      chain = undefined;\n      emitter.emit(\"disconnect\", undefined);\n    },\n    switchChain: async (newChain) => {\n      if (createOptions?.smartAccount && client && account) {\n        // if account abstraction is enabled, reconnect to smart account on the new chain\n        const { autoConnectInAppWallet } = await import(\"./index.js\");\n        const connector = await getOrCreateInAppWalletConnector(\n          client,\n          connectorFactory,\n          ecosystem,\n        );\n\n        if (ecosystem) {\n          const ecosystemOptions = await getEcosystemInfo(ecosystem.id);\n          const smartAccountOptions = ecosystemOptions?.smartAccountOptions;\n          if (smartAccountOptions) {\n            createOptions = {\n              ...createOptions,\n              smartAccount: {\n                chain: newChain,\n                sponsorGas: smartAccountOptions.sponsorGas,\n                factoryAddress: smartAccountOptions.accountFactoryAddress,\n              },\n            };\n          }\n        }\n\n        const {\n          account: connectedAccount,\n          chain: connectedChain,\n          adminAccount: _adminAccount,\n        } = await autoConnectInAppWallet(\n          {\n            chain: newChain,\n            client,\n          },\n          createOptions,\n          connector,\n        );\n        adminAccount = _adminAccount;\n        account = connectedAccount;\n        chain = connectedChain;\n      } else {\n        // if not, simply set the new chain\n        chain = newChain;\n      }\n      emitter.emit(\"chainChanged\", newChain);\n    },\n    getAdminAccount: () => adminAccount,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAOA,eAAsB,aAAa,MAMlC;AACC,QAAM,EAAE,QAAQ,WAAW,YAAY,eAAe,QAAO,IAAK;AAClE,SAAO,MAAM;IACX;IACA;IACA,MAAM;MACJ,QAAQ;MACR,QAAQ;MACR;MACA;MACA;;GAEH;AACH;;;ACCM,SAAU,gBAAa;AAG3B,QAAM,aAAa,oBAAI,IAAG;AAM1B,SAAO;IACL,UAAU,OAAO,IAAE;AA5BvB;AA6BM,UAAI,CAAC,WAAW,IAAI,KAAK,GAAG;AAC1B,mBAAW,IAAI,OAAO,oBAAI,IAAI,CAAC,EAAE,CAAC,CAAC;MACrC,OAAO;AACL,yBAAW,IAAI,KAAK,MAApB,mBAAuB,IAAI;MAC7B;AAEA,aAAO,MAAK;AACV,cAAM,cAAc,WAAW,IAAI,KAAK;AACxC,YAAI,aAAa;AACf,sBAAY,OAAO,EAAE;QACvB;MACF;IACF;IACA,KAAK,OAAO,MAAI;AACd,YAAM,cAAc,WAAW,IAAI,KAAK;AACxC,UAAI,aAAa;AACf,mBAAW,MAAM,aAAa;AAC5B,aAAG,IAAI;QACT;MACF;IACF;;AAEJ;;;ACvCM,SAAU,sBAAmB;AACjC,SAAO,cAAa;AACtB;;;ACLA,IAAM,iBAAiB,oBAAI,IAAG;AAK9B,eAAsB,gCACpB,QACA,kBACA,WAAqB;AA1BvB;AA4BE,QAAM,MAAM,UAAU;IACpB,UAAU,OAAO;IACjB;IACA,mBAAkB,YAAO,cAAP,mBAAkB,MAAM,GAAG;GAC9C;AACD,MAAI,eAAe,IAAI,GAAG,GAAG;AAC3B,WAAO,eAAe,IAAI,GAAG;EAC/B;AACA,QAAM,YAAY,MAAM,iBAAiB,MAAM;AAC/C,iBAAe,IAAI,KAAK,SAAS;AACjC,SAAO;AACT;AAKM,SAAU,kBAAkB,MAIjC;AACC,QAAM,EAAE,eAAe,gBAAgB,kBAAkB,UAAS,IAAK;AACvE,QAAM,WAAW,YAAY,UAAU,KAAK;AAC5C,QAAM,UAAU,oBAAmB;AACnC,MAAI,gBAAgB;AACpB,MAAI,UAA+B;AACnC,MAAI,eAAoC;AACxC,MAAI,QAA2B;AAC/B,MAAI;AAEJ,SAAO;IACL,IAAI;IACJ,WAAW,QAAQ;IACnB,WAAQ;AACN,UAAI,CAAC,OAAO;AACV,eAAO;MACT;AAEA,cAAQ,uBAAuB,MAAM,EAAE,KAAK;AAC5C,aAAO;IACT;IACA,WAAW,MAAM;IACjB,YAAY,MAAM;IAClB,aAAa,OAAO,YAAW;AAC7B,YAAM,EAAE,uBAAsB,IAAK,MAAM,OAAO,sBAAY;AAE5D,YAAM,YAAY,MAAM,gCACtB,QAAQ,QACR,kBACA,SAAS;AAGX,UAAI,WAAW;AACb,cAAM,mBAAmB,MAAM,iBAAiB,UAAU,EAAE;AAC5D,cAAM,sBAAsB,qDAAkB;AAC9C,YAAI,qBAAqB;AACvB,gBAAM,EAAE,eAAc,IAAK,iBAAiB;AAC5C,gBAAM,iBACJ,QAAQ,UACP,iBAAiB,eAAe,cAAc,IAAI;AACrD,cAAI,CAAC,gBAAgB;AACnB,kBAAM,IAAI,MACR,oLAAoL;UAExL;AAEA,0BAAgB;YACd,GAAG;YACH,cAAc;cACZ,OAAO;cACP,YAAY,oBAAoB;cAChC,gBAAgB,oBAAoB;;;QAG1C;MACF;AAEA,YAAM,EACJ,SAAS,kBACT,OAAO,gBACP,cAAc,cAAa,IACzB,MAAM,uBAAuB,SAAS,eAAe,SAAS;AAGlE,eAAS,QAAQ;AACjB,gBAAU;AACV,qBAAe;AACf,cAAQ;AACR,mBAAa;QACX,QAAQ,QAAQ;QAChB;QACA,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,aAAO;IACT;IACA,SAAS,OAAO,YAAW;AACzB,YAAM,EAAE,mBAAkB,IAAK,MAAM,OAAO,sBAAY;AACxD,YAAM,YAAY,MAAM,gCACtB,QAAQ,QACR,kBACA,SAAS;AAGX,UAAI,WAAW;AACb,cAAM,mBAAmB,MAAM,iBAAiB,UAAU,EAAE;AAC5D,cAAM,sBAAsB,qDAAkB;AAC9C,YAAI,qBAAqB;AACvB,gBAAM,EAAE,eAAc,IAAK,iBAAiB;AAC5C,gBAAM,iBACJ,QAAQ,UACP,iBAAiB,eAAe,cAAc,IAAI;AACrD,cAAI,CAAC,gBAAgB;AACnB,kBAAM,IAAI,MACR,oLAAoL;UAExL;AAEA,0BAAgB;YACd,GAAG;YACH,cAAc;cACZ,OAAO;cACP,YAAY,oBAAoB;cAChC,gBAAgB,oBAAoB;;;QAG1C;MACF;AAEA,YAAM,EACJ,SAAS,kBACT,OAAO,gBACP,cAAc,cAAa,IACzB,MAAM,mBAAmB,SAAS,eAAe,SAAS;AAG9D,eAAS,QAAQ;AACjB,gBAAU;AACV,qBAAe;AACf,cAAQ;AACR,mBAAa;QACX,QAAQ,QAAQ;QAChB;QACA,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,aAAO;IACT;IACA,YAAY,YAAW;AAErB,UAAI,QAAQ;AACV,cAAM,YAAY,MAAM,gCACtB,QACA,kBACA,SAAS;AAEX,cAAM,SAAS,MAAM,UAAU,OAAM;AACrC,YAAI,CAAC,OAAO,SAAS;AACnB,gBAAM,IAAI,MAAM,kBAAkB;QACpC;MACF;AACA,gBAAU;AACV,qBAAe;AACf,cAAQ;AACR,cAAQ,KAAK,cAAc,MAAS;IACtC;IACA,aAAa,OAAO,aAAY;AAC9B,WAAI,+CAAe,iBAAgB,UAAU,SAAS;AAEpD,cAAM,EAAE,uBAAsB,IAAK,MAAM,OAAO,sBAAY;AAC5D,cAAM,YAAY,MAAM,gCACtB,QACA,kBACA,SAAS;AAGX,YAAI,WAAW;AACb,gBAAM,mBAAmB,MAAM,iBAAiB,UAAU,EAAE;AAC5D,gBAAM,sBAAsB,qDAAkB;AAC9C,cAAI,qBAAqB;AACvB,4BAAgB;cACd,GAAG;cACH,cAAc;gBACZ,OAAO;gBACP,YAAY,oBAAoB;gBAChC,gBAAgB,oBAAoB;;;UAG1C;QACF;AAEA,cAAM,EACJ,SAAS,kBACT,OAAO,gBACP,cAAc,cAAa,IACzB,MAAM,uBACR;UACE,OAAO;UACP;WAEF,eACA,SAAS;AAEX,uBAAe;AACf,kBAAU;AACV,gBAAQ;MACV,OAAO;AAEL,gBAAQ;MACV;AACA,cAAQ,KAAK,gBAAgB,QAAQ;IACvC;IACA,iBAAiB,MAAM;;AAE3B;", "names": []}