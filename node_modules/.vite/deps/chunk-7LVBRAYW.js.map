{"version": 3, "sources": ["../../thirdweb/src/transaction/actions/send-and-confirm-transaction.ts", "../../thirdweb/src/transaction/actions/send-batch-transaction.ts"], "sourcesContent": ["import type { TransactionReceipt } from \"../types.js\";\nimport {\n  type SendTransactionOptions,\n  sendTransaction,\n} from \"./send-transaction.js\";\nimport { waitForReceipt } from \"./wait-for-tx-receipt.js\";\n\n/**\n * Sends a transaction using the provided wallet.\n * @param options - The options for sending the transaction.\n * @returns A promise that resolves to the confirmed transaction receipt.\n * @throws An error if the wallet is not connected.\n * @transaction\n * @example\n *\n * ### Basic usage\n * ```ts\n * import { sendAndConfirmTransaction } from \"thirdweb\";\n *\n * const transactionReceipt = await sendAndConfirmTransaction({\n *  account,\n *  transaction\n * });\n * ```\n *\n * ### Gasless usage with [thirdweb Engine](https://portal.thirdweb.com/engine)\n * ```ts\n * const transactionReceipt = await sendAndConfirmTransaction({\n *  account,\n *  transaction,\n *  gasless: {\n *    provider: \"engine\",\n *    relayerUrl: \"https://thirdweb.engine-***.thirdweb.com/relayer/***\",\n *    relayerForwarderAddress: \"0x...\",\n *  }\n * });\n * ```\n *\n * ### Gasless usage with OpenZeppelin\n * ```ts\n * const transactionReceipt = await sendAndConfirmTransaction({\n *  account,\n *  transaction,\n *  gasless: {\n *    provider: \"openzeppelin\",\n *    relayerUrl: \"https://...\",\n *    relayerForwarderAddress: \"0x...\",\n *  }\n * });\n * ```\n */\nexport async function sendAndConfirmTransaction(\n  options: SendTransactionOptions,\n): Promise<TransactionReceipt> {\n  const submittedTx = await sendTransaction(options);\n  return waitForReceipt(submittedTx);\n}\n", "import { resolvePromisedValue } from \"../../utils/promise/resolve-promised-value.js\";\nimport type {\n  Account,\n  SendTransactionOption,\n} from \"../../wallets/interfaces/wallet.js\";\nimport type { PreparedTransaction } from \"../prepare-transaction.js\";\nimport { encode } from \"./encode.js\";\nimport type { WaitForReceiptOptions } from \"./wait-for-tx-receipt.js\";\n\nexport type SendBatchTransactionOptions = {\n  transactions: PreparedTransaction[];\n  account: Account;\n};\n\n/**\n * Sends a batch transaction using the provided options.\n * @param options - The options for sending the batch transaction.\n * @returns A promise that resolves to the options for waiting for the receipt of the first transaction in the batch.\n * @throws An error if the account is not connected, there are no transactions to send, or the account does not implement sendBatchTransaction.\n * @transaction\n * @example\n * ```ts\n * import { sendBatchTransaction } from \"thirdweb\";\n *\n * const waitForReceiptOptions = await sendBatchTransaction({\n *  account,\n *  transactions\n * });\n * ```\n */\nexport async function sendBatchTransaction(\n  options: SendBatchTransactionOptions,\n): Promise<WaitForReceiptOptions> {\n  const { account, transactions } = options;\n  if (!account) {\n    throw new Error(\"not connected\");\n  }\n  if (transactions.length === 0) {\n    throw new Error(\"No transactions to send\");\n  }\n  const firstTx = transactions[0];\n  if (!firstTx) {\n    throw new Error(\"No transactions to send\");\n  }\n  if (account.sendBatchTransaction) {\n    const serializedTxs: SendTransactionOption[] = await Promise.all(\n      transactions.map(async (tx) => {\n        // no need to estimate gas for these, gas will be estimated on the entire batch\n        const [data, to, accessList, value] = await Promise.all([\n          encode(tx),\n          resolvePromisedValue(tx.to),\n          resolvePromisedValue(tx.accessList),\n          resolvePromisedValue(tx.value),\n        ]);\n        const serializedTx: SendTransactionOption = {\n          data,\n          chainId: tx.chain.id,\n          to,\n          value,\n          accessList,\n        };\n        return serializedTx;\n      }),\n    );\n    const result = await account.sendBatchTransaction(serializedTxs);\n    return {\n      ...result,\n      chain: firstTx.chain,\n      client: firstTx.client,\n    };\n  }\n  throw new Error(\"Account doesn't implement sendBatchTransaction\");\n}\n"], "mappings": ";;;;;;;;;;;;;;AAmDA,eAAsB,0BACpB,SAA+B;AAE/B,QAAM,cAAc,MAAM,gBAAgB,OAAO;AACjD,SAAO,eAAe,WAAW;AACnC;;;AC1BA,eAAsB,qBACpB,SAAoC;AAEpC,QAAM,EAAE,SAAS,aAAY,IAAK;AAClC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,eAAe;EACjC;AACA,MAAI,aAAa,WAAW,GAAG;AAC7B,UAAM,IAAI,MAAM,yBAAyB;EAC3C;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,yBAAyB;EAC3C;AACA,MAAI,QAAQ,sBAAsB;AAChC,UAAM,gBAAyC,MAAM,QAAQ,IAC3D,aAAa,IAAI,OAAO,OAAM;AAE5B,YAAM,CAAC,MAAM,IAAI,YAAY,KAAK,IAAI,MAAM,QAAQ,IAAI;QACtD,OAAO,EAAE;QACT,qBAAqB,GAAG,EAAE;QAC1B,qBAAqB,GAAG,UAAU;QAClC,qBAAqB,GAAG,KAAK;OAC9B;AACD,YAAM,eAAsC;QAC1C;QACA,SAAS,GAAG,MAAM;QAClB;QACA;QACA;;AAEF,aAAO;IACT,CAAC,CAAC;AAEJ,UAAM,SAAS,MAAM,QAAQ,qBAAqB,aAAa;AAC/D,WAAO;MACL,GAAG;MACH,OAAO,QAAQ;MACf,QAAQ,QAAQ;;EAEpB;AACA,QAAM,IAAI,MAAM,gDAAgD;AAClE;", "names": []}