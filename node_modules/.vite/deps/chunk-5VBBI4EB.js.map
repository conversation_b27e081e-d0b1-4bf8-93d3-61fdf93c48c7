{"version": 3, "sources": ["../../thirdweb/src/transaction/resolve-method.ts", "../../thirdweb/src/transaction/actions/eip7702/authorization.ts"], "sourcesContent": ["import type { <PERSON><PERSON>, AbiFunction } from \"abitype\";\nimport { parseAbiItem } from \"abitype\";\nimport { resolveContractAbi } from \"../contract/actions/resolve-abi.js\";\nimport type { ThirdwebContract } from \"../contract/contract.js\";\n\n/**\n * Resolves and returns the ABI function with the specified method name.\n * Throws an error if the function is not found in the ABI.\n * @template abiFn - The type of the ABI function.\n * @param method - The name of the method to resolve.\n * @returns The resolved ABI function.\n * @throws Error if the function is not found in the ABI.\n * @example\n * ```ts\n * import { resolveMethod, prepareContractCall } from \"thirdweb\";\n * const tx = prepareContractCall({\n *  contract,\n *  // automatically resolves the necessary abi to encode the transaction\n *  method: resolveMethod(\"transfer\"),\n *  // however there is no type completion for params in this case (as the resolution is async and happens at runtime)\n *  params: [to, value],\n * });\n * ```\n * @contract\n */\nexport function resolveMethod<\n  abiFn extends AbiFunction,\n  T<PERSON><PERSON> extends Abi = Abi,\n>(method: string) {\n  return async (contract: ThirdwebContract<TAbi>) => {\n    if (typeof method === \"string\" && method.startsWith(\"function \")) {\n      // we know it will be an abi function so we can cast it\n      return parseAbiItem(method) as AbiFunction;\n    }\n\n    const resolvedAbi = contract.abi?.length\n      ? contract.abi\n      : await resolveContractAbi<Abi>(contract);\n    // we try to find the abiFunction in the abi\n    const abiFunction = resolvedAbi.find((item) => {\n      // if the item is not a function we can ignore it\n      if (item.type !== \"function\") {\n        return false;\n      }\n      // if the item is a function we can compare the name\n      return item.name === method;\n    }) as abiFn | undefined;\n\n    if (!abiFunction) {\n      throw new Error(`could not find function with name \"${method}\" in abi`);\n    }\n    return abiFunction;\n  };\n}\n", "import type * as ox__Authorization from \"ox/Authorization\";\nimport type { Address } from \"../../../utils/address.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\n\n/**\n * An EIP-7702 authorization object fully prepared and ready for signing.\n *\n * @beta\n * @transaction\n */\nexport type AuthorizationRequest = {\n  address: Address;\n  chainId: number;\n  nonce: bigint;\n};\n\n/**\n * Represents a signed EIP-7702 authorization object.\n *\n * @beta\n * @transaction\n */\nexport type SignedAuthorization = ox__Authorization.ListSigned[number];\n\n/**\n * Sign the given EIP-7702 authorization object.\n * @param options - The options for `signAuthorization`\n * Refer to the type [`SignAuthorizationOptions`](https://portal.thirdweb.com/references/typescript/v5/SignAuthorizationOptions)\n * @returns The signed authorization object\n *\n * ```ts\n * import { signAuthorization } from \"thirdweb\";\n *\n * const authorization = await signAuthorization({\n *     request: {\n *         address: \"0x...\",\n *         chainId: 911867,\n *         nonce: 100n,\n *     },\n *     account: myAccount,\n * });\n * ```\n *\n * @beta\n * @transaction\n */\nexport async function signAuthorization(options: {\n  account: Account;\n  request: AuthorizationRequest;\n}): Promise<SignedAuthorization> {\n  const { account, request } = options;\n  if (typeof account.signAuthorization === \"undefined\") {\n    throw new Error(\n      \"This account type does not yet support signing EIP-7702 authorizations\",\n    );\n  }\n  return account.signAuthorization(request);\n}\n"], "mappings": ";;;;;;;;AAyBM,SAAU,cAGd,QAAc;AACd,SAAO,OAAO,aAAoC;AA5BpD;AA6BI,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW,WAAW,GAAG;AAEhE,aAAO,aAAa,MAAM;IAC5B;AAEA,UAAM,gBAAc,cAAS,QAAT,mBAAc,UAC9B,SAAS,MACT,MAAM,mBAAwB,QAAQ;AAE1C,UAAM,cAAc,YAAY,KAAK,CAAC,SAAQ;AAE5C,UAAI,KAAK,SAAS,YAAY;AAC5B,eAAO;MACT;AAEA,aAAO,KAAK,SAAS;IACvB,CAAC;AAED,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,sCAAsC,MAAM,UAAU;IACxE;AACA,WAAO;EACT;AACF;;;ACPA,eAAsB,kBAAkB,SAGvC;AACC,QAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,MAAI,OAAO,QAAQ,sBAAsB,aAAa;AACpD,UAAM,IAAI,MACR,wEAAwE;EAE5E;AACA,SAAO,QAAQ,kBAAkB,OAAO;AAC1C;", "names": []}