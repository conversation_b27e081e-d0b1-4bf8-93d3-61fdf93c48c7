{"version": 3, "sources": ["../../thirdweb/src/ai/index.ts", "../../thirdweb/src/ai/common.ts", "../../thirdweb/src/ai/chat.ts", "../../thirdweb/src/ai/execute.ts"], "sourcesContent": ["export { chat } from \"./chat.js\";\nexport { execute } from \"./execute.js\";\nexport type { Input, Output } from \"./common.js\";\n", "import type { Chain } from \"../chains/types.js\";\nimport { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"../chains/utils.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport {\n  type PreparedTransaction,\n  prepareTransaction,\n} from \"../transaction/prepare-transaction.js\";\nimport type { Address } from \"../utils/address.js\";\nimport { toBigInt } from \"../utils/bigint.js\";\nimport type { Hex } from \"../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../utils/fetch.js\";\nimport type { Account } from \"../wallets/interfaces/wallet.js\";\n\nconst NEBULA_API_URL = \"https://nebula-api.thirdweb.com\";\n\nexport type Input = {\n  client: ThirdwebClient;\n  account?: Account;\n  contextFilter?: {\n    chains?: Chain[];\n    walletAddresses?: string[];\n    contractAddresses?: string[];\n  };\n  sessionId?: string;\n} & (\n  | {\n      messages: {\n        role: \"user\" | \"assistant\";\n        content: string;\n      }[];\n    }\n  | {\n      message: string;\n    }\n);\n\nexport type Output = {\n  message: string;\n  sessionId: string;\n  transactions: PreparedTransaction[];\n};\n\ntype ApiResponse = {\n  message: string;\n  session_id: string;\n  actions?: {\n    type: \"init\" | \"presence\" | \"sign_transaction\";\n    source: string;\n    data: string;\n  }[];\n};\n\nexport async function nebulaFetch(\n  mode: \"execute\" | \"chat\",\n  input: Input,\n): Promise<Output> {\n  const fetch = getClientFetch(input.client);\n  const response = await fetch(`${NEBULA_API_URL}/${mode}`, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify({\n      ...(\"messages\" in input\n        ? {\n            messages: input.messages,\n          }\n        : {\n            message: input.message,\n          }),\n      session_id: input.sessionId,\n      ...(input.account\n        ? {\n            execute_config: {\n              mode: \"client\",\n              signer_wallet_address: input.account.address,\n            },\n          }\n        : {}),\n      ...(input.contextFilter\n        ? {\n            context_filter: {\n              chain_ids:\n                input.contextFilter.chains?.map((c) => c.id.toString()) || [],\n              wallet_addresses:\n                input.contextFilter.walletAddresses ||\n                (input.account ? [input.account.address] : []),\n              contract_addresses: input.contextFilter.contractAddresses || [],\n            },\n          }\n        : {}),\n    }),\n  });\n  if (!response.ok) {\n    const error = await response.text();\n    throw new Error(`Nebula API error: ${error}`);\n  }\n  const data = (await response.json()) as ApiResponse;\n\n  // parse transactions if present\n  let transactions: PreparedTransaction[] = [];\n  if (data.actions) {\n    transactions = data.actions\n      .map((action) => {\n        // only parse sign_transaction actions\n        if (action.type === \"sign_transaction\") {\n          const tx = JSON.parse(action.data) as {\n            chainId: number;\n            to: Address | undefined;\n            value: Hex;\n            data: Hex;\n          };\n          return prepareTransaction({\n            chain: getCachedChain(tx.chainId),\n            client: input.client,\n            to: tx.to,\n            value: tx.value ? toBigInt(tx.value) : undefined,\n            data: tx.data,\n          });\n        }\n        return undefined;\n      })\n      .filter((tx) => tx !== undefined);\n  }\n\n  return {\n    message: data.message,\n    sessionId: data.session_id,\n    transactions,\n  };\n}\n", "import { type Input, type Output, nebulaFetch } from \"./common.js\";\n\n/**\n * Chat with Nebula.\n *\n * @param input - The input for the chat.\n * @returns The chat response.\n * @beta This API is in early access and might change in the future.\n * @nebula\n *\n * @example\n * ```ts\n * import { Nebula } from \"thirdweb/ai\";\n *\n * const response = await Nebula.chat({\n *   client: TEST_CLIENT,\n *   message: \"What's the symbol of this contract: ******************************************\",\n *   contextFilter: {\n *     chains: [sepolia],\n *   },\n * });\n * ```\n *\n * Multi message prompt:\n *\n * ```ts\n * const response = await Nebula.chat({\n *   client,\n *   messages: [\n *     { role: \"user\", content: \"What's my balance?\" },\n *     { role: \"assistant\", content: \"Your balance is 0.023 ETH\" },\n *     { role: \"user\", content: \"What about my NFTs?\" },\n *   ],\n *   contextFilter: {\n *     chains: [sepolia],\n *   },\n * });\n * ```\n *\n * Extracting and sending transactions from a chat response:\n *\n * ```ts\n * const response = await Nebula.chat({ ... });\n * const transactions = response.transactions;\n * for (const transaction of transactions) {\n *   await sendTransaction({ transaction, account });\n * }\n * ```\n */\nexport async function chat(input: Input): Promise<Output> {\n  return nebulaFetch(\"chat\", input);\n}\n", "import { sendTransaction } from \"../transaction/actions/send-transaction.js\";\nimport type { SendTransactionResult } from \"../transaction/types.js\";\nimport type { Account } from \"../wallets/interfaces/wallet.js\";\nimport { type Input, nebulaFetch } from \"./common.js\";\n\n/**\n * Execute a transaction based on a prompt.\n *\n * @param input - The input for the transaction.\n * @returns The transaction hash.\n * @beta This API is in early access and might change in the future.\n * @nebula\n *\n * @example\n * ```ts\n * import { Nebula } from \"thirdweb/ai\";\n *\n * const wallet = createWallet(\"io.metamask\");\n * const account = wallet.connect({ client });\n *\n * const result = await Nebula.execute({\n *   client,\n *   account, // transactions will be sent from this account\n *   message: \"send 0.0001 ETH to vitalik.eth\",\n *   contextFilter: {\n *     chains: [sepolia],\n *   },\n * });\n * ```\n *\n * Multi message prompt:\n *\n * ```ts\n * Nebula.execute({\n *  client,\n *  account,\n *  messages: [\n *    { role: \"user\", content: \"What's the address of vitalik.eth\" },\n *    {\n *      role: \"assistant\",\n *      content:\n *        \"The address of vitalik.eth is 0xd8dA6BF26964aF8E437eEa5e3616511D7G3a3298\",\n *    },\n *    { role: \"user\", content: \"Send them 0.0001 ETH\" },\n *  ],\n *  contextFilter: {\n *    chains: [sepolia],\n *  },\n * });\n * ```\n */\nexport async function execute(\n  input: Input & { account: Account },\n): Promise<SendTransactionResult> {\n  const result = await nebulaFetch(\"execute\", input);\n  // TODO: optionally only return the transaction without executing it?\n  if (result.transactions.length === 0) {\n    throw new Error(result.message);\n  }\n  const tx = result.transactions[0];\n  if (!tx) {\n    throw new Error(result.message);\n  }\n  return sendTransaction({\n    transaction: tx,\n    account: input.account,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;ACaA,IAAM,iBAAiB;AAuCvB,eAAsB,YACpB,MACA,OAAY;AArDd;AAuDE,QAAM,QAAQ,eAAe,MAAM,MAAM;AACzC,QAAM,WAAW,MAAM,MAAM,GAAG,cAAc,IAAI,IAAI,IAAI;IACxD,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,KAAK,UAAU;MACnB,GAAI,cAAc,QACd;QACE,UAAU,MAAM;UAElB;QACE,SAAS,MAAM;;MAErB,YAAY,MAAM;MAClB,GAAI,MAAM,UACN;QACE,gBAAgB;UACd,MAAM;UACN,uBAAuB,MAAM,QAAQ;;UAGzC,CAAA;MACJ,GAAI,MAAM,gBACN;QACE,gBAAgB;UACd,aACE,WAAM,cAAc,WAApB,mBAA4B,IAAI,CAAC,MAAM,EAAE,GAAG,SAAQ,OAAO,CAAA;UAC7D,kBACE,MAAM,cAAc,oBACnB,MAAM,UAAU,CAAC,MAAM,QAAQ,OAAO,IAAI,CAAA;UAC7C,oBAAoB,MAAM,cAAc,qBAAqB,CAAA;;UAGjE,CAAA;KACL;GACF;AACD,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,QAAQ,MAAM,SAAS,KAAI;AACjC,UAAM,IAAI,MAAM,qBAAqB,KAAK,EAAE;EAC9C;AACA,QAAM,OAAQ,MAAM,SAAS,KAAI;AAGjC,MAAI,eAAsC,CAAA;AAC1C,MAAI,KAAK,SAAS;AAChB,mBAAe,KAAK,QACjB,IAAI,CAAC,WAAU;AAEd,UAAI,OAAO,SAAS,oBAAoB;AACtC,cAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AAMjC,eAAO,mBAAmB;UACxB,OAAO,eAAe,GAAG,OAAO;UAChC,QAAQ,MAAM;UACd,IAAI,GAAG;UACP,OAAO,GAAG,QAAQ,SAAS,GAAG,KAAK,IAAI;UACvC,MAAM,GAAG;SACV;MACH;AACA,aAAO;IACT,CAAC,EACA,OAAO,CAAC,OAAO,OAAO,MAAS;EACpC;AAEA,SAAO;IACL,SAAS,KAAK;IACd,WAAW,KAAK;IAChB;;AAEJ;;;ACjFA,eAAsB,KAAK,OAAY;AACrC,SAAO,YAAY,QAAQ,KAAK;AAClC;;;ACAA,eAAsB,QACpB,OAAmC;AAEnC,QAAM,SAAS,MAAM,YAAY,WAAW,KAAK;AAEjD,MAAI,OAAO,aAAa,WAAW,GAAG;AACpC,UAAM,IAAI,MAAM,OAAO,OAAO;EAChC;AACA,QAAM,KAAK,OAAO,aAAa,CAAC;AAChC,MAAI,CAAC,IAAI;AACP,UAAM,IAAI,MAAM,OAAO,OAAO;EAChC;AACA,SAAO,gBAAgB;IACrB,aAAa;IACb,SAAS,MAAM;GAChB;AACH;", "names": []}