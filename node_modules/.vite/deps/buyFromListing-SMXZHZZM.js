import {
  getListing,
  isGetListingSupported,
  isListingValid
} from "./chunk-ZFCPKXTP.js";
import "./chunk-WXVMGE2S.js";
import "./chunk-DNDNTN4Q.js";
import "./chunk-LDVL4EMN.js";
import "./chunk-IVKLLG7V.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import {
  isNativeTokenAddress
} from "./chunk-YCZ3YGMG.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  prepareContractCall
} from "./chunk-3GH3RYOE.js";
import "./chunk-OIHZCUZQ.js";
import "./chunk-PLFYO732.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-LFHG7EDC.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-HAADYJEF.js";
import "./chunk-FYKFURXC.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-QGXAPRFG.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-4LB33PYO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/marketplace/__generated__/IDirectListings/write/buyFromListing.js
var FN_SELECTOR = "0x704232dc";
var FN_INPUTS = [
  {
    type: "uint256",
    name: "_listingId"
  },
  {
    type: "address",
    name: "_buyFor"
  },
  {
    type: "uint256",
    name: "_quantity"
  },
  {
    type: "address",
    name: "_currency"
  },
  {
    type: "uint256",
    name: "_expectedTotalPrice"
  }
];
var FN_OUTPUTS = [];
function isBuyFromListingSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function buyFromListing(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.listingId,
        resolvedOptions.buyFor,
        resolvedOptions.quantity,
        resolvedOptions.currency,
        resolvedOptions.expectedTotalPrice
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/marketplace/direct-listings/write/buyFromListing.js
function buyFromListing2(options) {
  return buyFromListing({
    contract: options.contract,
    asyncParams: async () => {
      const listing = await getListing({
        contract: options.contract,
        listingId: options.listingId
      });
      const listingValidity = await isListingValid({
        contract: options.contract,
        listing,
        quantity: options.quantity
      });
      if (!listingValidity.valid) {
        throw new Error(listingValidity.reason);
      }
      return {
        listingId: options.listingId,
        quantity: options.quantity,
        buyFor: options.recipient,
        currency: listing.currencyContractAddress,
        expectedTotalPrice: listing.pricePerToken * options.quantity,
        overrides: {
          value: isNativeTokenAddress(listing.currencyContractAddress) ? listing.pricePerToken * options.quantity : 0n,
          extraGas: 50000n,
          // add extra gas to account for router call
          erc20Value: isNativeTokenAddress(listing.currencyContractAddress) ? void 0 : {
            amountWei: listing.pricePerToken * options.quantity,
            tokenAddress: listing.currencyContractAddress
          }
        }
      };
    }
  });
}
function isBuyFromListingSupported2(availableSelectors) {
  return isBuyFromListingSupported(availableSelectors) && isGetListingSupported(availableSelectors);
}
export {
  buyFromListing2 as buyFromListing,
  isBuyFromListingSupported2 as isBuyFromListingSupported
};
//# sourceMappingURL=buyFromListing-SMXZHZZM.js.map
