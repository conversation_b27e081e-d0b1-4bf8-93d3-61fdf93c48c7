import {
  getContractNFTs,
  getNFT,
  getOwnedNFTs
} from "./chunk-H5DL3D6Z.js";
import {
  getTransactions
} from "./chunk-HGMV3JDR.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/insight/get-tokens.js
async function getOwnedTokens(args) {
  var _a;
  const [{ getV1TokensErc20ByOwnerAddress }, { getThirdwebDomains }, { getClientFetch }, { assertInsightEnabled }, { stringify }] = await Promise.all([
    import("./thirdweb-RRHPM3WK.js"),
    import("./domains-GG5HLJBF.js"),
    import("./fetch-EM5E7J4N.js"),
    import("./common-MFMBCIOL.js"),
    import("./json-TMY4LLIG.js")
  ]);
  const { client, chains, ownerAddress, queryOptions } = args;
  await assertInsightEnabled(chains);
  const defaultQueryOptions = {
    chain: chains.map((chain) => chain.id),
    include_spam: "false",
    metadata: "true",
    limit: 50
  };
  const result = await getV1TokensErc20ByOwnerAddress({
    baseUrl: `https://${getThirdwebDomains().insight}`,
    fetch: getClientFetch(client),
    path: {
      ownerAddress
    },
    query: {
      ...defaultQueryOptions,
      ...queryOptions
    }
  });
  if (result.error) {
    throw new Error(`${result.response.status} ${result.response.statusText} - ${result.error ? stringify(result.error) : "Unknown error"}`);
  }
  return transformOwnedToken(((_a = result.data) == null ? void 0 : _a.data) ?? []);
}
async function transformOwnedToken(token) {
  const { toTokens } = await import("./units-2VJP3HUQ.js");
  return token.map((t) => {
    const decimals = t.decimals ?? 18;
    const value = BigInt(t.balance);
    return {
      value,
      displayValue: toTokens(value, decimals),
      tokenAddress: t.token_address,
      chainId: t.chain_id,
      decimals,
      symbol: t.symbol ?? "",
      name: t.name ?? ""
    };
  });
}

// node_modules/thirdweb/dist/esm/insight/get-events.js
async function getContractEvents(options) {
  var _a;
  const [{ getV1EventsByContractAddress }, { getThirdwebDomains }, { getClientFetch }, { assertInsightEnabled }, { stringify }] = await Promise.all([
    import("./thirdweb-RRHPM3WK.js"),
    import("./domains-GG5HLJBF.js"),
    import("./fetch-EM5E7J4N.js"),
    import("./common-MFMBCIOL.js"),
    import("./json-TMY4LLIG.js")
  ]);
  const { client, chains, contractAddress, event, queryOptions, decodeLogs } = options;
  await assertInsightEnabled(chains);
  const defaultQueryOptions = {
    chain: chains.map((chain) => chain.id),
    limit: 100,
    decode: decodeLogs
  };
  if (event) {
    defaultQueryOptions.filter_topic_0 = event.topics[0];
    defaultQueryOptions.filter_topic_1 = event.topics[1];
    defaultQueryOptions.filter_topic_2 = event.topics[2];
    defaultQueryOptions.filter_topic_3 = event.topics[3];
  }
  const result = await getV1EventsByContractAddress({
    baseUrl: `https://${getThirdwebDomains().insight}`,
    fetch: getClientFetch(client),
    path: {
      contractAddress
    },
    query: {
      ...defaultQueryOptions,
      ...queryOptions
    }
  });
  if (result.error) {
    throw new Error(`${result.response.status} ${result.response.statusText} - ${result.error ? stringify(result.error) : "Unknown error"}`);
  }
  return ((_a = result.data) == null ? void 0 : _a.data) ?? [];
}

// node_modules/thirdweb/dist/esm/insight/index.js
var insight_exports = {};
__export(insight_exports, {
  getContractEvents: () => getContractEvents,
  getContractNFTs: () => getContractNFTs,
  getNFT: () => getNFT,
  getOwnedNFTs: () => getOwnedNFTs,
  getOwnedTokens: () => getOwnedTokens,
  getTransactions: () => getTransactions
});

export {
  getOwnedTokens,
  getContractEvents,
  insight_exports
};
//# sourceMappingURL=chunk-V4XDO7Z5.js.map
