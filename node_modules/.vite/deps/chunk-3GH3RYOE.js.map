{"version": 3, "sources": ["../../thirdweb/src/transaction/prepare-contract-call.ts"], "sourcesContent": ["import {\n  type Abi,\n  type AbiFunction,\n  type ExtractAbiFunctionNames,\n  parseAbiItem,\n} from \"abitype\";\nimport type { TransactionRequest } from \"viem\";\nimport type { ThirdwebContract } from \"../contract/contract.js\";\nimport { encodeAbiParameters } from \"../utils/abi/encodeAbiParameters.js\";\nimport {\n  type PreparedMethod,\n  prepareMethod,\n} from \"../utils/abi/prepare-method.js\";\nimport { resolvePromisedValue } from \"../utils/promise/resolve-promised-value.js\";\nimport {\n  type PrepareTransactionOptions,\n  prepareTransaction,\n} from \"./prepare-transaction.js\";\nimport type {\n  BaseTransactionOptions,\n  ParamsOption,\n  ParseMethod,\n} from \"./types.js\";\nimport { isAbiFunction } from \"./utils.js\";\n\nexport type PrepareContractCallOptions<\n  TAbi extends Abi = [],\n  TMethod extends\n    | AbiFunction\n    | string\n    | ((\n        contract: ThirdwebContract<TAbi>,\n      ) => Promise<AbiFunction>) = TAbi extends { length: 0 }\n    ? AbiFunction | string\n    : ExtractAbiFunctionNames<TAbi>,\n  TPreparedMethod extends PreparedMethod<\n    ParseMethod<TAbi, TMethod>\n  > = PreparedMethod<ParseMethod<TAbi, TMethod>>,\n> = BaseTransactionOptions<\n  Omit<\n    TransactionRequest,\n    | \"from\"\n    | \"to\"\n    | \"data\"\n    | \"value\"\n    | \"accessList\"\n    | \"gas\"\n    | \"gasPrice\"\n    | \"maxFeePerGas\"\n    | \"maxPriorityFeePerGas\"\n    | \"nonce\"\n    | \"authorizationList\"\n  > & {\n    contract: ThirdwebContract<TAbi>;\n    method: TMethod | TPreparedMethod;\n  } & ParamsOption<TPreparedMethod[1]> &\n    Omit<PrepareTransactionOptions, \"to\" | \"data\" | \"chain\" | \"client\">,\n  TAbi\n>;\n\n/**\n * Prepares a contract call by resolving the ABI function, parameters and encoded data. Optionally specify other properties such as value or gas price.\n * @param options - The options for preparing the contract call.\n * @returns A promise that resolves to the prepared transaction.\n * @transaction\n * @example\n *\n * ### Usage with a human-readable method signature:\n *\n * ```ts\n * import { prepareContractCall } from \"thirdweb\";\n *\n * const transaction = prepareContractCall({\n *  contract,\n *  method: \"function transfer(address to, uint256 value)\",\n *  params: [to, value],\n * });\n * ```\n *\n * ### Usage with explicit gas price and/or value:\n *\n * ```ts\n * import { prepareContractCall } from \"thirdweb\";\n * import { toWei } from \"thirdweb/utils\";\n *\n * const transaction = prepareContractCall({\n *  contract,\n *  method: \"function transfer(address to, uint256 value)\",\n *  params: [to, value],\n *  maxFeePerGas: 30n,\n *  maxPriorityFeePerGas: 1n,\n *  value: toWei(\"0.01\"),\n * });\n * ```\n *\n * ### Usage with ERC20 value:\n *\n * For transactions that transfer ERC20 tokens, you can specify the value as the amount of tokens to transfer.\n *\n * You can use this in conjuction with the [`getApprovalForTransaction`](https://portal.thirdweb.com/references/typescript/v5/getApprovalForTransaction) function to easily create approval transactions for ERC20 tokens.\n *\n * This value will also be read by the react hooks and UI components to present to total cost to the user.\n *\n * ```ts\n * import { prepareContractCall } from \"thirdweb\";\n * import { toWei } from \"thirdweb/utils\";\n *\n * const transaction = prepareContractCall({\n *  contract,\n *  method: \"function payWithCoin()\",\n *  params: [],\n *  erc20Value: {\n *    tokenAddress: \"0x...\", // the address of the ERC20 token\n *    amountWei: toWei(\"0.1\"), // the amount of tokens to transfer in wei\n *  },\n * });\n * ```\n *\n * ### Usage with a JSON ABI function object:\n *\n * ```ts\n * import { prepareContractCall } from \"thirdweb\";\n *\n * const transaction = prepareContractCall({\n *  contract,\n *  method: {\n *    name: \"transfer\",\n *    type: \"function\",\n *    inputs: [\n *      { name: \"to\", type: \"address\" },\n *      { name: \"value\", type: \"uint256\" },\n *    ],\n *    outputs: [],\n *    stateMutability: \"payable\"\n *   },\n *  params: [to, value],\n * });\n * ```\n *\n * ### Usage with the ABI defined on the contract:\n *\n * ```ts\n * import { getContract, prepareContractCall } from \"thirdweb\";\n * const contract = getContract({\n *  ..., // chain, address, client\n *  abi: [...] // ABI with a \"transfer\" method\n * });\n * const transaction = prepareContractCall({\n *  contract,\n *  method: \"transfer\", // <- this gets inferred from the contract\n *  params: [to, value],\n * });\n * ```\n *\n * ### Passing extra call data to the transaction\n * ```ts\n * import { getContract, prepareContractCall } from \"thirdweb\";\n * const contract = getContract({\n *   ..., // chain, address, client\n * });\n *\n * const transaction = prepareContractCall({\n *   contract,\n *   method: \"function transfer(address to, uint256 value)\",\n *   params: [...],\n *   // The extra call data MUST be encoded to hex before passing\n *   extraCallData: \"0x.......\"\n * });\n * ```\n */\nexport function prepareContractCall<\n  const TAbi extends Abi,\n  const TMethod extends TAbi extends {\n    length: 0;\n  }\n    ?\n        | AbiFunction\n        | `function ${string}`\n        | ((contract: ThirdwebContract<TAbi>) => Promise<AbiFunction>)\n    : ExtractAbiFunctionNames<TAbi>,\n  const TPreparedMethod extends PreparedMethod<\n    ParseMethod<TAbi, TMethod>\n  > = PreparedMethod<ParseMethod<TAbi, TMethod>>,\n>(options: PrepareContractCallOptions<TAbi, TMethod, TPreparedMethod>) {\n  type ParsedMethod_ = ParseMethod<TAbi, TMethod>;\n  type PreparedMethod_ = PreparedMethod<ParsedMethod_>;\n  const { contract, method, params, ...rest } = options;\n\n  const preparedMethodPromise = () =>\n    (async () => {\n      if (Array.isArray(method)) {\n        return method as PreparedMethod_;\n      }\n      if (isAbiFunction(method)) {\n        return prepareMethod(method as ParsedMethod_) as PreparedMethod_;\n      }\n\n      if (typeof method === \"function\") {\n        return prepareMethod(\n          // @ts-expect-error - method *is* function in this case\n          (await method(contract)) as ParsedMethod_,\n        ) as PreparedMethod_;\n      }\n      // if the method starts with the string `function ` we always will want to try to parse it\n      if (typeof method === \"string\" && method.startsWith(\"function \")) {\n        // @ts-expect-error - method *is* string in this case\n        const abiItem = parseAbiItem(method);\n        if (abiItem.type === \"function\") {\n          return prepareMethod(abiItem as ParsedMethod_) as PreparedMethod_;\n        }\n        throw new Error(`\"method\" passed is not of type \"function\"`);\n      }\n      // check if we have a \"abi\" on the contract\n      if (contract.abi && contract.abi?.length > 0) {\n        // extract the abiFunction from it\n        const abiFunction = contract.abi?.find(\n          (item) => item.type === \"function\" && item.name === method,\n        );\n        // if we were able to find it -> return it\n        if (abiFunction) {\n          return prepareMethod(abiFunction as ParsedMethod_) as PreparedMethod_;\n        }\n      }\n      throw new Error(`Could not resolve method \"${method}\".`);\n    })();\n\n  return prepareTransaction(\n    {\n      ...rest,\n      // these always inferred from the contract\n      to: contract.address,\n      chain: contract.chain,\n      client: contract.client,\n      data: async () => {\n        let preparedM: PreparedMethod_;\n        if (Array.isArray(method)) {\n          preparedM = method as PreparedMethod_;\n        } else {\n          preparedM = await preparedMethodPromise();\n        }\n\n        if (preparedM[1].length === 0) {\n          // just return the fn sig directly -> no params\n          return preparedM[0];\n        }\n\n        // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n        // we can do this because we know the specific formats of the values\n        return (preparedM[0] +\n          encodeAbiParameters(\n            preparedM[1],\n            // @ts-expect-error - TODO: fix this type issue\n            await resolvePromisedValue(params ?? []),\n          ).slice(2)) as `${(typeof preparedM)[0]}${string}`;\n      },\n    },\n    {\n      preparedMethod: preparedMethodPromise,\n      contract: contract,\n    },\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AA0KM,SAAU,oBAad,SAAmE;AAGnE,QAAM,EAAE,UAAU,QAAQ,QAAQ,GAAG,KAAI,IAAK;AAE9C,QAAM,wBAAwB,OAC3B,YAAW;AA7LhB;AA8LM,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO;IACT;AACA,QAAI,cAAc,MAAM,GAAG;AACzB,aAAO,cAAc,MAAuB;IAC9C;AAEA,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO;;QAEJ,MAAM,OAAO,QAAQ;MAAmB;IAE7C;AAEA,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW,WAAW,GAAG;AAEhE,YAAM,UAAU,aAAa,MAAM;AACnC,UAAI,QAAQ,SAAS,YAAY;AAC/B,eAAO,cAAc,OAAwB;MAC/C;AACA,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AAEA,QAAI,SAAS,SAAO,cAAS,QAAT,mBAAc,UAAS,GAAG;AAE5C,YAAM,eAAc,cAAS,QAAT,mBAAc,KAChC,CAAC,SAAS,KAAK,SAAS,cAAc,KAAK,SAAS;AAGtD,UAAI,aAAa;AACf,eAAO,cAAc,WAA4B;MACnD;IACF;AACA,UAAM,IAAI,MAAM,6BAA6B,MAAM,IAAI;EACzD,GAAE;AAEJ,SAAO,mBACL;IACE,GAAG;;IAEH,IAAI,SAAS;IACb,OAAO,SAAS;IAChB,QAAQ,SAAS;IACjB,MAAM,YAAW;AACf,UAAI;AACJ,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,oBAAY;MACd,OAAO;AACL,oBAAY,MAAM,sBAAqB;MACzC;AAEA,UAAI,UAAU,CAAC,EAAE,WAAW,GAAG;AAE7B,eAAO,UAAU,CAAC;MACpB;AAIA,aAAQ,UAAU,CAAC,IACjB;QACE,UAAU,CAAC;;QAEX,MAAM,qBAAqB,UAAU,CAAA,CAAE;MAAC,EACxC,MAAM,CAAC;IACb;KAEF;IACE,gBAAgB;IAChB;GACD;AAEL;", "names": []}