{"version": 3, "sources": ["../../thirdweb/src/rpc/actions/eth_call.ts", "../../thirdweb/src/transaction/read-contract.ts"], "sourcesContent": ["import type {\n  <PERSON><PERSON>ag,\n  EIP1193RequestFn,\n  EIP1474Methods,\n  Hex,\n  RpcStateMapping,\n  RpcStateOverride,\n  RpcTransactionRequest,\n} from \"viem\";\nimport { numberToHex } from \"../../utils/encoding/hex.js\";\n\ntype StateOverride = Record<\n  string,\n  {\n    /**\n     * Fake balance to set for the account before executing the call.\n     */\n    balance?: bigint;\n    /**\n     * Fake nonce to set for the account before executing the call.\n     */\n    nonce?: number;\n    /**\n     * Fake EVM bytecode to inject into the account before executing the call.\n     */\n    code?: Hex;\n    /**\n     * Fake key-value mapping to override **all** slots in the account storage before executing the call.\n     */\n    state?: RpcStateMapping;\n    /**\n     * Fake key-value mapping to override **individual** slots in the account storage before executing the call.\n     */\n    stateDiff?: RpcStateMapping;\n  }\n>;\n\nfunction encodeStateOverrides(overrides: StateOverride): RpcStateOverride {\n  return Object.fromEntries(\n    Object.entries(overrides).map(([address, override]) => {\n      return [\n        address,\n        {\n          balance: override.balance ? numberToHex(override.balance) : undefined,\n          nonce: override.nonce ? numberToHex(override.nonce) : undefined,\n          code: override.code,\n          state: override.state,\n          stateDiff: override.stateDiff,\n        },\n      ];\n    }),\n  );\n}\n\n/**\n * Executes a call or a transaction on the Ethereum network.\n * @param request - The EIP1193 request function.\n * @param params - The parameters for the call or transaction.\n * @returns A promise that resolves to the result of the call or transaction.\n * @rpc\n * @example\n * ```ts\n * import { getRpcClient, eth_call } from \"thirdweb/rpc\";\n * const rpcRequest = getRpcClient({ client, chain });\n * const result = await eth_call(rpcRequest, {\n *  to: \"0x...\",\n *  ...\n * });\n * ```\n */\nexport async function eth_call(\n  request: EIP1193RequestFn<EIP1474Methods>,\n  params: Partial<RpcTransactionRequest> & {\n    blockNumber?: bigint | number;\n    blockTag?: BlockTag;\n    stateOverrides?: StateOverride;\n  },\n): Promise<Hex> {\n  const { blockNumber, blockTag, ...txRequest } = params;\n  const blockNumberHex = blockNumber ? numberToHex(blockNumber) : undefined;\n  // default to \"latest\" if no block is provided\n  const block = blockNumberHex || blockTag || \"latest\";\n\n  return await request({\n    method: \"eth_call\",\n    params: params.stateOverrides\n      ? [\n          txRequest as Partial<RpcTransactionRequest>,\n          block,\n          encodeStateOverrides(params.stateOverrides),\n        ]\n      : [txRequest as Partial<RpcTransactionRequest>, block],\n  });\n}\n", "import {\n  type Abi,\n  type AbiFunction,\n  type AbiParameter,\n  type AbiParametersToPrimitiveTypes,\n  type ExtractAbiFunctionNames,\n  parseAbiItem,\n} from \"abitype\";\nimport { type TransactionRequest, decodeAbiParameters } from \"viem\";\nimport type { ThirdwebContract } from \"../contract/contract.js\";\nimport { isAbiFunction } from \"./utils.js\";\n\nimport type { PrepareTransactionOptions } from \"./prepare-transaction.js\";\nimport type {\n  BaseTransactionOptions,\n  ParamsOption,\n  ParseMethod,\n} from \"./types.js\";\n\nimport { eth_call } from \"../rpc/actions/eth_call.js\";\nimport { getRpcClient } from \"../rpc/rpc.js\";\nimport { encodeAbiParameters } from \"../utils/abi/encodeAbiParameters.js\";\nimport {\n  type PreparedMethod,\n  prepareMethod,\n} from \"../utils/abi/prepare-method.js\";\nimport { getAddress } from \"../utils/address.js\";\nimport type { Hex } from \"../utils/encoding/hex.js\";\n\nexport type ReadContractResult<outputs extends readonly AbiParameter[]> = // if the outputs are 0 length, return never, invalid case\n  outputs extends { length: 0 }\n    ? never\n    : outputs extends { length: 1 }\n      ? // if the outputs are 1 length, we'll always return the first element\n        AbiParametersToPrimitiveTypes<outputs>[0]\n      : // otherwise we'll return the array\n        AbiParametersToPrimitiveTypes<outputs>;\n\nexport type ReadContractOptions<\n  TAbi extends Abi = [],\n  TMethod extends\n    | AbiFunction\n    | string\n    | ((\n        contract: ThirdwebContract<TAbi>,\n      ) => Promise<AbiFunction>) = TAbi extends { length: 0 }\n    ? AbiFunction | string\n    : ExtractAbiFunctionNames<TAbi>,\n  TPreparedMethod extends PreparedMethod<\n    ParseMethod<TAbi, TMethod>\n  > = PreparedMethod<ParseMethod<TAbi, TMethod>>,\n> = BaseTransactionOptions<\n  Omit<\n    TransactionRequest,\n    | \"from\"\n    | \"to\"\n    | \"data\"\n    | \"value\"\n    | \"accessList\"\n    | \"gas\"\n    | \"gasPrice\"\n    | \"maxFeePerGas\"\n    | \"maxPriorityFeePerGas\"\n    | \"nonce\"\n  > & {\n    method: TMethod | TPreparedMethod;\n    from?: string;\n  } & ParamsOption<TPreparedMethod[1]> &\n    Omit<PrepareTransactionOptions, \"to\" | \"data\" | \"chain\" | \"client\">,\n  TAbi\n>;\n\n/**\n * ### Reads state from a deployed smart contract.\n *\n * Use this for raw read calls from a contract, but you can also use read [extensions](https://portal.thirdweb.com/typescript/v5/extensions/use) for predefined methods for common standards.\n *\n * @param options - The transaction options.\n * @returns A promise that resolves with the result of the read call.\n * @transaction\n * @example\n *\n * ### Raw contract call (recommended)\n *\n * You can read from any contract by using the solidity signature of the function you want to call.\n *\n * ```ts\n * import { getContract } from \"thirdweb\";\n * import { sepolia } from \"thirdweb/chains\";\n * import { useReadContract } from \"thirdweb/react\";\n *\n * const contract = getContract({\n *   client,\n *   address: \"0x...\",\n *   chain: sepolia,\n * });\n *\n * const { data, isLoading } = useReadContract({\n *   contract,\n *   method: \"function tokenURI(uint256 tokenId) returns (string)\",\n *   params: [1n],\n * });\n * ```\n *\n * Note that this is type safe, the params types will be enforced based on the signature.\n *\n * ### Raw contract call with `resolveMethod`\n *\n * If you don't have the solidity signature of the function you want to call, you can use the `resolveMethod` helper to resolve the method from any deployed contract.\n *\n * Note that this is not type safe, and will also have a 1 time overhead of resolving the contract ABI.\n *\n * ```ts\n * import { getContract, resolveMethod } from \"thirdweb\";\n * import { sepolia } from \"thirdweb/chains\";\n * import { useReadContract } from \"thirdweb/react\";\n *\n * const contract = getContract({\n *   client,\n *   address: \"0x...\",\n *   chain: sepolia,\n * });\n *\n * const { data, isLoading } = useReadContract({\n *   contract,\n *   method: resolveMethod(\"tokenURI\"),\n *   params: [1n],\n * });\n * ```\n */\nexport async function readContract<\n  const TAbi extends Abi,\n  const TMethod extends TAbi extends {\n    length: 0;\n  }\n    ?\n        | AbiFunction\n        | `function ${string}`\n        | ((contract: ThirdwebContract<TAbi>) => Promise<AbiFunction>)\n    : ExtractAbiFunctionNames<TAbi>,\n  const TPreparedMethod extends PreparedMethod<\n    ParseMethod<TAbi, TMethod>\n  > = PreparedMethod<ParseMethod<TAbi, TMethod>>,\n>(\n  options: ReadContractOptions<TAbi, TMethod, TPreparedMethod>,\n): Promise<ReadContractResult<TPreparedMethod[2]>> {\n  type ParsedMethod_ = ParseMethod<TAbi, TMethod>;\n  type PreparedMethod_ = PreparedMethod<ParsedMethod_>;\n  const { contract, method, params } = options;\n\n  const resolvePreparedMethod = async () => {\n    if (Array.isArray(method)) {\n      return method as PreparedMethod_;\n    }\n    if (isAbiFunction(method)) {\n      return prepareMethod(method as ParsedMethod_) as PreparedMethod_;\n    }\n\n    if (typeof method === \"function\") {\n      return prepareMethod(\n        // @ts-expect-error - we're sure it's a function\n        (await method(contract)) as ParsedMethod_,\n      ) as PreparedMethod_;\n    }\n    // if the method starts with the string `function ` we always will want to try to parse it\n    if (typeof method === \"string\" && method.startsWith(\"function \")) {\n      // @ts-expect-error - method *is* string in this case\n      const abiItem = parseAbiItem(method);\n      if (abiItem.type === \"function\") {\n        return prepareMethod(abiItem as ParsedMethod_) as PreparedMethod_;\n      }\n      throw new Error(`\"method\" passed is not of type \"function\"`);\n    }\n    // check if we have a \"abi\" on the contract\n    if (contract.abi && contract.abi?.length > 0) {\n      // extract the abiFunction from it\n      const abiFunction = contract.abi?.find(\n        (item) => item.type === \"function\" && item.name === method,\n      );\n      // if we were able to find it -> return it\n      if (abiFunction) {\n        return prepareMethod(abiFunction as ParsedMethod_) as PreparedMethod_;\n      }\n    }\n    throw new Error(`Could not resolve method \"${method}\".`);\n  };\n\n  // resolve in parallel\n  const [resolvedPreparedMethod, resolvedParams] = await Promise.all([\n    resolvePreparedMethod(),\n    typeof params === \"function\" ? params() : params,\n  ]);\n\n  let encodedData: Hex;\n\n  // if we have no inputs, we know it's just the signature\n  if (resolvedPreparedMethod[1].length === 0) {\n    encodedData = resolvedPreparedMethod[0];\n  } else {\n    // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n    // we can do this because we know the specific formats of the values\n    encodedData = (resolvedPreparedMethod[0] +\n      encodeAbiParameters(\n        resolvedPreparedMethod[1],\n        // @ts-expect-error - TODO: fix this type issue\n        resolvedParams,\n      ).slice(2)) as `${(typeof resolvedPreparedMethod)[0]}${string}`;\n  }\n\n  const rpcRequest = getRpcClient({\n    chain: contract.chain,\n    client: contract.client,\n  });\n\n  const result = await eth_call(rpcRequest, {\n    data: encodedData,\n    to: contract.address,\n    from: options.from ? getAddress(options.from) : undefined,\n  });\n  // use the prepared method to decode the result\n  const decoded = decodeAbiParameters(resolvedPreparedMethod[2], result);\n  if (Array.isArray(decoded) && decoded.length === 1) {\n    return decoded[0];\n  }\n\n  return decoded as ReadContractResult<TPreparedMethod[2]>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAqCA,SAAS,qBAAqB,WAAwB;AACpD,SAAO,OAAO,YACZ,OAAO,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,SAAS,QAAQ,MAAK;AACpD,WAAO;MACL;MACA;QACE,SAAS,SAAS,UAAU,YAAY,SAAS,OAAO,IAAI;QAC5D,OAAO,SAAS,QAAQ,YAAY,SAAS,KAAK,IAAI;QACtD,MAAM,SAAS;QACf,OAAO,SAAS;QAChB,WAAW,SAAS;;;EAG1B,CAAC,CAAC;AAEN;AAkBA,eAAsB,SACpB,SACA,QAIC;AAED,QAAM,EAAE,aAAa,UAAU,GAAG,UAAS,IAAK;AAChD,QAAM,iBAAiB,cAAc,YAAY,WAAW,IAAI;AAEhE,QAAM,QAAQ,kBAAkB,YAAY;AAE5C,SAAO,MAAM,QAAQ;IACnB,QAAQ;IACR,QAAQ,OAAO,iBACX;MACE;MACA;MACA,qBAAqB,OAAO,cAAc;QAE5C,CAAC,WAA6C,KAAK;GACxD;AACH;;;ACqCA,eAAsB,aAcpB,SAA4D;AAI5D,QAAM,EAAE,UAAU,QAAQ,OAAM,IAAK;AAErC,QAAM,wBAAwB,YAAW;AAtJ3C;AAuJI,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO;IACT;AACA,QAAI,cAAc,MAAM,GAAG;AACzB,aAAO,cAAc,MAAuB;IAC9C;AAEA,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO;;QAEJ,MAAM,OAAO,QAAQ;MAAmB;IAE7C;AAEA,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW,WAAW,GAAG;AAEhE,YAAM,UAAU,aAAa,MAAM;AACnC,UAAI,QAAQ,SAAS,YAAY;AAC/B,eAAO,cAAc,OAAwB;MAC/C;AACA,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AAEA,QAAI,SAAS,SAAO,cAAS,QAAT,mBAAc,UAAS,GAAG;AAE5C,YAAM,eAAc,cAAS,QAAT,mBAAc,KAChC,CAAC,SAAS,KAAK,SAAS,cAAc,KAAK,SAAS;AAGtD,UAAI,aAAa;AACf,eAAO,cAAc,WAA4B;MACnD;IACF;AACA,UAAM,IAAI,MAAM,6BAA6B,MAAM,IAAI;EACzD;AAGA,QAAM,CAAC,wBAAwB,cAAc,IAAI,MAAM,QAAQ,IAAI;IACjE,sBAAqB;IACrB,OAAO,WAAW,aAAa,OAAM,IAAK;GAC3C;AAED,MAAI;AAGJ,MAAI,uBAAuB,CAAC,EAAE,WAAW,GAAG;AAC1C,kBAAc,uBAAuB,CAAC;EACxC,OAAO;AAGL,kBAAe,uBAAuB,CAAC,IACrC;MACE,uBAAuB,CAAC;;MAExB;IAAc,EACd,MAAM,CAAC;EACb;AAEA,QAAM,aAAa,aAAa;IAC9B,OAAO,SAAS;IAChB,QAAQ,SAAS;GAClB;AAED,QAAM,SAAS,MAAM,SAAS,YAAY;IACxC,MAAM;IACN,IAAI,SAAS;IACb,MAAM,QAAQ,OAAO,WAAW,QAAQ,IAAI,IAAI;GACjD;AAED,QAAM,UAAU,oBAAoB,uBAAuB,CAAC,GAAG,MAAM;AACrE,MAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW,GAAG;AAClD,WAAO,QAAQ,CAAC;EAClB;AAEA,SAAO;AACT;", "names": []}