import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  readContract
} from "./chunk-OIHZCUZQ.js";
import {
  encodeAbiParameters
} from "./chunk-PLFYO732.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-LFHG7EDC.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-HAADYJEF.js";
import "./chunk-FYKFURXC.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import {
  decodeAbiParameters
} from "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-4LB33PYO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc1155/__generated__/IERC1155/read/balanceOf.js
var FN_SELECTOR = "0x00fdd58e";
var FN_INPUTS = [
  {
    type: "address",
    name: "_owner"
  },
  {
    type: "uint256",
    name: "tokenId"
  }
];
var FN_OUTPUTS = [
  {
    type: "uint256"
  }
];
function isBalanceOfSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function encodeBalanceOfParams(options) {
  return encodeAbiParameters(FN_INPUTS, [options.owner, options.tokenId]);
}
function encodeBalanceOf(options) {
  return FN_SELECTOR + encodeBalanceOfParams(options).slice(2);
}
function decodeBalanceOfResult(result) {
  return decodeAbiParameters(FN_OUTPUTS, result)[0];
}
async function balanceOf(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: [options.owner, options.tokenId]
  });
}
export {
  FN_SELECTOR,
  balanceOf,
  decodeBalanceOfResult,
  encodeBalanceOf,
  encodeBalanceOfParams,
  isBalanceOfSupported
};
//# sourceMappingURL=balanceOf-JQLI5ZYC.js.map
