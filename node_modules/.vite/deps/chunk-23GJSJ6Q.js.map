{"version": 3, "sources": ["../../thirdweb/src/extensions/erc1155/__generated__/IERC1155/read/uri.ts", "../../thirdweb/src/extensions/erc1155/read/getNFT.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"uri\" function.\n */\nexport type UriParams = {\n  tokenId: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"tokenId\" }>;\n};\n\nexport const FN_SELECTOR = \"0x0e89341c\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"tokenId\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"string\",\n  },\n] as const;\n\n/**\n * Checks if the `uri` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `uri` method is supported.\n * @extension ERC1155\n * @example\n * ```ts\n * import { isUriSupported } from \"thirdweb/extensions/erc1155\";\n * const supported = isUriSupported([\"0x...\"]);\n * ```\n */\nexport function isUriSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"uri\" function.\n * @param options - The options for the uri function.\n * @returns The encoded ABI parameters.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeUriParams } from \"thirdweb/extensions/erc1155\";\n * const result = encodeUriParams({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeUriParams(options: UriParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.tokenId]);\n}\n\n/**\n * Encodes the \"uri\" function into a Hex string with its parameters.\n * @param options - The options for the uri function.\n * @returns The encoded hexadecimal string.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeUri } from \"thirdweb/extensions/erc1155\";\n * const result = encodeUri({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeUri(options: UriParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeUriParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the uri function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC1155\n * @example\n * ```ts\n * import { decodeUriResult } from \"thirdweb/extensions/erc1155\";\n * const result = decodeUriResultResult(\"...\");\n * ```\n */\nexport function decodeUriResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"uri\" function on the contract.\n * @param options - The options for the uri function.\n * @returns The parsed result of the function call.\n * @extension ERC1155\n * @example\n * ```ts\n * import { uri } from \"thirdweb/extensions/erc1155\";\n *\n * const result = await uri({\n *  contract,\n *  tokenId: ...,\n * });\n *\n * ```\n */\nexport async function uri(options: BaseTransactionOptions<UriParams>) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.tokenId],\n  });\n}\n", "import { getNFT as getNFTInsight } from \"../../../insight/get-nfts.js\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { fetchTokenMetadata } from \"../../../utils/nft/fetchTokenMetadata.js\";\nimport { type NFT, parseNFT } from \"../../../utils/nft/parseNft.js\";\nimport { totalSupply } from \"../__generated__/IERC1155/read/totalSupply.js\";\nimport { uri } from \"../__generated__/IERC1155/read/uri.js\";\nexport { isUriSupported as isGetNFTSupported } from \"../__generated__/IERC1155/read/uri.js\";\n\n/**\n * Parameters for getting an NFT.\n * @extension ERC1155\n */\nexport type GetNFTParams = {\n  tokenId: bigint;\n  /**\n   * Whether to use the insight API to fetch the NFT.\n   * @default true\n   */\n  useIndexer?: boolean;\n};\n\n/**\n * Retrieves information about a specific ERC1155 non-fungible token (NFT).\n * @param options - The options for retrieving the NFT.\n * @returns A promise that resolves to the NFT object.\n * @extension ERC1155\n * @example\n * ```ts\n * import { getNFT } from \"thirdweb/extensions/erc1155\";\n * const nft = await getNFT({\n *  contract,\n *  tokenId: 1n,\n * });\n * ```\n */\nexport async function getNFT(\n  options: BaseTransactionOptions<GetNFTParams>,\n): Promise<NFT> {\n  const { useIndexer = true } = options;\n  if (useIndexer) {\n    try {\n      return await getNFTFromInsight(options);\n    } catch {\n      return await getNFTFromRPC(options);\n    }\n  }\n  return await getNFTFromRPC(options);\n}\n\nasync function getNFTFromInsight(\n  options: BaseTransactionOptions<GetNFTParams>,\n): Promise<NFT> {\n  const nft = await getNFTInsight({\n    client: options.contract.client,\n    chain: options.contract.chain,\n    contractAddress: options.contract.address,\n    tokenId: options.tokenId,\n  });\n  if (!nft) {\n    // fresh contracts might be delayed in indexing, so we fallback to RPC\n    return getNFTFromRPC(options);\n  }\n  return nft;\n}\n\nasync function getNFTFromRPC(\n  options: BaseTransactionOptions<GetNFTParams>,\n): Promise<NFT> {\n  const [tokenUri, supply] = await Promise.all([\n    uri({\n      contract: options.contract,\n      tokenId: options.tokenId,\n    }),\n    totalSupply({\n      contract: options.contract,\n      id: options.tokenId,\n      // in cases where the supply is not available -> fall back to 0\n    }).catch(() => 0n),\n  ]);\n  return parseNFT(\n    await fetchTokenMetadata({\n      client: options.contract.client,\n      tokenId: options.tokenId,\n      tokenUri,\n    }).catch(() => ({\n      id: options.tokenId,\n      type: \"ERC1155\",\n      uri: tokenUri,\n    })),\n    {\n      tokenId: options.tokenId,\n      tokenUri,\n      type: \"ERC1155\",\n      owner: null,\n      supply,\n      tokenAddress: options.contract.address,\n      chainId: options.contract.chain.id,\n    },\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAeO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,eAAe,oBAA4B;AACzD,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAsEA,eAAsB,IAAI,SAA0C;AAClE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO;GACzB;AACH;;;ACrFA,eAAsBA,QACpB,SAA6C;AAE7C,QAAM,EAAE,aAAa,KAAI,IAAK;AAC9B,MAAI,YAAY;AACd,QAAI;AACF,aAAO,MAAM,kBAAkB,OAAO;IACxC,QAAQ;AACN,aAAO,MAAM,cAAc,OAAO;IACpC;EACF;AACA,SAAO,MAAM,cAAc,OAAO;AACpC;AAEA,eAAe,kBACb,SAA6C;AAE7C,QAAM,MAAM,MAAM,OAAc;IAC9B,QAAQ,QAAQ,SAAS;IACzB,OAAO,QAAQ,SAAS;IACxB,iBAAiB,QAAQ,SAAS;IAClC,SAAS,QAAQ;GAClB;AACD,MAAI,CAAC,KAAK;AAER,WAAO,cAAc,OAAO;EAC9B;AACA,SAAO;AACT;AAEA,eAAe,cACb,SAA6C;AAE7C,QAAM,CAAC,UAAU,MAAM,IAAI,MAAM,QAAQ,IAAI;IAC3C,IAAI;MACF,UAAU,QAAQ;MAClB,SAAS,QAAQ;KAClB;IACD,YAAY;MACV,UAAU,QAAQ;MAClB,IAAI,QAAQ;;KAEb,EAAE,MAAM,MAAM,EAAE;GAClB;AACD,SAAO,SACL,MAAM,mBAAmB;IACvB,QAAQ,QAAQ,SAAS;IACzB,SAAS,QAAQ;IACjB;GACD,EAAE,MAAM,OAAO;IACd,IAAI,QAAQ;IACZ,MAAM;IACN,KAAK;IACL,GACF;IACE,SAAS,QAAQ;IACjB;IACA,MAAM;IACN,OAAO;IACP;IACA,cAAc,QAAQ,SAAS;IAC/B,SAAS,QAAQ,SAAS,MAAM;GACjC;AAEL;", "names": ["getNFT"]}