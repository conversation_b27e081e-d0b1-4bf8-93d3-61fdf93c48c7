import {
  deleteConnectParamsFromStorage
} from "./chunk-NCVQ56IM.js";
import {
  isInAppWallet
} from "./chunk-RZIS2W4Z.js";
import {
  isSmartWallet
} from "./chunk-QXWT2LHJ.js";
import {
  ClientScopedStorage
} from "./chunk-FUW7UPWG.js";
import {
  smartWallet
} from "./chunk-5BDJHUZI.js";
import {
  isEcosystemWallet
} from "./chunk-QDEEV5NE.js";
import {
  createStore
} from "./chunk-HFJPNBPY.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import {
  cacheChains
} from "./chunk-KQKMGIQ6.js";

// node_modules/thirdweb/dist/esm/wallets/in-app/core/wallet/is-in-app-signer.js
function isInAppSigner(options) {
  const isInAppOrEcosystem = (w) => isInAppWallet(w) || isEcosystemWallet(w);
  const isSmartWalletWithAdmin = isSmartWallet(options.wallet) && options.connectedWallets.some((w) => {
    var _a, _b, _c, _d, _e, _f;
    return isInAppOrEcosystem(w) && ((_b = (_a = w.getAccount()) == null ? void 0 : _a.address) == null ? void 0 : _b.toLowerCase()) === ((_f = (_e = (_d = (_c = options.wallet).getAdminAccount) == null ? void 0 : _d.call(_c)) == null ? void 0 : _e.address) == null ? void 0 : _f.toLowerCase());
  });
  return isInAppOrEcosystem(options.wallet) || isSmartWalletWithAdmin;
}

// node_modules/thirdweb/dist/esm/react/core/utils/isSmartWallet.js
function hasSmartAccount(activeWallet) {
  const config = activeWallet == null ? void 0 : activeWallet.getConfig();
  return activeWallet !== void 0 && (activeWallet.id === "smart" || activeWallet.id === "inApp" && !!config && "smartAccount" in config || isEcosystemWallet(activeWallet) && !!config && "smartAccount" in config);
}

// node_modules/thirdweb/dist/esm/reactive/computedStore.js
function computedStore(computation, dependencies) {
  const listeners = /* @__PURE__ */ new Set();
  let value = computation();
  const notify = () => {
    for (const listener of listeners) {
      listener();
    }
  };
  const setValue = (newValue) => {
    value = newValue;
    notify();
  };
  for (const store of dependencies) {
    store.subscribe(() => {
      setValue(computation());
    });
  }
  return {
    getValue() {
      return value;
    },
    subscribe(listener) {
      listeners.add(listener);
      return () => {
        listeners.delete(listener);
      };
    }
  };
}

// node_modules/thirdweb/dist/esm/reactive/effect.js
function effect(effectFn, dependencies, runOnMount = true) {
  if (runOnMount) {
    effectFn();
  }
  const unsubscribeList = dependencies.map((store) => {
    return store.subscribe(() => {
      effectFn();
    });
  });
  return () => {
    for (const fn of unsubscribeList) {
      fn();
    }
  };
}

// node_modules/thirdweb/dist/esm/wallets/manager/index.js
var CONNECTED_WALLET_IDS = "thirdweb:connected-wallet-ids";
var LAST_ACTIVE_EOA_ID = "thirdweb:active-wallet-id";
var LAST_ACTIVE_CHAIN = "thirdweb:active-chain";
function createConnectionManager(storage) {
  const activeWalletStore = createStore(void 0);
  const activeAccountStore = createStore(void 0);
  const activeWalletChainStore = createStore(void 0);
  const activeWalletConnectionStatusStore = createStore("unknown");
  const definedChainsStore = createStore(/* @__PURE__ */ new Map());
  effect(() => {
    cacheChains([...definedChainsStore.getValue().values()]);
  }, [definedChainsStore]);
  effect(() => {
    const chainVal = activeWalletChainStore.getValue();
    if (!chainVal) {
      return;
    }
    const definedChain = definedChainsStore.getValue().get(chainVal.id);
    if (!definedChain || definedChain === chainVal) {
      return;
    }
    activeWalletChainStore.setValue(definedChain);
  }, [definedChainsStore, activeWalletChainStore]);
  const walletIdToConnectedWalletMap = createStore(/* @__PURE__ */ new Map());
  const isAutoConnecting = createStore(false);
  const connectedWallets = computedStore(() => {
    return Array.from(walletIdToConnectedWalletMap.getValue().values());
  }, [walletIdToConnectedWalletMap]);
  const addConnectedWallet = (wallet) => {
    const oldValue = walletIdToConnectedWalletMap.getValue();
    if (oldValue.has(wallet.id)) {
      return;
    }
    const newValue = new Map(oldValue);
    newValue.set(wallet.id, wallet);
    walletIdToConnectedWalletMap.setValue(newValue);
  };
  const removeConnectedWallet = (wallet) => {
    const oldValue = walletIdToConnectedWalletMap.getValue();
    const newValue = new Map(oldValue);
    newValue.delete(wallet.id);
    walletIdToConnectedWalletMap.setValue(newValue);
  };
  const onWalletDisconnect = (wallet) => {
    deleteConnectParamsFromStorage(storage, wallet.id);
    removeConnectedWallet(wallet);
    if (activeWalletStore.getValue() === wallet) {
      storage.removeItem(LAST_ACTIVE_EOA_ID);
      activeAccountStore.setValue(void 0);
      activeWalletChainStore.setValue(void 0);
      activeWalletStore.setValue(void 0);
      activeWalletConnectionStatusStore.setValue("disconnected");
    }
  };
  const disconnectWallet = (wallet) => {
    onWalletDisconnect(wallet);
    wallet.disconnect();
  };
  const handleConnection = async (wallet, options) => {
    const account = wallet.getAccount();
    if (!account) {
      throw new Error("Cannot set a wallet without an account as active");
    }
    const activeWallet = await (async () => {
      if ((options == null ? void 0 : options.accountAbstraction) && !hasSmartAccount(wallet)) {
        return await handleSmartWalletConnection(wallet, options.client, options.accountAbstraction, onWalletDisconnect);
      } else {
        return wallet;
      }
    })();
    await storage.setItem(LAST_ACTIVE_EOA_ID, wallet.id);
    addConnectedWallet(wallet);
    if ((options == null ? void 0 : options.setWalletAsActive) !== false) {
      handleSetActiveWallet(activeWallet);
    }
    wallet.subscribe("accountChanged", async () => {
      var _a;
      const newWallet = await handleConnection(wallet, options);
      (_a = options == null ? void 0 : options.onConnect) == null ? void 0 : _a.call(options, newWallet);
    });
    return activeWallet;
  };
  const connect = async (wallet, options) => {
    var _a;
    const connectedWallet = await handleConnection(wallet, options);
    (_a = options == null ? void 0 : options.onConnect) == null ? void 0 : _a.call(options, connectedWallet);
    return connectedWallet;
  };
  const handleSetActiveWallet = (activeWallet) => {
    const account = activeWallet.getAccount();
    if (!account) {
      throw new Error("Cannot set a wallet without an account as active");
    }
    addConnectedWallet(activeWallet);
    activeWalletStore.setValue(activeWallet);
    activeAccountStore.setValue(account);
    activeWalletChainStore.setValue(activeWallet.getChain());
    activeWalletConnectionStatusStore.setValue("connected");
    const onAccountsChanged = (newAccount) => {
      activeAccountStore.setValue(newAccount);
    };
    const unsubAccounts = activeWallet.subscribe("accountChanged", onAccountsChanged);
    const unsubChainChanged = activeWallet.subscribe("chainChanged", (chain) => activeWalletChainStore.setValue(chain));
    const unsubDisconnect = activeWallet.subscribe("disconnect", () => {
      handleDisconnect();
    });
    const handleDisconnect = () => {
      onWalletDisconnect(activeWallet);
      unsubAccounts();
      unsubChainChanged();
      unsubDisconnect();
    };
  };
  const setActiveWallet = async (activeWallet) => {
    handleSetActiveWallet(activeWallet);
    if (activeWallet.id !== "smart") {
      await storage.setItem(LAST_ACTIVE_EOA_ID, activeWallet.id);
    }
  };
  effect(() => {
    const _chain = activeWalletChainStore.getValue();
    if (_chain) {
      storage.setItem(LAST_ACTIVE_CHAIN, stringify(_chain));
    } else {
      storage.removeItem(LAST_ACTIVE_CHAIN);
    }
  }, [activeWalletChainStore], false);
  effect(async () => {
    const prevAccounts = await getStoredConnectedWalletIds(storage) || [];
    const accounts = connectedWallets.getValue();
    const ids = accounts.map((acc) => acc == null ? void 0 : acc.id).filter((c) => !!c);
    storage.setItem(CONNECTED_WALLET_IDS, stringify(Array.from(/* @__PURE__ */ new Set([...prevAccounts, ...ids]))));
  }, [connectedWallets], false);
  const switchActiveWalletChain = async (chain) => {
    const wallet = activeWalletStore.getValue();
    if (!wallet) {
      throw new Error("No active wallet found");
    }
    if (!wallet.switchChain) {
      throw new Error("Wallet does not support switching chains");
    }
    if (isSmartWallet(wallet)) {
      const personalWalletId = await getStoredActiveWalletId(storage);
      if (personalWalletId) {
        const personalWallet = connectedWallets.getValue().find((w) => w.id === personalWalletId);
        if (personalWallet) {
          await personalWallet.switchChain(chain);
          await wallet.switchChain(chain);
          handleSetActiveWallet(wallet);
          return;
        }
      }
      await wallet.switchChain(chain);
      handleSetActiveWallet(wallet);
    } else {
      await wallet.switchChain(chain);
    }
    activeWalletChainStore.setValue(wallet.getChain());
  };
  function defineChains(chains) {
    const currentMapVal = definedChainsStore.getValue();
    const allChainsSame = chains.every((c) => {
      const definedChain = currentMapVal.get(c.id);
      return stringify(definedChain) === stringify(c);
    });
    if (allChainsSame) {
      return;
    }
    const newMapVal = new Map(currentMapVal);
    for (const c of chains) {
      newMapVal.set(c.id, c);
    }
    definedChainsStore.setValue(newMapVal);
  }
  return {
    activeWalletStore,
    activeAccountStore,
    connectedWallets,
    addConnectedWallet,
    disconnectWallet,
    setActiveWallet,
    connect,
    handleConnection,
    activeWalletChainStore,
    switchActiveWalletChain,
    activeWalletConnectionStatusStore,
    isAutoConnecting,
    removeConnectedWallet,
    defineChains
  };
}
async function getStoredConnectedWalletIds(storage) {
  try {
    const value = await storage.getItem(CONNECTED_WALLET_IDS);
    if (value) {
      return JSON.parse(value);
    }
    return [];
  } catch {
    return [];
  }
}
async function getStoredActiveWalletId(storage) {
  try {
    const value = await storage.getItem(LAST_ACTIVE_EOA_ID);
    if (value) {
      return value;
    }
  } catch {
  }
  return null;
}
async function getLastConnectedChain(storage) {
  try {
    const value = await storage.getItem(LAST_ACTIVE_CHAIN);
    if (value) {
      return JSON.parse(value);
    }
  } catch {
  }
  return null;
}
var handleSmartWalletConnection = async (eoaWallet, client, options, onWalletDisconnect) => {
  const signer = eoaWallet.getAccount();
  if (!signer) {
    throw new Error("Cannot set a wallet without an account as active");
  }
  const wallet = smartWallet(options);
  await wallet.connect({
    personalAccount: signer,
    client,
    chain: options.chain
  });
  const disconnectUnsub = eoaWallet.subscribe("disconnect", () => {
    handleDisconnect();
  });
  const handleDisconnect = () => {
    disconnectUnsub();
    onWalletDisconnect(wallet);
  };
  return wallet;
};

// node_modules/thirdweb/dist/esm/utils/timeoutPromise.js
function timeoutPromise(promise, option) {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(option.message));
    }, option.ms);
    promise.then((res) => {
      clearTimeout(timeoutId);
      resolve(res);
    }, (err) => {
      clearTimeout(timeoutId);
      reject(err);
    });
  });
}

// node_modules/thirdweb/dist/esm/wallets/in-app/web/lib/get-url-token.js
function getUrlToken() {
  if (typeof document === "undefined") {
    return void 0;
  }
  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const authResultString = params.get("authResult");
  const walletId = params.get("walletId");
  const authProvider = params.get("authProvider");
  const authCookie = params.get("authCookie");
  if ((authCookie || authResultString) && walletId) {
    const authResult = (() => {
      if (authResultString) {
        params.delete("authResult");
        return JSON.parse(decodeURIComponent(authResultString));
      }
    })();
    params.delete("walletId");
    params.delete("authProvider");
    params.delete("authCookie");
    window.history.pushState({}, "", `${window.location.pathname}?${params.toString()}`);
    return { walletId, authResult, authProvider, authCookie };
  }
  return void 0;
}

// node_modules/thirdweb/dist/esm/wallets/connection/autoConnectCore.js
var lastAutoConnectionResultPromise = void 0;
var autoConnectCore = async (props) => {
  if (lastAutoConnectionResultPromise && !props.force) {
    const lastResult = await lastAutoConnectionResultPromise;
    if (lastResult) {
      return true;
    }
  }
  const resultPromise = _autoConnectCore(props);
  lastAutoConnectionResultPromise = resultPromise;
  return resultPromise;
};
var _autoConnectCore = async ({ storage, props, createWalletFn, manager, connectOverride, getInstalledWallets, setLastAuthProvider }) => {
  var _a, _b, _c, _d, _e;
  const { wallets, onConnect } = props;
  const timeout = props.timeout ?? 15e3;
  let autoConnected = false;
  manager.isAutoConnecting.setValue(true);
  let [lastConnectedWalletIds, lastActiveWalletId] = await Promise.all([
    getStoredConnectedWalletIds(storage),
    getStoredActiveWalletId(storage)
  ]);
  const urlToken = getUrlToken();
  const wallet = wallets.find((w) => w.id === (urlToken == null ? void 0 : urlToken.walletId));
  if ((urlToken == null ? void 0 : urlToken.authCookie) && wallet) {
    const clientStorage = new ClientScopedStorage({
      storage,
      clientId: props.client.clientId,
      ecosystem: isEcosystemWallet(wallet) ? {
        id: wallet.id,
        partnerId: (_a = wallet.getConfig()) == null ? void 0 : _a.partnerId
      } : void 0
    });
    await clientStorage.saveAuthCookie(urlToken.authCookie);
  }
  if (urlToken == null ? void 0 : urlToken.walletId) {
    lastActiveWalletId = urlToken.walletId;
    lastConnectedWalletIds = (lastConnectedWalletIds == null ? void 0 : lastConnectedWalletIds.includes(urlToken.walletId)) ? lastConnectedWalletIds : [urlToken.walletId, ...lastConnectedWalletIds || []];
  }
  if (urlToken == null ? void 0 : urlToken.authProvider) {
    await (setLastAuthProvider == null ? void 0 : setLastAuthProvider(urlToken.authProvider, storage));
  }
  if (!lastConnectedWalletIds) {
    return autoConnected;
  }
  const lastConnectedChain = await getLastConnectedChain(storage) || props.chain;
  const availableWallets = [...wallets, ...(getInstalledWallets == null ? void 0 : getInstalledWallets()) ?? []];
  const activeWallet = lastActiveWalletId && (availableWallets.find((w) => w.id === lastActiveWalletId) || createWalletFn(lastActiveWalletId));
  if (activeWallet) {
    manager.activeWalletConnectionStatusStore.setValue("connecting");
    await timeoutPromise(handleWalletConnection({
      wallet: activeWallet,
      client: props.client,
      lastConnectedChain,
      authResult: urlToken == null ? void 0 : urlToken.authResult
    }), {
      ms: timeout,
      message: `AutoConnect timeout: ${timeout}ms limit exceeded.`
    }).catch((err) => {
      console.warn(err.message);
      if (props.onTimeout) {
        props.onTimeout();
      }
    });
    try {
      const connectedWallet = await (connectOverride ? connectOverride(activeWallet) : manager.connect(activeWallet, {
        client: props.client,
        accountAbstraction: props.accountAbstraction
      }));
      if (connectedWallet) {
        autoConnected = true;
        try {
          onConnect == null ? void 0 : onConnect(connectedWallet);
        } catch {
        }
      } else {
        manager.activeWalletConnectionStatusStore.setValue("disconnected");
      }
    } catch (e) {
      if (e instanceof Error) {
        console.warn("Error auto connecting wallet:", e.message);
      }
      manager.activeWalletConnectionStatusStore.setValue("disconnected");
    }
  } else {
    manager.activeWalletConnectionStatusStore.setValue("disconnected");
  }
  const otherWallets = availableWallets.filter((w) => w.id !== lastActiveWalletId && lastConnectedWalletIds.includes(w.id));
  for (const wallet2 of otherWallets) {
    try {
      await handleWalletConnection({
        wallet: wallet2,
        client: props.client,
        lastConnectedChain,
        authResult: urlToken == null ? void 0 : urlToken.authResult
      });
      manager.addConnectedWallet(wallet2);
    } catch {
    }
  }
  const isIAW = activeWallet && isInAppSigner({
    wallet: activeWallet,
    connectedWallets: activeWallet ? [activeWallet, ...otherWallets] : otherWallets
  });
  if (isIAW && ((_b = props.siweAuth) == null ? void 0 : _b.requiresAuth) && !((_c = props.siweAuth) == null ? void 0 : _c.isLoggedIn) && !((_d = props.siweAuth) == null ? void 0 : _d.isLoggingIn)) {
    await ((_e = props.siweAuth) == null ? void 0 : _e.doLogin().catch((err) => {
      console.warn("Error signing in with SIWE:", err.message);
    }));
  }
  manager.isAutoConnecting.setValue(false);
  return autoConnected;
};
async function handleWalletConnection(props) {
  return props.wallet.autoConnect({
    client: props.client,
    chain: props.lastConnectedChain,
    authResult: props.authResult
  });
}

export {
  isInAppSigner,
  hasSmartAccount,
  createConnectionManager,
  autoConnectCore
};
//# sourceMappingURL=chunk-SNFNA35G.js.map
