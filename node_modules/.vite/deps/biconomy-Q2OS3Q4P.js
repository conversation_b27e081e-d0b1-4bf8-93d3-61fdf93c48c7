import {
  ZERO_ADDRESS
} from "./chunk-YCZ3YGMG.js";
import {
  readContract
} from "./chunk-OIHZCUZQ.js";
import "./chunk-PLFYO732.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-LFHG7EDC.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-HAADYJEF.js";
import "./chunk-FYKFURXC.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import {
  encodeAbiParameters
} from "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  getAddress,
  keccak256
} from "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import {
  isHex
} from "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-4LB33PYO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/transaction/actions/gasless/providers/biconomy.js
var BATCH_ID = 0n;
async function prepareBiconomyTransaction({ account, serializableTransaction, transaction, gasless }) {
  const forwarderContract = getContract({
    address: gasless.relayerForwarderAddress,
    chain: transaction.chain,
    client: transaction.client
  });
  const nonce = await readContract({
    contract: forwarderContract,
    method: "function getNonce(address,uint256) view returns (uint256)",
    params: [account.address, BATCH_ID]
  });
  const deadline = Math.floor(Date.now() / 1e3) + (gasless.deadlineSeconds ?? 3600);
  const request = {
    from: account.address,
    to: serializableTransaction.to,
    token: ZERO_ADDRESS,
    txGas: serializableTransaction.gas,
    tokenGasPrice: 0n,
    batchId: BATCH_ID,
    batchNonce: nonce,
    deadline,
    data: serializableTransaction.data
  };
  if (!request.to) {
    throw new Error("Cannot send a transaction without a `to` address");
  }
  if (!request.txGas) {
    throw new Error("Cannot send a transaction without a `gas` value");
  }
  if (!request.data) {
    throw new Error("Cannot send a transaction without a `data` value");
  }
  const message = encodeAbiParameters([
    { type: "address" },
    { type: "address" },
    { type: "address" },
    { type: "uint256" },
    { type: "uint256" },
    { type: "uint256" },
    { type: "uint256" },
    { type: "bytes32" }
  ], [
    getAddress(request.from),
    getAddress(request.to),
    getAddress(request.token),
    request.txGas,
    request.tokenGasPrice,
    request.batchId,
    request.batchNonce,
    keccak256(request.data)
  ]);
  const signature = await account.signMessage({ message });
  return [request, signature];
}
async function relayBiconomyTransaction(options) {
  const [request, signature] = await prepareBiconomyTransaction(options);
  const response = await fetch("https://api.biconomy.io/api/v2/meta-tx/native", {
    method: "POST",
    body: stringify({
      apiId: options.gasless.apiId,
      params: [request, signature],
      from: request.from,
      to: request.to,
      gasLimit: request.txGas
    }),
    headers: {
      "x-api-key": options.gasless.apiKey,
      "Content-Type": "application/json;charset=utf-8"
    }
  });
  if (!response.ok) {
    throw new Error(`Failed to send transaction: ${await response.text()}`);
  }
  const json = await response.json();
  const transactionHash = json.txHash;
  if (isHex(transactionHash)) {
    return {
      transactionHash,
      chain: options.transaction.chain,
      client: options.transaction.client
    };
  }
  throw new Error(`Failed to send transaction: ${stringify(json)}`);
}
export {
  prepareBiconomyTransaction,
  relayBiconomyTransaction
};
//# sourceMappingURL=biconomy-Q2OS3Q4P.js.map
