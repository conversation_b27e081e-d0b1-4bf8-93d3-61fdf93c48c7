import {
  getUser
} from "./chunk-SBIK2FO7.js";
import {
  deploySmartAccount
} from "./chunk-FYY623ZK.js";
import {
  signTransaction
} from "./chunk-GLGO7WQD.js";
import "./chunk-S2QNH72K.js";
import {
  autoConnectCore,
  createConnectionManager
} from "./chunk-SNFNA35G.js";
import {
  DefaultWalletConnectRequestHandlers,
  createWalletConnectClient,
  createWalletConnectSession,
  disconnectWalletConnectSession,
  getActiveWalletConnectSessions
} from "./chunk-YSGJZKRF.js";
import "./chunk-SNLJIEF2.js";
import "./chunk-TR45YI6T.js";
import {
  getDefaultWallets
} from "./chunk-GWKPF6JP.js";
import {
  authenticate,
  authenticateWithRedirect,
  getProfiles,
  getUserEmail,
  getUserPhoneNumber,
  linkProfile,
  preAuthenticate,
  unlinkProfile
} from "./chunk-TW2EKXAY.js";
import {
  getWalletBalance
} from "./chunk-6PLZ73QQ.js";
import "./chunk-7N62H5IB.js";
import "./chunk-OJRBY574.js";
import "./chunk-TJPCO3UF.js";
import "./chunk-FUPOJN5U.js";
import "./chunk-NCVQ56IM.js";
import "./chunk-67YIWUOQ.js";
import {
  getWalletInfo
} from "./chunk-MZEIIHE4.js";
import "./chunk-RZIS2W4Z.js";
import "./chunk-QXWT2LHJ.js";
import "./chunk-AKEHYN4S.js";
import "./chunk-FUW7UPWG.js";
import {
  autoConnectEip1193Wallet,
  connectEip1193Wallet,
  createWallet,
  ecosystemWallet,
  getInstalledWalletProviders,
  getInstalledWallets,
  inAppWallet,
  injectedProvider,
  smartWallet,
  walletConnect
} from "./chunk-5BDJHUZI.js";
import {
  createWalletEmitter,
  trackConnect
} from "./chunk-SDE6XNUJ.js";
import "./chunk-QDEEV5NE.js";
import "./chunk-34X5PL56.js";
import "./chunk-QXYVYYMB.js";
import "./chunk-KZXPW4P4.js";
import "./chunk-IA4CMUWX.js";
import "./chunk-ZVAAMTP5.js";
import "./chunk-R4YODVWY.js";
import {
  webLocalStorage
} from "./chunk-G4H2UJKK.js";
import "./chunk-LWBFBP2R.js";
import "./chunk-LJJPSJ3C.js";
import "./chunk-K43SHKO4.js";
import "./chunk-NACC2RRT.js";
import "./chunk-3WB2EUD3.js";
import {
  getSignPayload as getSignPayload2
} from "./chunk-4XJYATTE.js";
import "./chunk-RKW6PCRI.js";
import "./chunk-OHXFGHHA.js";
import "./chunk-FEE2SXO2.js";
import "./chunk-RJGRI3EY.js";
import {
  getSignPayload as getSignPayload3
} from "./chunk-35SUGYXY.js";
import "./chunk-X2XBGGU2.js";
import "./chunk-3S7RRRP4.js";
import "./chunk-S6FQMGF4.js";
import "./chunk-QWTIS6CA.js";
import "./chunk-YCZ3YGMG.js";
import "./chunk-54TJVF2D.js";
import {
  randomPrivateKey,
  sign
} from "./chunk-V2MC2I4R.js";
import {
  from,
  getSignPayload
} from "./chunk-FSZ5BSZ7.js";
import {
  toHex as toHex2
} from "./chunk-4L6F72MM.js";
import "./chunk-M5BKQRCL.js";
import {
  eth_sendRawTransaction
} from "./chunk-CMXLKATA.js";
import "./chunk-LM2644TQ.js";
import "./chunk-3GH3RYOE.js";
import "./chunk-DUYIIKDP.js";
import "./chunk-OIHZCUZQ.js";
import "./chunk-PLFYO732.js";
import "./chunk-2M7BVURO.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-JMHMJ42H.js";
import {
  sendTransaction
} from "./chunk-K4XAEHXR.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-R462U44Z.js";
import "./chunk-WLZN2VO2.js";
import "./chunk-DVJU3TKU.js";
import {
  estimateGas
} from "./chunk-LFHG7EDC.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import "./chunk-HAADYJEF.js";
import "./chunk-FYKFURXC.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import {
  prepareTransaction
} from "./chunk-QGXAPRFG.js";
import "./chunk-2CIJO3V3.js";
import {
  publicKeyToAddress
} from "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import {
  secp256k1
} from "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-HGMV3JDR.js";
import "./chunk-7RWWVHOG.js";
import {
  getAddress
} from "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import {
  hexToNumber,
  isHex,
  toHex
} from "./chunk-OLGC3KE4.js";
import {
  fromNumber2 as fromNumber,
  fromString2 as fromString,
  toNumber2 as toNumber
} from "./chunk-UG7W3O5D.js";
import "./chunk-4LB33PYO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import {
  getCachedChain,
  getCachedChainIfExists
} from "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/utils/signatures/sign-message.js
function signMessage(options) {
  if ("privateKey" in options) {
    const payload = getSignPayload2(typeof options.message === "object" ? options.message.raw : fromString(options.message));
    const signature = sign({
      payload,
      privateKey: options.privateKey
    });
    return toHex2(signature);
  }
  if ("account" in options) {
    const { message, account } = options;
    return account.signMessage({ message });
  }
  throw new Error("Either privateKey or account is required");
}

// node_modules/thirdweb/dist/esm/utils/signatures/sign-typed-data.js
function signTypedData(options) {
  var _a;
  const { privateKey, ...typedData } = options;
  if (typeof ((_a = typedData.domain) == null ? void 0 : _a.chainId) === "string") {
    typedData.domain.chainId = toNumber(typedData.domain.chainId);
  }
  const payload = getSignPayload3(typedData);
  const signature = sign({
    payload,
    privateKey
  });
  return toHex2(signature);
}

// node_modules/thirdweb/dist/esm/wallets/private-key.js
function privateKeyToAccount(options) {
  const { client } = options;
  const privateKey = `0x${options.privateKey.replace(/^0x/, "")}`;
  const publicKey = toHex(secp256k1.getPublicKey(privateKey.slice(2), false));
  const address = publicKeyToAddress(publicKey);
  const account = {
    address: getAddress(address),
    sendTransaction: async (tx) => {
      const rpcRequest = getRpcClient({
        client,
        chain: getCachedChain(tx.chainId)
      });
      const signedTx = signTransaction({
        transaction: tx,
        privateKey
      });
      const transactionHash = await eth_sendRawTransaction(rpcRequest, signedTx);
      return {
        transactionHash
      };
    },
    signMessage: async ({ message }) => {
      return signMessage({
        message,
        privateKey
      });
    },
    signTypedData: async (_typedData) => {
      return signTypedData({
        ..._typedData,
        privateKey
      });
    },
    signTransaction: async (tx) => {
      return signTransaction({
        transaction: tx,
        privateKey
      });
    },
    signAuthorization: async (authorization) => {
      const signature = sign({
        payload: getSignPayload(authorization),
        privateKey
      });
      return from(authorization, { signature });
    }
  };
  return account;
}

// node_modules/thirdweb/dist/esm/wallets/utils/generateAccount.js
async function generateAccount(options) {
  const privateKey = toHex(secp256k1.utils.randomPrivateKey());
  return privateKeyToAccount({ privateKey, client: options.client });
}

// node_modules/thirdweb/dist/esm/wallets/getAllWalletsList.js
async function getAllWalletsList() {
  return (await import("./wallet-infos-7XY3FIL7.js")).default;
}

// node_modules/thirdweb/dist/esm/adapters/wallet-adapter.js
function createWalletAdapter(options) {
  const emitter = createWalletEmitter();
  let _chain = options.chain;
  return {
    id: "adapter",
    subscribe: emitter.subscribe,
    connect: async () => {
      emitter.emit("onConnect", options);
      return options.adaptedAccount;
    },
    autoConnect: async () => {
      emitter.emit("onConnect", options);
      return options.adaptedAccount;
    },
    disconnect: async () => {
      await options.onDisconnect();
      emitter.emit("disconnect", void 0);
    },
    getAccount() {
      return options.adaptedAccount;
    },
    getChain() {
      const cachedChain = getCachedChainIfExists(_chain.id);
      _chain = cachedChain || _chain;
      return _chain;
    },
    getConfig() {
      return options;
    },
    switchChain: async (chain) => {
      await options.switchChain(chain);
      _chain = chain;
      emitter.emit("chainChanged", chain);
    }
  };
}

// node_modules/thirdweb/dist/esm/adapters/eip1193/index.js
var eip1193_exports = {};
__export(eip1193_exports, {
  fromProvider: () => fromProvider,
  toProvider: () => toProvider
});

// node_modules/thirdweb/dist/esm/adapters/eip1193/from-eip1193.js
function fromProvider(options) {
  const id = options.walletId ?? "adapter";
  const emitter = createWalletEmitter();
  let account = void 0;
  let chain = void 0;
  let provider = void 0;
  const getProvider = async (params) => {
    provider = typeof options.provider === "function" ? await options.provider(params) : options.provider;
    return provider;
  };
  const unsubscribeChain = emitter.subscribe("chainChanged", (newChain) => {
    chain = newChain;
  });
  function reset() {
    account = void 0;
    chain = void 0;
  }
  let handleDisconnect = async () => {
  };
  const unsubscribeDisconnect = emitter.subscribe("disconnect", () => {
    reset();
    unsubscribeChain();
    unsubscribeDisconnect();
  });
  emitter.subscribe("accountChanged", (_account) => {
    account = _account;
  });
  let handleSwitchChain = async (c) => {
    await (provider == null ? void 0 : provider.request({
      method: "wallet_switchEthereumChain",
      params: [{ chainId: fromNumber(c.id) }]
    }));
  };
  return {
    id,
    subscribe: emitter.subscribe,
    getConfig: () => void 0,
    getChain() {
      if (!chain) {
        return void 0;
      }
      chain = getCachedChainIfExists(chain.id) || chain;
      return chain;
    },
    getAccount: () => account,
    connect: async (connectOptions) => {
      var _a;
      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await connectEip1193Wallet({
        id,
        provider: await getProvider({ chainId: (_a = connectOptions.chain) == null ? void 0 : _a.id }),
        client: connectOptions.client,
        chain: connectOptions.chain,
        emitter
      });
      account = connectedAccount;
      chain = connectedChain;
      handleDisconnect = doDisconnect;
      handleSwitchChain = doSwitchChain;
      emitter.emit("onConnect", connectOptions);
      trackConnect({
        client: connectOptions.client,
        walletType: id,
        walletAddress: account.address
      });
      return account;
    },
    autoConnect: async (connectOptions) => {
      var _a;
      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await autoConnectEip1193Wallet({
        id,
        provider: await getProvider({ chainId: (_a = connectOptions.chain) == null ? void 0 : _a.id }),
        emitter,
        chain: connectOptions.chain,
        client: connectOptions.client
      });
      account = connectedAccount;
      chain = connectedChain;
      handleDisconnect = doDisconnect;
      handleSwitchChain = doSwitchChain;
      emitter.emit("onConnect", connectOptions);
      trackConnect({
        client: connectOptions.client,
        walletType: id,
        walletAddress: account.address
      });
      return account;
    },
    disconnect: async () => {
      reset();
      await handleDisconnect();
      emitter.emit("disconnect", void 0);
    },
    switchChain: async (c) => {
      await handleSwitchChain(c);
      emitter.emit("chainChanged", c);
    }
  };
}

// node_modules/thirdweb/dist/esm/adapters/eip1193/to-eip1193.js
function toProvider(options) {
  const { chain, client, wallet, connectOverride } = options;
  const rpcClient = getRpcClient({ client, chain });
  return {
    on: wallet.subscribe,
    removeListener: () => {
    },
    request: async (request) => {
      if (request.method === "eth_sendTransaction") {
        const account = wallet.getAccount();
        if (!account) {
          throw new Error("Account not connected");
        }
        const result = await sendTransaction({
          transaction: prepareTransaction({
            ...request.params[0],
            chain,
            client
          }),
          account
        });
        return result.transactionHash;
      }
      if (request.method === "eth_estimateGas") {
        const account = wallet.getAccount();
        if (!account) {
          throw new Error("Account not connected");
        }
        return estimateGas({
          transaction: prepareTransaction({
            ...request.params[0],
            chain,
            client
          }),
          account
        });
      }
      if (request.method === "personal_sign") {
        const account = wallet.getAccount();
        if (!account) {
          throw new Error("Account not connected");
        }
        return account.signMessage({
          message: {
            raw: request.params[0]
          }
        });
      }
      if (request.method === "eth_signTypedData_v4") {
        const account = wallet.getAccount();
        if (!account) {
          throw new Error("Account not connected");
        }
        const data = JSON.parse(request.params[1]);
        return account.signTypedData(data);
      }
      if (request.method === "eth_accounts") {
        const account = wallet.getAccount();
        if (!account) {
          throw new Error("Account not connected");
        }
        return [account.address];
      }
      if (request.method === "eth_requestAccounts") {
        const account = connectOverride ? await connectOverride(wallet) : await wallet.connect({
          client
        });
        if (!account) {
          throw new Error("Unable to connect wallet");
        }
        return [account.address];
      }
      if (request.method === "wallet_switchEthereumChain" || request.method === "wallet_addEthereumChain") {
        const data = request.params[0];
        const chainIdHex = data.chainId;
        if (!chainIdHex) {
          throw new Error("Chain ID is required");
        }
        const chainId = isHex(chainIdHex) ? hexToNumber(chainIdHex) : chainIdHex;
        const chain2 = getCachedChain(chainId);
        return wallet.switchChain(chain2);
      }
      return rpcClient(request);
    }
  };
}

// node_modules/thirdweb/dist/esm/wallets/connection/autoConnect.js
async function autoConnect(props) {
  const wallets = props.wallets || getDefaultWallets(props);
  const manager = createConnectionManager(webLocalStorage);
  const result = await autoConnectCore({
    storage: webLocalStorage,
    props: {
      ...props,
      wallets
    },
    createWalletFn: createWallet,
    getInstalledWallets: () => {
      const specifiedWalletIds = new Set(wallets.map((x) => x.id));
      const installedWallets = getInstalledWalletProviders().filter((x) => !specifiedWalletIds.has(x.info.rdns)).map((x) => createWallet(x.info.rdns));
      return installedWallets;
    },
    manager
  });
  return result;
}
export {
  DefaultWalletConnectRequestHandlers,
  eip1193_exports as EIP1193,
  authenticate,
  authenticateWithRedirect,
  autoConnect,
  createWallet,
  createWalletAdapter,
  createWalletConnectClient,
  createWalletConnectSession,
  deploySmartAccount,
  disconnectWalletConnectSession,
  ecosystemWallet,
  inAppWallet as embeddedWallet,
  generateAccount,
  getActiveWalletConnectSessions,
  getAllWalletsList,
  getInstalledWallets,
  getProfiles,
  getUser,
  getUserEmail,
  getUserPhoneNumber,
  getWalletBalance,
  getWalletInfo,
  inAppWallet,
  injectedProvider,
  linkProfile,
  preAuthenticate,
  privateKeyToAccount as privateKeyAccount,
  privateKeyToAccount,
  randomPrivateKey,
  smartWallet,
  unlinkProfile,
  walletConnect
};
//# sourceMappingURL=thirdweb_wallets.js.map
