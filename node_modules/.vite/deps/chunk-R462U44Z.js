import {
  isZkSync<PERSON>hain
} from "./chunk-WLZN2VO2.js";
import {
  encode
} from "./chunk-DVJU3TKU.js";
import {
  estimateGas,
  getGasOverridesForTransaction,
  resolvePromisedValue
} from "./chunk-LFHG7EDC.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import {
  getAddress
} from "./chunk-FFXQ6EIY.js";

// node_modules/thirdweb/dist/esm/transaction/actions/to-serializable-transaction.js
async function toSerializableTransaction(options) {
  const isZkSync = await isZkSyncChain(options.transaction.chain);
  if (isZkSync) {
    const { getZkGasFees } = await import("./send-eip712-transaction-CZEUKIB2.js");
    const { gas: gas2, maxFeePerGas, maxPriorityFeePerGas } = await getZkGasFees({
      transaction: options.transaction,
      from: typeof options.from === "string" ? getAddress(options.from) : options.from !== void 0 ? getAddress(options.from.address) : void 0
    });
    options.transaction = {
      ...options.transaction,
      gas: gas2,
      maxFeePerGas,
      maxPriorityFeePerGas
    };
  }
  const rpcRequest = getRpcClient(options.transaction);
  const chainId = options.transaction.chain.id;
  const from = options.from;
  let [data, nonce, gas, feeData, to, accessList, value, authorizationList, type] = await Promise.all([
    encode(options.transaction),
    (async () => {
      const resolvedNonce = await resolvePromisedValue(options.transaction.nonce);
      if (resolvedNonce !== void 0) {
        return resolvedNonce;
      }
      return from ? await import("./eth_getTransactionCount-FE5XVHP5.js").then(({ eth_getTransactionCount }) => eth_getTransactionCount(rpcRequest, {
        address: typeof from === "string" ? getAddress(from) : getAddress(from.address),
        blockTag: "pending"
      })) : void 0;
    })(),
    // takes the same options as the sendTransaction function thankfully!
    estimateGas({
      ...options,
      from: options.from
    }),
    getGasOverridesForTransaction(options.transaction),
    resolvePromisedValue(options.transaction.to),
    resolvePromisedValue(options.transaction.accessList),
    resolvePromisedValue(options.transaction.value),
    resolvePromisedValue(options.transaction.authorizationList),
    resolvePromisedValue(options.transaction.type)
  ]);
  const extraGas = await resolvePromisedValue(options.transaction.extraGas);
  if (extraGas) {
    gas += extraGas;
  }
  return {
    to,
    chainId,
    data,
    gas,
    nonce,
    accessList,
    value,
    authorizationList,
    type,
    ...feeData
  };
}

export {
  toSerializableTransaction
};
//# sourceMappingURL=chunk-R462U44Z.js.map
