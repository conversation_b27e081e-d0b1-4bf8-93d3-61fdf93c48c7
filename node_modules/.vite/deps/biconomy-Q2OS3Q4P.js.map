{"version": 3, "sources": ["../../thirdweb/src/transaction/actions/gasless/providers/biconomy.ts"], "sourcesContent": ["import type { Address } from \"abitype\";\nimport { encodeAbiParameters } from \"viem\";\nimport { ZERO_ADDRESS } from \"../../../../constants/addresses.js\";\nimport { getContract } from \"../../../../contract/contract.js\";\nimport { getAddress } from \"../../../../utils/address.js\";\nimport { isHex } from \"../../../../utils/encoding/helpers/is-hex.js\";\nimport { keccak256 } from \"../../../../utils/hashing/keccak256.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Account } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { PreparedTransaction } from \"../../../prepare-transaction.js\";\nimport { readContract } from \"../../../read-contract.js\";\nimport type { SerializableTransaction } from \"../../../serialize-transaction.js\";\nimport type { WaitForReceiptOptions } from \"../../wait-for-tx-receipt.js\";\n\n/**\n * @transaction\n */\nexport type BiconomyOptions = {\n  provider: \"biconomy\";\n  // you can find the correct forwarder for your network here: https://docs-gasless.biconomy.io/misc/contract-addresses\n  relayerForwarderAddress: Address;\n  apiId: string;\n  apiKey: string;\n  deadlineSeconds?: number; // default: 3600\n};\n\ntype SendBiconomyTransactionOptions = {\n  account: Account;\n  // TODO: update this to `Transaction<\"prepared\">` once the type is available to ensure only prepared transactions are accepted\n  // biome-ignore lint/suspicious/noExplicitAny: library function that accepts any prepared transaction type\n  transaction: PreparedTransaction<any>;\n  serializableTransaction: SerializableTransaction;\n  gasless: BiconomyOptions;\n};\n\n// we do not send multiple batches so this stays consistent\nconst BATCH_ID = 0n;\n\n/**\n * @internal - only exported for testing\n */\nexport async function prepareBiconomyTransaction({\n  account,\n  serializableTransaction,\n  transaction,\n  gasless,\n}: SendBiconomyTransactionOptions) {\n  const forwarderContract = getContract({\n    address: gasless.relayerForwarderAddress,\n    chain: transaction.chain,\n    client: transaction.client,\n  });\n\n  // get the nonce\n  const nonce = await readContract({\n    contract: forwarderContract,\n    method: \"function getNonce(address,uint256) view returns (uint256)\",\n    params: [account.address, BATCH_ID],\n  });\n\n  const deadline =\n    Math.floor(Date.now() / 1000) + (gasless.deadlineSeconds ?? 3600);\n\n  const request = {\n    from: account.address,\n    to: serializableTransaction.to,\n    token: ZERO_ADDRESS,\n    txGas: serializableTransaction.gas,\n    tokenGasPrice: 0n,\n    batchId: BATCH_ID,\n    batchNonce: nonce,\n    deadline: deadline,\n    data: serializableTransaction.data,\n  };\n\n  if (!request.to) {\n    throw new Error(\"Cannot send a transaction without a `to` address\");\n  }\n  if (!request.txGas) {\n    throw new Error(\"Cannot send a transaction without a `gas` value\");\n  }\n  if (!request.data) {\n    throw new Error(\"Cannot send a transaction without a `data` value\");\n  }\n\n  // create the hash\n  const message = encodeAbiParameters(\n    [\n      { type: \"address\" },\n      { type: \"address\" },\n      { type: \"address\" },\n      { type: \"uint256\" },\n      { type: \"uint256\" },\n      { type: \"uint256\" },\n      { type: \"uint256\" },\n      { type: \"bytes32\" },\n    ],\n    [\n      getAddress(request.from),\n      getAddress(request.to),\n      getAddress(request.token),\n      request.txGas,\n      request.tokenGasPrice,\n      request.batchId,\n      request.batchNonce,\n      keccak256(request.data),\n    ],\n  );\n\n  const signature = await account.signMessage({ message });\n\n  return [request, signature] as const;\n}\n\n/**\n * @internal\n */\nexport async function relayBiconomyTransaction(\n  options: SendBiconomyTransactionOptions,\n): Promise<WaitForReceiptOptions> {\n  const [request, signature] = await prepareBiconomyTransaction(options);\n\n  // send the transaction to the biconomy api\n  const response = await fetch(\n    \"https://api.biconomy.io/api/v2/meta-tx/native\",\n    {\n      method: \"POST\",\n      body: stringify({\n        apiId: options.gasless.apiId,\n        params: [request, signature],\n        from: request.from,\n        to: request.to,\n        gasLimit: request.txGas,\n      }),\n      headers: {\n        \"x-api-key\": options.gasless.apiKey,\n        \"Content-Type\": \"application/json;charset=utf-8\",\n      },\n    },\n  );\n  if (!response.ok) {\n    throw new Error(`Failed to send transaction: ${await response.text()}`);\n  }\n  const json = await response.json();\n  const transactionHash = json.txHash;\n  if (isHex(transactionHash)) {\n    return {\n      transactionHash: transactionHash,\n      chain: options.transaction.chain,\n      client: options.transaction.client,\n    };\n  }\n  throw new Error(`Failed to send transaction: ${stringify(json)}`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,IAAM,WAAW;AAKjB,eAAsB,2BAA2B,EAC/C,SACA,yBACA,aACA,QAAO,GACwB;AAC/B,QAAM,oBAAoB,YAAY;IACpC,SAAS,QAAQ;IACjB,OAAO,YAAY;IACnB,QAAQ,YAAY;GACrB;AAGD,QAAM,QAAQ,MAAM,aAAa;IAC/B,UAAU;IACV,QAAQ;IACR,QAAQ,CAAC,QAAQ,SAAS,QAAQ;GACnC;AAED,QAAM,WACJ,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI,KAAK,QAAQ,mBAAmB;AAE9D,QAAM,UAAU;IACd,MAAM,QAAQ;IACd,IAAI,wBAAwB;IAC5B,OAAO;IACP,OAAO,wBAAwB;IAC/B,eAAe;IACf,SAAS;IACT,YAAY;IACZ;IACA,MAAM,wBAAwB;;AAGhC,MAAI,CAAC,QAAQ,IAAI;AACf,UAAM,IAAI,MAAM,kDAAkD;EACpE;AACA,MAAI,CAAC,QAAQ,OAAO;AAClB,UAAM,IAAI,MAAM,iDAAiD;EACnE;AACA,MAAI,CAAC,QAAQ,MAAM;AACjB,UAAM,IAAI,MAAM,kDAAkD;EACpE;AAGA,QAAM,UAAU,oBACd;IACE,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;IACjB,EAAE,MAAM,UAAS;KAEnB;IACE,WAAW,QAAQ,IAAI;IACvB,WAAW,QAAQ,EAAE;IACrB,WAAW,QAAQ,KAAK;IACxB,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,UAAU,QAAQ,IAAI;GACvB;AAGH,QAAM,YAAY,MAAM,QAAQ,YAAY,EAAE,QAAO,CAAE;AAEvD,SAAO,CAAC,SAAS,SAAS;AAC5B;AAKA,eAAsB,yBACpB,SAAuC;AAEvC,QAAM,CAAC,SAAS,SAAS,IAAI,MAAM,2BAA2B,OAAO;AAGrE,QAAM,WAAW,MAAM,MACrB,iDACA;IACE,QAAQ;IACR,MAAM,UAAU;MACd,OAAO,QAAQ,QAAQ;MACvB,QAAQ,CAAC,SAAS,SAAS;MAC3B,MAAM,QAAQ;MACd,IAAI,QAAQ;MACZ,UAAU,QAAQ;KACnB;IACD,SAAS;MACP,aAAa,QAAQ,QAAQ;MAC7B,gBAAgB;;GAEnB;AAEH,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,+BAA+B,MAAM,SAAS,KAAI,CAAE,EAAE;EACxE;AACA,QAAM,OAAO,MAAM,SAAS,KAAI;AAChC,QAAM,kBAAkB,KAAK;AAC7B,MAAI,MAAM,eAAe,GAAG;AAC1B,WAAO;MACL;MACA,OAAO,QAAQ,YAAY;MAC3B,QAAQ,QAAQ,YAAY;;EAEhC;AACA,QAAM,IAAI,MAAM,+BAA+B,UAAU,IAAI,CAAC,EAAE;AAClE;", "names": []}