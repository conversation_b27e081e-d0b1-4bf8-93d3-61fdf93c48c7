import {
  once
} from "./chunk-NACC2RRT.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  prepareContractCall
} from "./chunk-3GH3RYOE.js";
import {
  encodeAbiParameters
} from "./chunk-PLFYO732.js";
import {
  toUnits
} from "./chunk-HAADYJEF.js";

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IERC20/write/transfer.js
var FN_SELECTOR = "0xa9059cbb";
var FN_INPUTS = [
  {
    type: "address",
    name: "to"
  },
  {
    type: "uint256",
    name: "value"
  }
];
var FN_OUTPUTS = [
  {
    type: "bool"
  }
];
function isTransferSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function encodeTransferParams(options) {
  return encodeAbiParameters(FN_INPUTS, [options.to, options.value]);
}
function encodeTransfer(options) {
  return FN_SELECTOR + encodeTransferParams(options).slice(2);
}
function transfer(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.to, resolvedOptions.value];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/write/transfer.js
function transfer2(options) {
  return transfer({
    contract: options.contract,
    asyncParams: async () => {
      let amount;
      if ("amount" in options) {
        const { decimals } = await import("./decimals-K3BF34VF.js");
        const d = await decimals(options).catch(() => 18);
        amount = toUnits(options.amount.toString(), d);
      } else {
        amount = options.amountWei;
      }
      return {
        to: options.to,
        value: amount,
        overrides: {
          erc20Value: {
            amountWei: amount,
            tokenAddress: options.contract.address
          },
          ...options.overrides
        }
      };
    }
  });
}

export {
  isTransferSupported,
  encodeTransfer,
  transfer2 as transfer
};
//# sourceMappingURL=chunk-PPFWNLQY.js.map
