{"version": 3, "sources": ["../../thirdweb/src/wallets/utils/getWalletBalance.ts"], "sourcesContent": ["import type { Chain } from \"../../chains/types.js\";\nimport {\n  getChainDecimals,\n  getChainNativeCurrencyName,\n  getChainSymbol,\n} from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { NATIVE_TOKEN_ADDRESS } from \"../../constants/addresses.js\";\nimport { getContract } from \"../../contract/contract.js\";\nimport type { GetBalanceResult } from \"../../extensions/erc20/read/getBalance.js\";\nimport { eth_getBalance } from \"../../rpc/actions/eth_getBalance.js\";\nimport { getRpcClient } from \"../../rpc/rpc.js\";\nimport { toTokens } from \"../../utils/units.js\";\n\nexport type GetWalletBalanceOptions = {\n  address: string;\n  client: ThirdwebClient;\n  chain: Chain;\n  /**\n   * (Optional) The address of the token to retrieve the balance for. If not provided, the balance of the native token will be retrieved.\n   */\n  tokenAddress?: string;\n};\n\nexport type GetWalletBalanceResult = GetBalanceResult;\n\n/**\n * Retrieves the balance of a token or native currency for a given wallet.\n * @param options - The options for retrieving the token balance.\n * @param options.address - The address for which to retrieve the balance.\n * @param options.client - The Thirdweb client to use for the request.\n * @param options.chain - The chain for which to retrieve the balance.\n * @param options.tokenAddress - (Optional) The address of the token to retrieve the balance for. If not provided, the balance of the native token will be retrieved.\n * @returns A promise that resolves to the token balance result.\n * @example\n * ```ts\n * import { getWalletBalance } from \"thirdweb/wallets\";\n * const balance = await getWalletBalance({ address, client, chain, tokenAddress });\n * ```\n * @walletUtils\n */\nexport async function getWalletBalance(\n  options: GetWalletBalanceOptions,\n): Promise<GetWalletBalanceResult> {\n  const { address, client, chain, tokenAddress } = options;\n  // erc20 case\n  if (tokenAddress) {\n    // load balanceOf dynamically to avoid circular dependency\n    const { getBalance } = await import(\n      \"../../extensions/erc20/read/getBalance.js\"\n    );\n    return getBalance({\n      contract: getContract({ client, chain, address: tokenAddress }),\n      address,\n    });\n  }\n  // native token case\n  const rpcRequest = getRpcClient({ client, chain });\n\n  const [nativeSymbol, nativeDecimals, nativeName, nativeBalance] =\n    await Promise.all([\n      getChainSymbol(chain),\n      getChainDecimals(chain),\n      getChainNativeCurrencyName(chain),\n      eth_getBalance(rpcRequest, { address }),\n    ]);\n\n  return {\n    value: nativeBalance,\n    decimals: nativeDecimals,\n    displayValue: toTokens(nativeBalance, nativeDecimals),\n    symbol: nativeSymbol,\n    name: nativeName,\n    tokenAddress: tokenAddress ?? NATIVE_TOKEN_ADDRESS,\n    chainId: chain.id,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAyCA,eAAsB,iBACpB,SAAgC;AAEhC,QAAM,EAAE,SAAS,QAAQ,OAAO,aAAY,IAAK;AAEjD,MAAI,cAAc;AAEhB,UAAM,EAAE,WAAU,IAAK,MAAM,OAC3B,0BAA2C;AAE7C,WAAO,WAAW;MAChB,UAAU,YAAY,EAAE,QAAQ,OAAO,SAAS,aAAY,CAAE;MAC9D;KACD;EACH;AAEA,QAAM,aAAa,aAAa,EAAE,QAAQ,MAAK,CAAE;AAEjD,QAAM,CAAC,cAAc,gBAAgB,YAAY,aAAa,IAC5D,MAAM,QAAQ,IAAI;IAChB,eAAe,KAAK;IACpB,iBAAiB,KAAK;IACtB,2BAA2B,KAAK;IAChC,eAAe,YAAY,EAAE,QAAO,CAAE;GACvC;AAEH,SAAO;IACL,OAAO;IACP,UAAU;IACV,cAAc,SAAS,eAAe,cAAc;IACpD,QAAQ;IACR,MAAM;IACN,cAAc,gBAAgB;IAC9B,SAAS,MAAM;;AAEnB;", "names": []}