{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IDropSinglePhase/read/claimCondition.ts"], "sourcesContent": ["import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0xd637ed59\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"tuple\",\n    name: \"condition\",\n    components: [\n      {\n        type: \"uint256\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxClaimableSupply\",\n      },\n      {\n        type: \"uint256\",\n        name: \"supplyClaimed\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantityLimitPerWallet\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"merkle<PERSON><PERSON>\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n      {\n        type: \"string\",\n        name: \"metadata\",\n      },\n    ],\n  },\n] as const;\n\n/**\n * Checks if the `claimCondition` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `claimCondition` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isClaimConditionSupported } from \"thirdweb/extensions/erc721\";\n * const supported = isClaimConditionSupported([\"0x...\"]);\n * ```\n */\nexport function isClaimConditionSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the claimCondition function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC721\n * @example\n * ```ts\n * import { decodeClaimConditionResult } from \"thirdweb/extensions/erc721\";\n * const result = decodeClaimConditionResultResult(\"...\");\n * ```\n */\nexport function decodeClaimConditionResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"claimCondition\" function on the contract.\n * @param options - The options for the claimCondition function.\n * @returns The parsed result of the function call.\n * @extension ERC721\n * @example\n * ```ts\n * import { claimCondition } from \"thirdweb/extensions/erc721\";\n *\n * const result = await claimCondition({\n *  contract,\n * });\n *\n * ```\n */\nexport async function claimCondition(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n"], "mappings": ";;;;;;;;;;;AAOO,IAAM,cAAc;AAC3B,IAAM,YAAY,CAAA;AAClB,IAAM,aAAa;EACjB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAiBR,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAaM,SAAU,2BAA2B,QAAW;AACpD,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAiBA,eAAsB,eAAe,SAA+B;AAClE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAA;GACT;AACH;", "names": []}