import { useEffect, useState, useRef } from "react";
import Sidebar from "@/components/Sidebar";
import MainContent, { MainContentRef } from "@/components/MainContent";
import TopWalletButton from "@/components/TopWalletButton";
import { useChatStore } from "@/store/chatStore";
import { Button } from "@/components/ui/button";
import { Sun, Moon, MenuIcon } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { useNebulaChat } from "@/hooks/use-nebula-chat";
import { useIsMobile } from "@/hooks/use-mobile";
import { CubeIcon } from "@/components/icons";
import WalletDebugger from "@/components/WalletDebugger";

const LoggedInHome = () => {
  const { activeChat, setActiveChat, setChats } = useChatStore();
  const { theme, toggleTheme } = useTheme();
  const isMobile = useIsMobile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(!isMobile);
  const mainContentRef = useRef<MainContentRef>(null);

  // Get chat data from the Nebula chat hook
  const { chats, isLoading: isLoadingChats } = useNebulaChat();

  // Close sidebar on mobile when screen size changes
  useEffect(() => {
    if (isMobile) {
      setIsSidebarOpen(false);
    } else {
      setIsSidebarOpen(true);
    }
  }, [isMobile]);

  // Set chats in store when data is loaded
  useEffect(() => {
    if (chats && chats.length > 0) {
      setChats(chats);

      // Set first chat as active if no active chat
      if (!activeChat) {
        setActiveChat(chats[0]);
      }
    }
  }, [chats, activeChat, setChats, setActiveChat]);

  // Handle back to welcome - delegate to MainContent
  const handleBackToWelcome = () => {
    if (mainContentRef.current) {
      mainContentRef.current.handleBackToWelcome();
    }
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header with mobile menu button and controls - Height: p-4 (32px) + h-9 content (36px) = 68px = 4.25rem */}
      <header className="fixed top-0 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-border/50 h-[4.25rem]">
        <div className="flex items-center justify-between p-4">
          {/* Left side - Mobile menu button and Logo */}
          <div className="flex items-center gap-3">
            {isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarOpen(true)}
                className="h-9 w-9 p-0 nebula-hover"
                title="Open menu"
              >
                <MenuIcon className="h-4 w-4" />
              </Button>
            )}

            {/* Logo and Title */}
            <div className="flex items-center gap-3">
              <div className="nebula-icon-bg w-8 h-8 flex items-center justify-center">
                <CubeIcon className="text-foreground text-lg" />
              </div>
              <h1 className="text-xl font-bold text-foreground no-underline tracking-wide">
                Web3AI
              </h1>
            </div>
          </div>

          {/* Right side controls */}
          <div className="flex items-center gap-3">
            {/* Theme toggle button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleTheme}
              className="h-9 w-9 p-0 nebula-hover"
              title={
                theme === "dark"
                  ? "Switch to light mode"
                  : "Switch to dark mode"
              }
            >
              {theme === "dark" ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>
            <TopWalletButton />
          </div>
        </div>
      </header>

      {/* Sidebar - Fixed positioned */}
      <Sidebar
        isLoading={isLoadingChats}
        isOpen={isSidebarOpen}
        onClose={() => setIsSidebarOpen(false)}
        onBackToWelcome={handleBackToWelcome}
      />

      {/* Main Layout with header spacing - Page height: 100vh, Header height: 4.25rem, Remaining: calc(100vh - 4.25rem) */}
      <div className="flex flex-col flex-1 overflow-hidden pt-[4.25rem]">
        <MainContent ref={mainContentRef} />
      </div>
    </div>
  );
};

export default LoggedInHome;
