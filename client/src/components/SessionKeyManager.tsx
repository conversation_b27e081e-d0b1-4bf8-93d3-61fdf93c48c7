import { useEffect, useState } from "react";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import { useSessionKeys } from "@/hooks/useSessionKeys";
import { SESSION_KEY_PRESETS } from "@/lib/sessionKeyManager";
import { debugWalletConnection } from "@/lib/thirdweb";

/**
 * Session Key Manager Component
 * Automatically manages session keys for smart accounts
 * Shows session key status and provides manual controls
 */
const SessionKeyManager = () => {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const {
    sessionKey,
    hasSessionKey,
    isCreating,
    isRemoving,
    error,
    isExpired,
    createSessionKey,
    removeSessionKey,
    getFormattedTimeUntilExpiry,
    clearError,
    presets,
  } = useSessionKeys();

  const [autoCreateEnabled, setAutoCreateEnabled] = useState(true);
  const [selectedPreset, setSelectedPreset] = useState<keyof typeof SESSION_KEY_PRESETS>("GENERAL");
  const [isSmartAccount, setIsSmartAccount] = useState(false);

  // Check if connected wallet is a smart account
  useEffect(() => {
    if (wallet && account) {
      const smartAccountDetected = debugWalletConnection(wallet, account);
      setIsSmartAccount(smartAccountDetected);
    } else {
      setIsSmartAccount(false);
    }
  }, [wallet, account]);

  // Auto-create session key when smart account connects (if enabled)
  useEffect(() => {
    if (
      isSmartAccount && 
      account && 
      !hasSessionKey && 
      !isCreating && 
      autoCreateEnabled
    ) {
      console.log("🔄 Auto-creating session key for smart account");
      createSessionKey(selectedPreset);
    }
  }, [isSmartAccount, account, hasSessionKey, isCreating, autoCreateEnabled, selectedPreset, createSessionKey]);

  // Don't render if no account or not a smart account
  if (!account || !isSmartAccount) {
    return null;
  }

  return (
    <div className="p-4 border rounded-lg bg-card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Session Keys</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Auto-create:</span>
          <input
            type="checkbox"
            checked={autoCreateEnabled}
            onChange={(e) => setAutoCreateEnabled(e.target.checked)}
            className="rounded"
          />
        </div>
      </div>

      {/* Session Key Status */}
      <div className="space-y-3">
        {hasSessionKey && sessionKey ? (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-green-800">✅ Session Key Active</div>
                <div className="text-sm text-green-600">
                  Address: {sessionKey.address.slice(0, 6)}...{sessionKey.address.slice(-4)}
                </div>
                <div className="text-sm text-green-600">
                  Expires in: {getFormattedTimeUntilExpiry()}
                </div>
                <div className="text-sm text-green-600">
                  Max per tx: {sessionKey.permissions.nativeTokenLimitPerTransaction} ETH
                </div>
              </div>
              <button
                onClick={removeSessionKey}
                disabled={isRemoving}
                className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
              >
                {isRemoving ? "Removing..." : "Remove"}
              </button>
            </div>
          </div>
        ) : isExpired ? (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="font-medium text-yellow-800">⚠️ Session Key Expired</div>
            <div className="text-sm text-yellow-600">
              Create a new session key to enable automatic transactions
            </div>
          </div>
        ) : (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="font-medium text-blue-800">ℹ️ No Session Key</div>
            <div className="text-sm text-blue-600">
              Create a session key to enable automatic transaction signing
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-red-800">❌ Error</div>
                <div className="text-sm text-red-600">{error}</div>
              </div>
              <button
                onClick={clearError}
                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        {/* Manual Session Key Creation */}
        {!hasSessionKey && (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-2">
                Permission Preset:
              </label>
              <select
                value={selectedPreset}
                onChange={(e) => setSelectedPreset(e.target.value as keyof typeof SESSION_KEY_PRESETS)}
                className="w-full p-2 border rounded-md bg-background"
              >
                {Object.entries(presets).map(([key, preset]) => (
                  <option key={key} value={key}>
                    {preset.name} - {preset.description}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={() => createSessionKey(selectedPreset)}
              disabled={isCreating}
              className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              {isCreating ? "Creating Session Key..." : "Create Session Key"}
            </button>
          </div>
        )}

        {/* Session Key Benefits */}
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm font-medium mb-2">🚀 Session Key Benefits:</div>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Automatic transaction signing (no popups)</li>
            <li>• Faster transaction execution</li>
            <li>• Better user experience</li>
            <li>• Time-limited permissions for security</li>
            <li>• Configurable spending limits</li>
          </ul>
        </div>

        {/* Current Preset Info */}
        {selectedPreset && (
          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-1">
              Selected: {presets[selectedPreset].name}
            </div>
            <div className="text-xs text-blue-600">
              {presets[selectedPreset].description}
            </div>
            <div className="text-xs text-blue-600 mt-1">
              Max per transaction: {presets[selectedPreset].permissions.nativeTokenLimitPerTransaction} ETH
            </div>
            <div className="text-xs text-blue-600">
              Duration: {selectedPreset === "GENERAL" ? "24 hours" : 
                        selectedPreset === "TRANSFERS" ? "7 days" :
                        selectedPreset === "DEFI" ? "12 hours" :
                        selectedPreset === "TESTING" ? "1 hour" : "Custom"}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionKeyManager;
