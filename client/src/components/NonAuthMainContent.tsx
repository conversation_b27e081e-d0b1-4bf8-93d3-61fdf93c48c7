import {
  useConnectModal,
  useActiveAccount,
  useActiveWallet,
} from "thirdweb/react";
import { useTheme } from "@/hooks/use-theme";
import { useEffect } from "react";
import NonAuthWelcomePage from "@/components/NonAuthWelcomePage";
import {
  client,
  wallets,
  chains,
  smartAccountConfig,
  debugWalletConnection,
} from "@/lib/thirdweb";

const NonAuthMainContent = () => {
  const { theme } = useTheme();
  const { connect, isConnecting } = useConnectModal();
  const account = useActiveAccount();
  const wallet = useActiveWallet();

  // Debug wallet connection from suggestion chips/typing flow
  useEffect(() => {
    if (wallet && account) {
      console.log("🔗 NonAuth Flow Connected - Running Debug Check:");
      const isSmartAccount = debugWalletConnection(wallet, account);

      if (!isSmartAccount) {
        console.warn("⚠️ WARNING: NonAuth flow created a regular wallet!");
        console.warn(
          "This should now be fixed with accountAbstraction config."
        );
      } else {
        console.log("✅ SUCCESS: NonAuth flow created a smart account!");
      }
    }
  }, [wallet, account]);

  // Handle sign-in trigger
  const handleSignInTrigger = async (message?: string) => {
    try {
      // Don't store the message - just trigger sign-in
      // The user will need to re-enter their message after signing in

      // Open the connect modal with smart account configuration
      await connect({
        client,
        wallets,
        chains,
        theme,
        accountAbstraction: smartAccountConfig, // 🔧 FIX: Add smart account configuration
        connectModal: {
          title: "Sign In to Web3AI",
          size: "compact",
        },
      });
    } catch (error) {
      console.error("Failed to connect wallet:", error);
    }
  };

  return (
    <main className="flex-1 flex flex-col h-full overflow-hidden bg-background">
      {/* Responsive container with proper mobile spacing */}
      <div className="w-full max-w-4xl mx-auto flex flex-col h-full px-4 sm:px-6 lg:px-8 py-2 sm:py-4">
        <NonAuthWelcomePage
          onSignInTrigger={handleSignInTrigger}
          isConnecting={isConnecting}
        />
      </div>
    </main>
  );
};

export default NonAuthMainContent;
