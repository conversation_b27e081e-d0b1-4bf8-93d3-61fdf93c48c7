import { useEffect } from "react";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import { debugWalletConnection } from "@/lib/thirdweb";

/**
 * Debug component to test wallet connection and smart account creation
 * This component will log detailed information about the connected wallet
 */
const WalletDebugger = () => {
  const account = useActiveAccount();
  const wallet = useActiveWallet();

  useEffect(() => {
    if (wallet && account) {
      console.log("🔍 WALLET DEBUGGER - Full Analysis:");
      console.log("=====================================");
      
      // Run our debug function
      const isSmartAccount = debugWalletConnection(wallet, account);
      
      // Additional detailed checks
      console.log("📋 Additional Details:");
      console.log("- Wallet Object:", wallet);
      console.log("- Account Object:", account);
      console.log("- Wallet Methods:", Object.getOwnPropertyNames(wallet));
      
      // Check if wallet has smart account methods
      const hasSmartAccountMethods = 'getAdminAccount' in wallet || 'getPersonalAccount' in wallet;
      console.log("- Has Smart Account Methods:", hasSmartAccountMethods);
      
      // Check account address pattern (smart accounts often have different patterns)
      const addressPattern = account.address;
      console.log("- Account Address Pattern:", addressPattern);
      
      // Final verdict
      if (isSmartAccount) {
        console.log("✅ VERDICT: This is a SMART ACCOUNT");
        console.log("🎉 Smart account creation is working correctly!");
      } else {
        console.log("❌ VERDICT: This is a REGULAR WALLET");
        console.log("⚠️ Smart account creation is NOT working!");
        console.log("🔧 Check accountAbstraction configuration in ConnectButton");
      }
      
      console.log("=====================================");
    } else if (wallet && !account) {
      console.log("⚠️ Wallet connected but no account found");
    } else if (!wallet && !account) {
      console.log("ℹ️ No wallet connected");
    }
  }, [wallet, account]);

  if (!wallet || !account) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded-lg">
        <h3 className="font-bold text-yellow-800">Wallet Debugger</h3>
        <p className="text-yellow-700">
          {!wallet ? "No wallet connected" : "Wallet connected but no account"}
        </p>
        <p className="text-sm text-yellow-600 mt-2">
          Connect a wallet to see debug information in the console.
        </p>
      </div>
    );
  }

  const config = wallet.getConfig?.();
  const hasSmartAccountFeatures = wallet.id === "smart" || 
    (wallet.id === "inApp" && config && "smartAccount" in config) ||
    (config && "smartAccount" in config);

  return (
    <div className={`p-4 border rounded-lg ${hasSmartAccountFeatures ? 'bg-green-100 border-green-400' : 'bg-red-100 border-red-400'}`}>
      <h3 className="font-bold mb-2">
        {hasSmartAccountFeatures ? '✅ Smart Account Connected' : '❌ Regular Wallet Connected'}
      </h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Wallet ID:</strong> {wallet.id}
        </div>
        <div>
          <strong>Account Address:</strong> {account.address}
        </div>
        <div>
          <strong>Has Smart Account Config:</strong> {config && "smartAccount" in config ? "Yes" : "No"}
        </div>
        <div>
          <strong>Account Type:</strong> {hasSmartAccountFeatures ? "Smart Account" : "Regular Wallet"}
        </div>
      </div>
      
      <div className="mt-3 text-xs text-gray-600">
        Check browser console for detailed debug information.
      </div>
    </div>
  );
};

export default WalletDebugger;
