import { generateAccount } from "thirdweb/wallets";
import { addSessionKey, removeSession<PERSON>ey } from "thirdweb/extensions/erc4337";
import { getContract, sendTransaction } from "thirdweb";
import { client, sepolia } from "@/lib/thirdweb";
import type { Account } from "thirdweb/wallets";
import type { AccountPermissions } from "thirdweb/extensions/erc4337";

/**
 * Session Key Manager for Smart Accounts
 * Handles creation, storage, and management of session keys with permissions
 */

export interface SessionKeyData {
  address: string;
  privateKey: string;
  permissions: AccountPermissions;
  createdAt: Date;
  expiresAt: Date;
  smartAccountAddress: string;
}

export interface SessionKeyPermissionPreset {
  name: string;
  description: string;
  permissions: AccountPermissions;
}

// Predefined permission presets for common use cases
export const SESSION_KEY_PRESETS: Record<string, SessionKeyPermissionPreset> = {
  // General Web3 interactions (24 hours)
  GENERAL: {
    name: "General Web3 Actions",
    description: "Token transfers, swaps, and basic DeFi interactions",
    permissions: {
      approvedTargets: "*", // Allow all contracts
      nativeTokenLimitPerTransaction: "0.1", // 0.1 ETH per transaction
      permissionStartTimestamp: new Date(),
      permissionEndTimestamp: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    },
  },
  
  // Token transfers only (7 days)
  TRANSFERS: {
    name: "Token Transfers",
    description: "Send and receive tokens only",
    permissions: {
      approvedTargets: [
        "******************************************", // USDC
        "******************************************", // USDC Polygon
        // Add more token addresses as needed
      ],
      nativeTokenLimitPerTransaction: "0.05", // 0.05 ETH per transaction
      permissionStartTimestamp: new Date(),
      permissionEndTimestamp: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  },
  
  // DeFi interactions (12 hours)
  DEFI: {
    name: "DeFi Interactions",
    description: "Swaps, liquidity, and DeFi protocols",
    permissions: {
      approvedTargets: "*", // Allow all contracts for DeFi
      nativeTokenLimitPerTransaction: "0.2", // 0.2 ETH per transaction
      permissionStartTimestamp: new Date(),
      permissionEndTimestamp: new Date(Date.now() + 12 * 60 * 60 * 1000), // 12 hours
    },
  },
  
  // Limited testing (1 hour)
  TESTING: {
    name: "Testing & Development",
    description: "Short-term testing with minimal permissions",
    permissions: {
      approvedTargets: "*",
      nativeTokenLimitPerTransaction: "0.01", // 0.01 ETH per transaction
      permissionStartTimestamp: new Date(),
      permissionEndTimestamp: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
    },
  },
};

class SessionKeyManager {
  private static instance: SessionKeyManager;
  private sessionKeys: Map<string, SessionKeyData> = new Map();
  private readonly STORAGE_KEY = "thirdweb_session_keys";

  private constructor() {
    this.loadSessionKeysFromStorage();
  }

  public static getInstance(): SessionKeyManager {
    if (!SessionKeyManager.instance) {
      SessionKeyManager.instance = new SessionKeyManager();
    }
    return SessionKeyManager.instance;
  }

  /**
   * Create a new session key for a smart account
   */
  public async createSessionKey(
    smartAccount: Account,
    preset: keyof typeof SESSION_KEY_PRESETS = "GENERAL"
  ): Promise<SessionKeyData> {
    try {
      console.log("🔑 Creating session key for smart account:", smartAccount.address);
      
      // Generate a new session key account
      const sessionKeyAccount = await generateAccount({ client });
      
      // Get the permission preset
      const permissionPreset = SESSION_KEY_PRESETS[preset];
      if (!permissionPreset) {
        throw new Error(`Unknown session key preset: ${preset}`);
      }

      // Create session key data
      const sessionKeyData: SessionKeyData = {
        address: sessionKeyAccount.address,
        privateKey: sessionKeyAccount.privateKey,
        permissions: permissionPreset.permissions,
        createdAt: new Date(),
        expiresAt: permissionPreset.permissions.permissionEndTimestamp || new Date(Date.now() + 24 * 60 * 60 * 1000),
        smartAccountAddress: smartAccount.address,
      };

      // Get smart account contract
      const smartAccountContract = getContract({
        client,
        address: smartAccount.address,
        chain: sepolia,
      });

      // Add session key to smart account
      const addSessionKeyTx = addSessionKey({
        contract: smartAccountContract,
        account: smartAccount,
        sessionKeyAddress: sessionKeyAccount.address,
        permissions: permissionPreset.permissions,
      });

      // Send the transaction to add session key
      console.log("📝 Adding session key to smart account...");
      await sendTransaction({
        account: smartAccount,
        transaction: addSessionKeyTx,
      });

      // Store session key data
      this.sessionKeys.set(smartAccount.address, sessionKeyData);
      this.saveSessionKeysToStorage();

      console.log("✅ Session key created successfully:", {
        sessionKeyAddress: sessionKeyAccount.address,
        smartAccountAddress: smartAccount.address,
        preset: preset,
        expiresAt: sessionKeyData.expiresAt,
      });

      return sessionKeyData;
    } catch (error) {
      console.error("❌ Failed to create session key:", error);
      throw error;
    }
  }

  /**
   * Get session key for a smart account
   */
  public getSessionKey(smartAccountAddress: string): SessionKeyData | null {
    const sessionKey = this.sessionKeys.get(smartAccountAddress);
    
    // Check if session key exists and is not expired
    if (sessionKey && new Date() < sessionKey.expiresAt) {
      return sessionKey;
    }
    
    // Remove expired session key
    if (sessionKey) {
      this.sessionKeys.delete(smartAccountAddress);
      this.saveSessionKeysToStorage();
    }
    
    return null;
  }

  /**
   * Remove session key from smart account
   */
  public async removeSessionKey(smartAccount: Account): Promise<void> {
    try {
      const sessionKeyData = this.sessionKeys.get(smartAccount.address);
      if (!sessionKeyData) {
        console.log("No session key found for smart account:", smartAccount.address);
        return;
      }

      // Get smart account contract
      const smartAccountContract = getContract({
        client,
        address: smartAccount.address,
        chain: sepolia,
      });

      // Remove session key from smart account
      const removeSessionKeyTx = removeSessionKey({
        contract: smartAccountContract,
        account: smartAccount,
        sessionKeyAddress: sessionKeyData.address,
      });

      // Send the transaction to remove session key
      console.log("🗑️ Removing session key from smart account...");
      await sendTransaction({
        account: smartAccount,
        transaction: removeSessionKeyTx,
      });

      // Remove from local storage
      this.sessionKeys.delete(smartAccount.address);
      this.saveSessionKeysToStorage();

      console.log("✅ Session key removed successfully");
    } catch (error) {
      console.error("❌ Failed to remove session key:", error);
      throw error;
    }
  }

  /**
   * Check if smart account has an active session key
   */
  public hasActiveSessionKey(smartAccountAddress: string): boolean {
    return this.getSessionKey(smartAccountAddress) !== null;
  }

  /**
   * Get all active session keys
   */
  public getAllActiveSessionKeys(): SessionKeyData[] {
    const activeKeys: SessionKeyData[] = [];
    const now = new Date();
    
    for (const [address, sessionKey] of this.sessionKeys.entries()) {
      if (now < sessionKey.expiresAt) {
        activeKeys.push(sessionKey);
      } else {
        // Remove expired keys
        this.sessionKeys.delete(address);
      }
    }
    
    this.saveSessionKeysToStorage();
    return activeKeys;
  }

  /**
   * Clear all session keys
   */
  public clearAllSessionKeys(): void {
    this.sessionKeys.clear();
    localStorage.removeItem(this.STORAGE_KEY);
    console.log("🧹 All session keys cleared");
  }

  /**
   * Load session keys from localStorage
   */
  private loadSessionKeysFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        for (const [address, sessionKeyData] of Object.entries(data)) {
          // Convert date strings back to Date objects
          const sessionKey = sessionKeyData as any;
          sessionKey.createdAt = new Date(sessionKey.createdAt);
          sessionKey.expiresAt = new Date(sessionKey.expiresAt);
          sessionKey.permissions.permissionStartTimestamp = new Date(sessionKey.permissions.permissionStartTimestamp);
          sessionKey.permissions.permissionEndTimestamp = new Date(sessionKey.permissions.permissionEndTimestamp);
          
          this.sessionKeys.set(address, sessionKey as SessionKeyData);
        }
        console.log("📂 Loaded session keys from storage:", this.sessionKeys.size);
      }
    } catch (error) {
      console.error("Failed to load session keys from storage:", error);
    }
  }

  /**
   * Save session keys to localStorage
   */
  private saveSessionKeysToStorage(): void {
    try {
      const data = Object.fromEntries(this.sessionKeys);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error("Failed to save session keys to storage:", error);
    }
  }
}

// Export singleton instance
export const sessionKeyManager = SessionKeyManager.getInstance();

// Export types and presets
export { SESSION_KEY_PRESETS };
export type { SessionKeyData, SessionKeyPermissionPreset };
